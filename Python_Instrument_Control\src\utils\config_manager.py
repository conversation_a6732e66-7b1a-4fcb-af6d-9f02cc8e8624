"""
Configuration management for instrument control application
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional
from .logger import get_logger

class ConfigManager:
    """Manages application configuration"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = get_logger("ConfigManager")
        
        # Default config file path
        if config_file is None:
            config_dir = Path(__file__).parent.parent.parent / "config"
            config_dir.mkdir(exist_ok=True)
            config_file = config_dir / "config.yaml"
        
        self.config_file = Path(config_file)
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file"""
        
        if not self.config_file.exists():
            self.logger.info(f"Config file not found, creating default: {self.config_file}")
            return self._create_default_config()
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.json':
                    config = json.load(f)
                else:
                    config = yaml.safe_load(f)
            
            self.logger.info(f"Loaded configuration from {self.config_file}")
            return config or {}
            
        except Exception as e:
            self.logger.error(f"Error loading config: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration"""
        
        default_config = {
            "application": {
                "name": "Python Instrument Control",
                "version": "1.0.0",
                "debug": False
            },
            "gui": {
                "theme": "dark",
                "window_size": [1200, 800],
                "window_position": [100, 100]
            },
            "instruments": {
                "auto_connect": False,
                "connection_timeout": 5.0,
                "default_settings": {
                    "serial": {
                        "baudrate": 9600,
                        "timeout": 1.0,
                        "termination": "\r\n"
                    }
                }
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 10
            },
            "data": {
                "auto_save": True,
                "save_format": "csv",
                "data_directory": "data"
            }
        }
        
        self.save_config(default_config)
        return default_config
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value using dot notation"""
        
        keys = key.split('.')
        config = self.config
        
        # Navigate to the parent of the target key
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # Set the value
        config[keys[-1]] = value
        self.logger.debug(f"Set config {key} = {value}")
    
    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """Save configuration to file"""
        
        if config is not None:
            self.config = config
        
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_file.suffix.lower() == '.json':
                    json.dump(self.config, f, indent=2)
                else:
                    yaml.dump(self.config, f, default_flow_style=False, indent=2)
            
            self.logger.info(f"Saved configuration to {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            return False
    
    def reload(self) -> None:
        """Reload configuration from file"""
        self.config = self._load_config()
        self.logger.info("Configuration reloaded")
    
    def get_instrument_config(self, instrument_name: str) -> Dict[str, Any]:
        """Get configuration for specific instrument"""
        return self.get(f"instruments.{instrument_name}", {})
