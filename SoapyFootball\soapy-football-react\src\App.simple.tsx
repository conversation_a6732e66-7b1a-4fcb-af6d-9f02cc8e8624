import React from 'react';
import './App.css';

function App() {
  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb' }}>
      <header style={{ backgroundColor: 'white', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '1rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#0ea5e9' }}>Soapy Football</div>
          <nav>
            <ul style={{ display: 'flex', gap: '1rem', listStyle: 'none', margin: 0, padding: 0 }}>
              <li><a href="#" style={{ color: '#374151', textDecoration: 'none' }}>Home</a></li>
              <li><a href="#" style={{ color: '#374151', textDecoration: 'none' }}>Book Now</a></li>
              <li><a href="#" style={{ color: '#374151', textDecoration: 'none' }}>My Bookings</a></li>
              <li><a href="#" style={{ backgroundColor: '#0ea5e9', color: 'white', padding: '0.5rem 1rem', borderRadius: '0.375rem', textDecoration: 'none' }}>Sign In</a></li>
            </ul>
          </nav>
        </div>
      </header>

      <main>
        <section style={{ padding: '4rem 0', background: 'linear-gradient(to bottom, rgba(14, 165, 233, 0.1), white)' }}>
          <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem' }}>
            <div style={{ maxWidth: '800px', margin: '0 auto', textAlign: 'center' }}>
              <h1 style={{ fontSize: '2.5rem', fontWeight: 'bold', marginBottom: '1.5rem' }}>
                <span style={{ color: '#0ea5e9' }}>Soapy Football</span> Turf Booking
              </h1>
              <p style={{ fontSize: '1.25rem', color: '#4b5563', marginBottom: '2rem' }}>
                Experience the thrill of playing football on our premium soapy turf.
                Perfect for friendly matches, corporate events, and weekend fun with friends and family.
                Book your slot today and enjoy a unique football experience!
              </p>
              <button style={{ backgroundColor: '#0ea5e9', color: 'white', padding: '0.75rem 2rem', borderRadius: '0.375rem', fontWeight: '500', border: 'none', cursor: 'pointer', boxShadow: '0 4px 6px rgba(0,0,0,0.1)' }}>
                Book Now
              </button>
            </div>
          </div>
        </section>
      </main>

      <footer style={{ backgroundColor: '#1e293b', color: 'white', padding: '2rem 0' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 1rem', textAlign: 'center' }}>
          <p>&copy; {new Date().getFullYear()} Soapy Football. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}

export default App;
