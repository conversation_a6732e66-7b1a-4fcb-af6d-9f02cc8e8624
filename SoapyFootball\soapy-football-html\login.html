<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Soapy Football</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #0284c7;
        }

        .btn-secondary {
            background-color: #10b981;
        }

        .btn-secondary:hover {
            background-color: #059669;
        }

        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #0ea5e9;
        }

        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Login section */
        .login-section {
            padding: 80px 0;
            flex-grow: 1;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2rem;
            color: #1e293b;
        }

        .login-container {
            max-width: 400px;
            margin: 0 auto;
        }

        .login-card {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e293b;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Inter', sans-serif;
        }

        .form-group input:focus {
            outline: none;
            border-color: #0ea5e9;
        }

        .login-btn {
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
            margin-top: 10px;
        }

        .auth-links {
            margin-top: 20px;
            text-align: center;
        }

        .auth-links a {
            color: #0ea5e9;
            text-decoration: none;
        }

        .auth-links a:hover {
            text-decoration: underline;
        }

        .auth-separator {
            display: flex;
            align-items: center;
            margin: 20px 0;
        }

        .auth-separator::before,
        .auth-separator::after {
            content: '';
            flex: 1;
            border-bottom: 1px solid #e5e7eb;
        }

        .auth-separator span {
            padding: 0 10px;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .social-login {
            display: flex;
            gap: 10px;
        }

        .social-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background-color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .social-btn:hover {
            background-color: #f3f4f6;
        }

        .social-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        /* Tabs */
        .auth-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .auth-tab {
            flex: 1;
            text-align: center;
            padding: 10px;
            cursor: pointer;
            color: #6b7280;
            font-weight: 500;
            transition: all 0.3s;
        }

        .auth-tab.active {
            color: #0ea5e9;
            border-bottom: 2px solid #0ea5e9;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .footer-col ul {
            list-style: none;
        }

        .footer-col ul li {
            margin-bottom: 10px;
        }

        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-col a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            nav ul.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">Soapy Football</a>
            <button class="mobile-menu-btn">☰</button>
            <nav>
                <ul id="menu">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="history.html">My Bookings</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="booking.html" class="nav-btn">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="login-section">
        <div class="container">
            <h2 class="section-title">Account Access</h2>
            <div class="login-container">
                <div class="login-card">
                    <div class="auth-tabs">
                        <div class="auth-tab active" data-tab="login">Login</div>
                        <div class="auth-tab" data-tab="signup">Sign Up</div>
                    </div>

                    <div class="tab-content active" id="login-tab">
                        <form id="login-form">
                            <div class="form-group">
                                <label for="login-email">Email</label>
                                <input type="email" id="login-email" placeholder="Enter your email" required>
                            </div>

                            <div class="form-group">
                                <label for="login-password">Password</label>
                                <input type="password" id="login-password" placeholder="Enter your password" required>
                            </div>

                            <button type="submit" class="btn login-btn">Login</button>

                            <div class="auth-links">
                                <a href="#">Forgot password?</a>
                            </div>
                        </form>

                        <div class="auth-separator">
                            <span>OR</span>
                        </div>

                        <div class="social-login">
                            <button class="social-btn">
                                <span class="social-icon">G</span> Google
                            </button>
                            <button class="social-btn">
                                <span class="social-icon">f</span> Facebook
                            </button>
                        </div>
                    </div>

                    <div class="tab-content" id="signup-tab">
                        <form id="signup-form">
                            <div class="form-group">
                                <label for="signup-name">Full Name</label>
                                <input type="text" id="signup-name" placeholder="Enter your full name" required>
                            </div>

                            <div class="form-group">
                                <label for="signup-email">Email</label>
                                <input type="email" id="signup-email" placeholder="Enter your email" required>
                            </div>

                            <div class="form-group">
                                <label for="signup-password">Password</label>
                                <input type="password" id="signup-password" placeholder="Create a password" required>
                            </div>

                            <div class="form-group">
                                <label for="signup-confirm-password">Confirm Password</label>
                                <input type="password" id="signup-confirm-password" placeholder="Confirm your password" required>
                            </div>

                            <button type="submit" class="btn login-btn">Sign Up</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best turf booking platform for football enthusiasts.</p>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9876543210</li>
                        <li>Address: 123 Sports Complex, Mumbai</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const menu = document.getElementById('menu');

        mobileMenuBtn.addEventListener('click', () => {
            menu.classList.toggle('active');
        });

        // Auth tabs
        const authTabs = document.querySelectorAll('.auth-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        authTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                authTabs.forEach(t => t.classList.remove('active'));

                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all tab contents
                tabContents.forEach(content => content.classList.remove('active'));

                // Show the corresponding tab content
                const tabId = tab.dataset.tab + '-tab';
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Login form submission
        const loginForm = document.getElementById('login-form');

        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            // Simulate login (in a real app, this would make an API call)
            console.log('Login attempt:', { email, password });

            // Redirect to home page after successful login
            alert('Login successful!');
            window.location.href = 'index.html';
        });

        // Signup form submission
        const signupForm = document.getElementById('signup-form');

        signupForm.addEventListener('submit', (e) => {
            e.preventDefault();

            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('signup-confirm-password').value;

            // Check if passwords match
            if (password !== confirmPassword) {
                alert('Passwords do not match!');
                return;
            }

            // Simulate signup (in a real app, this would make an API call)
            console.log('Signup attempt:', { name, email, password });

            // Redirect to home page after successful signup
            alert('Account created successfully!');
            window.location.href = 'index.html';
        });
    </script>
</body>
</html>
