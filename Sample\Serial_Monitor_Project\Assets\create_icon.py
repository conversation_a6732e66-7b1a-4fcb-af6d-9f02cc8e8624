from PIL import Image, ImageDraw
import os

def create_serial_icon():
    # Create a 256x256 image with transparent background
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Define colors matching our purple theme
    purple_main = (139, 95, 191)  # #8b5fbf
    purple_dark = (122, 79, 179)  # #7a4fb3
    purple_border = (111, 76, 155)  # #6f4c9b
    white = (255, 255, 255)
    
    # Draw main connector body (USB/Serial connector shape)
    connector_width = 180
    connector_height = 120
    connector_x = (size - connector_width) // 2
    connector_y = (size - connector_height) // 2
    
    # Main connector rectangle with rounded corners
    draw.rounded_rectangle(
        [connector_x, connector_y, connector_x + connector_width, connector_y + connector_height],
        radius=15,
        fill=purple_main,
        outline=purple_border,
        width=4
    )
    
    # Draw connector pins/contacts
    pin_width = 12
    pin_height = 8
    pin_spacing = 20
    pins_start_x = connector_x + 30
    pins_y = connector_y + connector_height // 2 - pin_height // 2
    
    # Draw 4 pins
    for i in range(4):
        pin_x = pins_start_x + i * pin_spacing
        draw.rectangle(
            [pin_x, pins_y, pin_x + pin_width, pins_y + pin_height],
            fill=white
        )
    
    # Draw cable coming out
    cable_width = 20
    cable_length = 60
    cable_x = connector_x + connector_width
    cable_y = connector_y + connector_height // 2 - cable_width // 2
    
    draw.rectangle(
        [cable_x, cable_y, cable_x + cable_length, cable_y + cable_width],
        fill=purple_dark
    )
    
    # Draw data flow lines (representing serial communication)
    line_y_start = connector_y - 30
    line_y_spacing = 15
    line_length = 40
    line_x = connector_x + connector_width // 2 - line_length // 2
    
    for i in range(3):
        y = line_y_start + i * line_y_spacing
        # Draw arrow-like lines
        draw.line([line_x, y, line_x + line_length, y], fill=purple_main, width=3)
        # Arrow head
        draw.line([line_x + line_length - 8, y - 4, line_x + line_length, y], fill=purple_main, width=3)
        draw.line([line_x + line_length - 8, y + 4, line_x + line_length, y], fill=purple_main, width=3)
    
    # Add small circuit pattern
    circuit_size = 8
    circuit_x = connector_x + 20
    circuit_y = connector_y + 20
    
    # Draw small circuit traces
    draw.rectangle([circuit_x, circuit_y, circuit_x + circuit_size, circuit_y + 2], fill=white)
    draw.rectangle([circuit_x, circuit_y + 4, circuit_x + circuit_size, circuit_y + 6], fill=white)
    draw.rectangle([circuit_x + circuit_size + 5, circuit_y, circuit_x + circuit_size + 5 + circuit_size, circuit_y + 2], fill=white)
    draw.rectangle([circuit_x + circuit_size + 5, circuit_y + 4, circuit_x + circuit_size + 5 + circuit_size, circuit_y + 6], fill=white)
    
    return img

def create_multiple_sizes():
    # Create the main icon
    main_icon = create_serial_icon()
    
    # Create different sizes for ICO file
    sizes = [16, 32, 48, 64, 128, 256]
    images = []
    
    for size in sizes:
        resized = main_icon.resize((size, size), Image.Resampling.LANCZOS)
        images.append(resized)
    
    # Save as ICO file
    main_icon.save('serial_monitor_icon.ico', format='ICO', sizes=[(img.width, img.height) for img in images])
    print("Icon created successfully: serial_monitor_icon.ico")
    
    # Also save as PNG for preview
    main_icon.save('serial_monitor_icon.png', format='PNG')
    print("PNG preview created: serial_monitor_icon.png")

if __name__ == "__main__":
    create_multiple_sizes()
