"""
VISA Resource Manager for Lab Instruments
Handles VISA connections, resource discovery, and communication
"""

import pyvisa
import logging
import time
from typing import Dict, List, Optional, Any
import yaml
from pathlib import Path

logger = logging.getLogger(__name__)


class VISAManager:
    """Manages VISA resources and connections for lab instruments"""
    
    def __init__(self, config_path: str = None):
        """
        Initialize VISA Manager
        
        Args:
            config_path: Path to instruments configuration file
        """
        self.rm = None
        self.connected_instruments = {}
        self.config = {}
        self.config_path = config_path or "config/instruments.yaml"
        
        self._initialize_visa()
        self._load_config()
    
    def _initialize_visa(self):
        """Initialize VISA resource manager"""
        try:
            # Try NI-VISA first, fallback to pyvisa-py
            try:
                self.rm = pyvisa.ResourceManager()
                logger.info("Using NI-VISA backend")
            except:
                self.rm = pyvisa.ResourceManager('@py')
                logger.info("Using pyvisa-py backend")
                
        except Exception as e:
            logger.error(f"Failed to initialize VISA: {e}")
            raise
    
    def _load_config(self):
        """Load instrument configuration from YAML file"""
        try:
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r') as f:
                    self.config = yaml.safe_load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                logger.warning(f"Configuration file not found: {self.config_path}")
                self.config = {}
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            self.config = {}
    
    def scan_resources(self) -> List[str]:
        """
        Scan for available VISA resources
        
        Returns:
            List of available VISA resource strings
        """
        try:
            resources = self.rm.list_resources()
            logger.info(f"Found {len(resources)} VISA resources: {list(resources)}")
            return list(resources)
        except Exception as e:
            logger.error(f"Failed to scan resources: {e}")
            return []
    
    def connect_instrument(self, instrument_id: str, visa_address: str = None) -> Optional[pyvisa.Resource]:
        """
        Connect to an instrument
        
        Args:
            instrument_id: Unique identifier for the instrument
            visa_address: VISA address (optional, will use config if not provided)
            
        Returns:
            VISA resource object or None if connection failed
        """
        try:
            # Get VISA address from config if not provided
            if not visa_address:
                visa_address = self._get_visa_address_from_config(instrument_id)
                if not visa_address:
                    logger.error(f"No VISA address found for instrument: {instrument_id}")
                    return None
            
            # Check if already connected
            if instrument_id in self.connected_instruments:
                logger.info(f"Instrument {instrument_id} already connected")
                return self.connected_instruments[instrument_id]
            
            # Connect to instrument
            instrument = self.rm.open_resource(visa_address)
            
            # Configure timeout and termination
            config = self._get_instrument_config(instrument_id)
            if config:
                instrument.timeout = config.get('timeout', 5000)
                if 'read_termination' in config:
                    instrument.read_termination = config['read_termination']
                if 'write_termination' in config:
                    instrument.write_termination = config['write_termination']
            
            # Test connection
            try:
                idn = instrument.query('*IDN?')
                logger.info(f"Connected to {instrument_id}: {idn.strip()}")
            except:
                logger.warning(f"Connected to {instrument_id} but *IDN? failed")
            
            self.connected_instruments[instrument_id] = instrument
            return instrument
            
        except Exception as e:
            logger.error(f"Failed to connect to {instrument_id} at {visa_address}: {e}")
            return None
    
    def disconnect_instrument(self, instrument_id: str) -> bool:
        """
        Disconnect an instrument
        
        Args:
            instrument_id: Unique identifier for the instrument
            
        Returns:
            True if successfully disconnected
        """
        try:
            if instrument_id in self.connected_instruments:
                self.connected_instruments[instrument_id].close()
                del self.connected_instruments[instrument_id]
                logger.info(f"Disconnected instrument: {instrument_id}")
                return True
            else:
                logger.warning(f"Instrument not connected: {instrument_id}")
                return False
        except Exception as e:
            logger.error(f"Failed to disconnect {instrument_id}: {e}")
            return False
    
    def disconnect_all(self):
        """Disconnect all connected instruments"""
        for instrument_id in list(self.connected_instruments.keys()):
            self.disconnect_instrument(instrument_id)
    
    def get_instrument(self, instrument_id: str) -> Optional[pyvisa.Resource]:
        """
        Get connected instrument resource
        
        Args:
            instrument_id: Unique identifier for the instrument
            
        Returns:
            VISA resource object or None if not connected
        """
        return self.connected_instruments.get(instrument_id)
    
    def is_connected(self, instrument_id: str) -> bool:
        """
        Check if instrument is connected
        
        Args:
            instrument_id: Unique identifier for the instrument
            
        Returns:
            True if connected
        """
        return instrument_id in self.connected_instruments
    
    def get_connected_instruments(self) -> List[str]:
        """
        Get list of connected instrument IDs
        
        Returns:
            List of connected instrument IDs
        """
        return list(self.connected_instruments.keys())
    
    def _get_visa_address_from_config(self, instrument_id: str) -> Optional[str]:
        """Get VISA address from configuration"""
        try:
            # Search through all instrument categories
            for category in self.config.get('instruments', {}).values():
                if isinstance(category, dict) and instrument_id in category:
                    return category[instrument_id].get('visa_address')
            return None
        except Exception as e:
            logger.error(f"Error getting VISA address for {instrument_id}: {e}")
            return None
    
    def _get_instrument_config(self, instrument_id: str) -> Optional[Dict]:
        """Get instrument configuration"""
        try:
            # Search through all instrument categories
            for category in self.config.get('instruments', {}).values():
                if isinstance(category, dict) and instrument_id in category:
                    return category[instrument_id]
            return None
        except Exception as e:
            logger.error(f"Error getting config for {instrument_id}: {e}")
            return None
    
    def get_instrument_list(self) -> Dict[str, Dict]:
        """
        Get list of all configured instruments
        
        Returns:
            Dictionary of instrument configurations
        """
        instruments = {}
        try:
            for category_name, category in self.config.get('instruments', {}).items():
                if isinstance(category, dict):
                    for inst_id, inst_config in category.items():
                        instruments[inst_id] = {
                            'category': category_name,
                            'config': inst_config
                        }
        except Exception as e:
            logger.error(f"Error getting instrument list: {e}")
        
        return instruments
    
    def __del__(self):
        """Cleanup on destruction"""
        try:
            self.disconnect_all()
            if self.rm:
                self.rm.close()
        except:
            pass
