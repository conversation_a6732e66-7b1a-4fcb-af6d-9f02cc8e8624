"""
Create distribution packages for Serial Monitor
"""
import os
import shutil
import zipfile
from datetime import datetime

def create_zip_package():
    """Create a ZIP package for distribution"""
    
    print("Creating distribution ZIP package...")
    
    # Create timestamp for version
    timestamp = datetime.now().strftime("%Y%m%d")
    zip_filename = f"SerialMonitor_v1.0_{timestamp}.zip"
    
    # Create ZIP file
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        
        # Add installer files
        installer_dir = "SerialMonitor_Installer"
        if os.path.exists(installer_dir):
            for root, dirs, files in os.walk(installer_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, ".")
                    zipf.write(file_path, arc_path)
                    print(f"  Added: {arc_path}")
        
        # Add standalone exe
        if os.path.exists("dist/SerialMonitor.exe"):
            zipf.write("dist/SerialMonitor.exe", "Standalone/SerialMonitor.exe")
            print("  Added: Standalone/SerialMonitor.exe")
        
        # Add documentation
        docs = ["INSTALLATION_GUIDE.md", "serial_monitor_icon.png"]
        for doc in docs:
            if os.path.exists(doc):
                zipf.write(doc, f"Documentation/{doc}")
                print(f"  Added: Documentation/{doc}")
    
    # Get file size
    size_mb = os.path.getsize(zip_filename) / (1024 * 1024)
    
    print(f"\nZIP package created: {zip_filename}")
    print(f"Size: {size_mb:.1f} MB")
    
    return zip_filename

def create_readme():
    """Create a distribution README"""
    
    readme_content = f"""# Serial Monitor - Distribution Package

## Package Contents

This package contains multiple installation options for Serial Monitor:

### 1. Professional Installer (Recommended)
**Location**: `SerialMonitor_Installer/`

**Installation**:
1. Extract this ZIP file
2. Navigate to `SerialMonitor_Installer` folder
3. Right-click `Setup.bat`
4. Select "Run as administrator"
5. Follow the installation wizard

**Features**:
- Installs to Program Files
- Creates Start Menu shortcuts
- Adds to Add/Remove Programs
- Optional desktop shortcut
- Proper uninstaller

### 2. Standalone Executable
**Location**: `Standalone/SerialMonitor.exe`

**Usage**:
- No installation required
- Just run the exe file
- Portable - can run from USB drive
- Perfect for temporary use

### 3. Documentation
**Location**: `Documentation/`

Contains:
- Installation guide
- Application icon
- User documentation

## System Requirements

- Windows 10/11 (64-bit)
- Administrator privileges (for installer)
- 50MB free disk space
- COM port access permissions

## Application Features

- **Beautiful Purple Theme**: Modern dark interface
- **COM Port Auto-Detection**: Finds available ports automatically
- **Multiple Baud Rates**: 9600 to 921600 baud support
- **Real-time Communication**: Send/receive serial data
- **Arduino IDE Style**: Familiar serial monitor interface
- **Professional Vector Icons**: Custom serial port icons
- **Clean Interface**: White status text for visibility

## Quick Start

1. **For permanent installation**: Use the Professional Installer
2. **For quick testing**: Use the Standalone executable
3. **Connect your device**: Select COM port and baud rate
4. **Start communicating**: Send commands and monitor responses

## Support

- Check the documentation folder for detailed guides
- Ensure device drivers are installed
- Run as administrator if COM port access fails

---
Serial Monitor v1.0 - Professional Serial Communication Tool
Created: {datetime.now().strftime("%Y-%m-%d")}
"""
    
    with open("DISTRIBUTION_README.txt", "w", encoding='utf-8') as f:
        f.write(readme_content)
    
    print("Distribution README created: DISTRIBUTION_README.txt")

def main():
    """Main function"""
    
    print("Serial Monitor - Distribution Package Creator")
    print("=" * 50)
    
    # Create README
    create_readme()
    
    # Create ZIP package
    zip_file = create_zip_package()
    
    print("\n" + "=" * 50)
    print("DISTRIBUTION PACKAGE READY!")
    print("=" * 50)
    print(f"\nPackage: {zip_file}")
    print("\nThis ZIP file contains:")
    print("  [+] Professional Windows installer")
    print("  [+] Standalone portable executable")
    print("  [+] Complete documentation")
    print("  [+] Installation guides")
    print("\nYou can now distribute this ZIP file to install")
    print("Serial Monitor on any Windows PC!")

if __name__ == "__main__":
    main()
