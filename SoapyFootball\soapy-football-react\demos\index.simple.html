<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football</title>
    <style>
        body {
            font-family: 'Inter', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9fafb;
            color: #1e293b;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .hero {
            background: linear-gradient(to bottom, rgba(14, 165, 233, 0.1), white);
            padding: 4rem 0;
            text-align: center;
        }
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        .hero h1 span {
            color: #0ea5e9;
        }
        .hero p {
            font-size: 1.25rem;
            color: #64748b;
            max-width: 800px;
            margin: 0 auto 2rem;
        }
        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            transition: background-color 0.2s;
        }
        .btn:hover {
            background-color: #0284c7;
        }
        .features {
            padding: 4rem 0;
            background-color: white;
        }
        .features h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 3rem;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        .feature-card {
            background-color: #f9fafb;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .feature-card p {
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="hero">
        <div class="container">
            <h1><span>Soapy Football</span> Turf Booking</h1>
            <p>Experience the thrill of playing football on our premium soapy turf. Perfect for friendly matches, corporate events, and weekend fun with friends and family. Book your slot today and enjoy a unique football experience!</p>
            <a href="#" class="btn">Book Now</a>
        </div>
    </div>

    <div class="features">
        <div class="container">
            <h2>Why Choose Soapy Football?</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>Unique Experience</h3>
                    <p>Enjoy football with a twist! Our soapy turf adds an exciting challenge to the game.</p>
                </div>
                <div class="feature-card">
                    <h3>Convenient Booking</h3>
                    <p>Easy online booking system with flexible time slots from 7 AM to 10 PM.</p>
                </div>
                <div class="feature-card">
                    <h3>Secure Payments</h3>
                    <p>Hassle-free booking with secure online payments through Razorpay.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
