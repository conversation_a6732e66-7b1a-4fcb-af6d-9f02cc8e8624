import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Testimonial {
  id: number;
  name: string;
  role: string;
  content: string;
  avatar: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    role: 'Football Enthusiast',
    content: 'Soapy Football is such a unique experience! The slippery turf adds a whole new dimension to the game. My friends and I had a blast trying to stay on our feet while playing.',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    role: 'Corporate Event Manager',
    content: 'We booked Soapy Football for our company team building event and it was a huge hit! Everyone was laughing and having a great time. The booking process was smooth and the staff was very helpful.',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
  },
  {
    id: 3,
    name: '<PERSON><PERSON><PERSON>',
    role: 'College Student',
    content: 'My college friends and I play here every weekend. It\'s become our favorite hangout spot. The soapy turf makes even the most serious football players slip and slide, which makes the game so much more fun!',
    avatar: 'https://randomuser.me/api/portraits/men/67.jpg'
  },
  {
    id: 4,
    name: '<PERSON><PERSON>',
    role: 'Birthday Party Host',
    content: 'I booked Soapy Football for my son\'s 16th birthday and it was the best decision ever! All the teenagers had an amazing time and they can\'t stop talking about when they can go back.',
    avatar: 'https://randomuser.me/api/portraits/women/63.jpg'
  }
];

const Testimonials = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, []);
  
  const handleDotClick = (index: number) => {
    setCurrentIndex(index);
  };
  
  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-dark mb-4">What Our Customers Say</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Don't just take our word for it. Here's what people who have experienced Soapy Football have to say.
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="relative h-80 sm:h-64">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <div className="bg-white rounded-lg shadow-md p-8 h-full flex flex-col justify-between">
                  <div>
                    <p className="text-gray-600 italic mb-6">"{testimonials[currentIndex].content}"</p>
                  </div>
                  
                  <div className="flex items-center">
                    <img
                      src={testimonials[currentIndex].avatar}
                      alt={testimonials[currentIndex].name}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <h4 className="font-semibold text-dark">{testimonials[currentIndex].name}</h4>
                      <p className="text-gray-500 text-sm">{testimonials[currentIndex].role}</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
          
          <div className="flex justify-center mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => handleDotClick(index)}
                className={`w-3 h-3 mx-1 rounded-full ${
                  index === currentIndex ? 'bg-primary' : 'bg-gray-300'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
