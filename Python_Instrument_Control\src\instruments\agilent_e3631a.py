"""
Agilent E3631A Triple Output Power Supply Driver
Channel 1: 0-6V, 0-5A (30W)
Channel 2: 0-25V, 0-1A (25W) 
Channel 3: 0-25V, 0-1A (25W)
"""

from typing import Dict, Any, List, Optional
import time
from .base_instrument import BaseInstrument


class AgilentE3631A(BaseInstrument):
    """Driver for Agilent E3631A Triple Output Power Supply"""
    
    def __init__(self, name: str = "E3631A", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        
        # Channel specifications
        self.channel_specs = {
            1: {'voltage_max': 6.0, 'current_max': 5.0, 'power_max': 30.0},
            2: {'voltage_max': 25.0, 'current_max': 1.0, 'power_max': 25.0},
            3: {'voltage_max': 25.0, 'current_max': 1.0, 'power_max': 25.0}
        }
        
        # Channel settings
        self.channel_settings = {}
        for ch in range(1, 4):
            self.channel_settings[ch] = {
                'voltage': 0.0,
                'current': 0.0,
                'enabled': False,
                'ovp': self.channel_specs[ch]['voltage_max'],
                'ocp': self.channel_specs[ch]['current_max']
            }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get power supply capabilities"""
        return {
            'type': 'power_supply',
            'channels': 3,
            'channel_specs': self.channel_specs,
            'features': [
                'Independent channel control',
                'Over-voltage protection',
                'Over-current protection',
                'Remote sensing',
                'Tracking modes'
            ]
        }
    
    def initialize(self) -> bool:
        """Initialize power supply to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(2)
            
            # Turn off all outputs
            for ch in range(1, 4):
                self.set_channel_enabled(ch, False)
                self.set_voltage(ch, 0.0)
                self.set_current(ch, 0.1)  # Set minimum current limit
            
            # Clear any errors
            self.write("*CLS")
            
            self.logger.info("Agilent E3631A initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Agilent E3631A: {e}")
            return False
    
    # Channel Selection
    def select_channel(self, channel: int) -> bool:
        """Select active channel for subsequent commands"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        channel_map = {1: "P6V", 2: "P25V", 3: "N25V"}
        return self.write(f"INST {channel_map[channel]}")
    
    # Voltage Control
    def set_voltage(self, channel: int, voltage: float) -> bool:
        """Set voltage for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        max_voltage = self.channel_specs[channel]['voltage_max']
        if not 0 <= voltage <= max_voltage:
            raise ValueError(f"Voltage must be 0-{max_voltage}V for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f"VOLT {voltage}")
        if success:
            self.channel_settings[channel]['voltage'] = voltage
        return success
    
    def get_voltage_setpoint(self, channel: int) -> float:
        """Get voltage setpoint for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("VOLT?")
        return float(response)
    
    def measure_voltage(self, channel: int) -> float:
        """Measure actual voltage for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("MEAS:VOLT?")
        return float(response)
    
    # Current Control
    def set_current(self, channel: int, current: float) -> bool:
        """Set current limit for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        max_current = self.channel_specs[channel]['current_max']
        if not 0 <= current <= max_current:
            raise ValueError(f"Current must be 0-{max_current}A for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f"CURR {current}")
        if success:
            self.channel_settings[channel]['current'] = current
        return success
    
    def get_current_setpoint(self, channel: int) -> float:
        """Get current setpoint for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("CURR?")
        return float(response)
    
    def measure_current(self, channel: int) -> float:
        """Measure actual current for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("MEAS:CURR?")
        return float(response)
    
    # Output Control
    def set_channel_enabled(self, channel: int, enabled: bool) -> bool:
        """Enable or disable output for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        state = "ON" if enabled else "OFF"
        success = self.write(f"OUTP {state}")
        if success:
            self.channel_settings[channel]['enabled'] = enabled
        return success
    
    def get_channel_enabled(self, channel: int) -> bool:
        """Get output state for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("OUTP?")
        return response.strip() == "1"
    
    def set_all_outputs_enabled(self, enabled: bool) -> bool:
        """Enable or disable all outputs"""
        success = True
        for ch in range(1, 4):
            success &= self.set_channel_enabled(ch, enabled)
        return success
    
    # Protection Settings
    def set_ovp(self, channel: int, voltage: float) -> bool:
        """Set over-voltage protection for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        max_voltage = self.channel_specs[channel]['voltage_max']
        if not 0 <= voltage <= max_voltage * 1.1:  # Allow 10% over max
            raise ValueError(f"OVP must be 0-{max_voltage * 1.1}V for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f"VOLT:PROT {voltage}")
        if success:
            self.channel_settings[channel]['ovp'] = voltage
        return success
    
    def get_ovp(self, channel: int) -> float:
        """Get over-voltage protection setting"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("VOLT:PROT?")
        return float(response)
    
    def set_ocp(self, channel: int, current: float) -> bool:
        """Set over-current protection for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        max_current = self.channel_specs[channel]['current_max']
        if not 0 <= current <= max_current * 1.1:  # Allow 10% over max
            raise ValueError(f"OCP must be 0-{max_current * 1.1}A for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f"CURR:PROT {current}")
        if success:
            self.channel_settings[channel]['ocp'] = current
        return success
    
    def get_ocp(self, channel: int) -> float:
        """Get over-current protection setting"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        self.select_channel(channel)
        response = self.query("CURR:PROT?")
        return float(response)
    
    # Measurement Methods
    def measure_power(self, channel: int) -> float:
        """Calculate power for specified channel"""
        voltage = self.measure_voltage(channel)
        current = self.measure_current(channel)
        return voltage * current
    
    def get_all_measurements(self, channel: int) -> Dict[str, float]:
        """Get all measurements for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        try:
            measurements = {
                'voltage': self.measure_voltage(channel),
                'current': self.measure_current(channel)
            }
            measurements['power'] = measurements['voltage'] * measurements['current']
            return measurements
        except Exception as e:
            self.logger.error(f"Failed to get measurements for channel {channel}: {e}")
            return {}
    
    def get_all_channels_measurements(self) -> Dict[int, Dict[str, float]]:
        """Get measurements for all channels"""
        all_measurements = {}
        for ch in range(1, 4):
            all_measurements[ch] = self.get_all_measurements(ch)
        return all_measurements
    
    # Status and Monitoring
    def get_channel_status(self, channel: int) -> Dict[str, Any]:
        """Get comprehensive status for specified channel"""
        if not 1 <= channel <= 3:
            raise ValueError("Channel must be 1, 2, or 3")
        
        try:
            status = {
                'enabled': self.get_channel_enabled(channel),
                'voltage_setpoint': self.get_voltage_setpoint(channel),
                'current_setpoint': self.get_current_setpoint(channel),
                'measurements': self.get_all_measurements(channel),
                'protection': {
                    'ovp': self.get_ovp(channel),
                    'ocp': self.get_ocp(channel)
                },
                'specifications': self.channel_specs[channel]
            }
            return status
        except Exception as e:
            self.logger.error(f"Failed to get status for channel {channel}: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status for all channels"""
        base_status = super().get_status()
        
        if self.connected:
            try:
                ps_status = {
                    'channels': {}
                }
                
                for ch in range(1, 4):
                    ps_status['channels'][ch] = self.get_channel_status(ch)
                
                base_status.update(ps_status)
            except Exception as e:
                self.logger.error(f"Failed to get status: {e}")
        
        return base_status
    
    # Error Checking
    def check_errors(self) -> List[str]:
        """Check for instrument errors"""
        errors = []
        try:
            while True:
                error = self.query("SYST:ERR?")
                if error.startswith("0,"):
                    break
                errors.append(error)
        except Exception as e:
            self.logger.error(f"Failed to check errors: {e}")
        
        return errors
    
    # Convenience Methods
    def set_channel_output(self, channel: int, voltage: float, current: float, enabled: bool = True) -> bool:
        """Set voltage, current, and enable state for a channel"""
        success = True
        success &= self.set_voltage(channel, voltage)
        success &= self.set_current(channel, current)
        success &= self.set_channel_enabled(channel, enabled)
        return success
    
    def emergency_stop(self) -> bool:
        """Emergency stop - turn off all outputs immediately"""
        return self.set_all_outputs_enabled(False)
