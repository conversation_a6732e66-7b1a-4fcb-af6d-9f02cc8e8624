import { motion } from 'framer-motion';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  fullScreen?: boolean;
}

const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'primary',
  fullScreen = false 
}: LoadingSpinnerProps) => {
  const sizeMap = {
    small: 'h-6 w-6',
    medium: 'h-12 w-12',
    large: 'h-16 w-16'
  };

  const colorMap = {
    primary: 'border-primary',
    white: 'border-white',
    dark: 'border-dark'
  };

  const spinnerElement = (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={`rounded-full border-t-2 border-b-2 ${colorMap[color as keyof typeof colorMap] || 'border-primary'} ${sizeMap[size]}`}
    />
  );

  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white bg-opacity-80 z-50">
        {spinnerElement}
      </div>
    );
  }

  return spinnerElement;
};

export default LoadingSpinner;
