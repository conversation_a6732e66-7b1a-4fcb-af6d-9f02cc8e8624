# Lab Instrument Control System

A comprehensive Python-based instrument control system for laboratory automation with GUI interface and VISA communication support.

## Overview

This system provides a unified interface to control various laboratory instruments including:

- **Oscilloscopes**: Tektronix MSO58B (8-channel)
- **Power Supplies**: Agilent E3631A (3-channel), Chroma 62006P (1-channel), Rigol DP821A (2-channel)
- **Electronic Loads**: HP 6063B (1-channel)
- **Multimeters**: Agilent 34401A (6.5 digit)

## Features

- **VISA Communication**: Supports USB, GPIB, Ethernet, and Serial connections
- **Modular Design**: Easy to add new instrument drivers
- **GUI Interface**: User-friendly control panels for each instrument type
- **Configuration Management**: YAML-based instrument configuration
- **Real-time Monitoring**: Live measurement displays and status updates
- **Safety Features**: Protection limits and emergency stop functionality
- **Logging**: Comprehensive logging for debugging and audit trails

## Supported Instruments

### Oscilloscopes
- **Tektronix MSO58B**: 8-channel, 1 GHz bandwidth, 6.25 GS/s
  - Channel configuration and control
  - Trigger settings
  - Measurement functions
  - Screenshot capture

### Power Supplies
- **Agilent E3631A**: Triple output (6V/5A, ±25V/1A)
- **Chroma 62006P**: Single output (100V/25A/600W)
- **Rigol DP821A**: Dual output (60V/1A, 8V/10A)
  - Voltage and current control
  - Output enable/disable
  - Protection settings
  - Real-time measurements

### Electronic Loads
- **HP 6063B**: 240V/240A/240W
  - Multiple operating modes (CC, CV, CR, CP)
  - Protection settings
  - Real-time measurements

### Multimeters
- **Agilent 34401A**: 6.5 digit precision
  - Multiple measurement functions
  - Range selection
  - Statistics and data logging

## Project Structure

```
Python_Instrument_Control/
├── src/
│   ├── instruments/           # Instrument drivers
│   │   ├── base_instrument.py
│   │   ├── tektronix_mso58b.py
│   │   ├── agilent_e3631a.py
│   │   ├── chroma_62006p.py
│   │   ├── rigol_dp821a.py
│   │   ├── hp_6063b.py
│   │   └── agilent_34401a.py
│   ├── gui/                   # GUI components
│   │   ├── main_window.py
│   │   └── instrument_control_widget.py
│   ├── utils/                 # Utilities
│   │   └── visa_manager.py
│   └── main_app.py           # Application launcher
├── config/                   # Configuration files
│   ├── config.yaml
│   └── instruments.yaml
├── docs/                     # Instrument manuals
├── logs/                     # Log files
├── requirements.txt
└── README.md
```

## Installation

### Prerequisites

- Python 3.8 or higher
- NI-VISA runtime (recommended) or pyvisa-py
- Windows, macOS, or Linux

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Key Dependencies

- `pyvisa`: VISA communication library
- `customtkinter`: Modern GUI framework
- `pyyaml`: Configuration file handling
- `numpy`, `pandas`: Data processing
- `matplotlib`: Plotting and visualization

## Configuration

### Instrument Configuration

Edit `config/instruments.yaml` to configure your instruments:

```yaml
instruments:
  power_supplies:
    agilent_e3631a:
      name: "Agilent E3631A"
      visa_address: "GPIB0::5::INSTR"
      type: "power_supply"
      channels: 3
      enabled: true

  multimeters:
    agilent_34401a:
      name: "Agilent 34401A"
      visa_address: "GPIB0::8::INSTR"
      type: "multimeter"
      enabled: true
```

### Application Configuration

Edit `config/config.yaml` for application settings:

```yaml
application:
  name: "Lab Instrument Control System"

gui:
  theme: "dark"
  window_size: [1200, 800]

instruments:
  connection_timeout: 5.0
  auto_connect: false
```

## Usage

### Starting the Application

```bash
python src/main_app.py
```

Or use the provided batch file:
```bash
Quick_Start.bat
```

### GUI Interface

1. **Connections Tab**: Scan for VISA resources and configure instruments
2. **Instruments Tab**: Control connected instruments with dedicated panels
3. **Data Tab**: Data acquisition and logging features
4. **Settings Tab**: Application configuration

### Connecting Instruments

1. Go to the "Connections" tab
2. Click "Scan Resources" to find available VISA instruments
3. Select instrument type and enter VISA address
4. Add to configuration
5. Go to "Instruments" tab and click "Connect"

### Controlling Instruments

Each instrument type has a dedicated control panel:

- **Power Supplies**: Set voltage/current, enable outputs, view measurements
- **Multimeters**: Select function/range, take readings, view statistics
- **Oscilloscopes**: Configure channels, triggers, take measurements
- **Electronic Loads**: Set operating mode, configure limits

## Adding New Instruments

To add support for a new instrument:

1. **Create a new driver file** in `src/instruments/`
2. **Inherit from BaseInstrument**:

```python
from .base_instrument import BaseInstrument

class MyInstrument(BaseInstrument):
    def __init__(self, name: str, visa_address: str, visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)

    def get_capabilities(self) -> Dict[str, Any]:
        return {
            'type': 'my_instrument_type',
            'features': ['feature1', 'feature2']
        }

    def initialize(self) -> bool:
        # Implement initialization logic
        return True
```

3. **Add to instrument registry** in `src/instruments/__init__.py`
4. **Update configuration** in `config/instruments.yaml`

## API Reference

### BaseInstrument Class

All instrument drivers inherit from `BaseInstrument`:

- `connect()`: Establish VISA connection
- `disconnect()`: Close VISA connection
- `query(command)`: Send query and return response
- `write(command)`: Send command to instrument
- `get_id()`: Get instrument identification (*IDN?)
- `reset()`: Reset instrument (*RST)
- `get_status()`: Get comprehensive instrument status
- `get_capabilities()`: Get instrument capabilities (abstract)
- `initialize()`: Initialize instrument (abstract)

### VISAManager Class

Manages VISA resources and connections:

- `scan_resources()`: Scan for available VISA resources
- `connect_instrument(id, address)`: Connect to instrument
- `disconnect_instrument(id)`: Disconnect instrument
- `get_instrument(id)`: Get connected instrument resource

## Safety and Best Practices

### Safety Guidelines

- **Always verify voltage/current limits** before connecting instruments
- **Use emergency stop** functions when available
- **Check protection settings** before enabling outputs
- **Monitor instrument status** during operation
- **Follow instrument-specific safety procedures**

### Best Practices

- **Test connections** before running automated sequences
- **Use appropriate VISA timeouts** for your instruments
- **Log all operations** for debugging and audit trails
- **Implement error handling** in automation scripts
- **Regular calibration** of measurement instruments

## Troubleshooting

### Common Issues

- **VISA Connection Errors**:
  - Check VISA address format
  - Verify instrument is powered on
  - Check cable connections
  - Try different VISA backend (@ni vs @py)

- **GUI Not Responding**:
  - Check logs for error messages
  - Verify all dependencies are installed
  - Try running with different theme

- **Instrument Communication Errors**:
  - Check instrument manual for command syntax
  - Verify termination characters
  - Check timeout settings

### Getting Help

- Check the `docs/` directory for instrument manuals
- Review log files in `logs/` directory
- Test individual instrument drivers before using GUI
- Use VISA resource manager to verify connections

## Contributing

1. Fork the repository
2. Create a feature branch for your instrument
3. Add instrument driver following the BaseInstrument pattern
4. Add configuration to instruments.yaml
5. Test thoroughly with actual hardware
6. Submit a pull request with documentation

## License

This project is open source. See LICENSE file for details.

## Version History

- **v1.0.0** - Initial release with 6 instrument drivers and GUI interface

---

**Lab Instrument Control System** - Professional laboratory automation made simple.
