"""
Main GUI Window for Instrument Control Application
"""

import customtkinter as ctk
from typing import Dict, Any, Optional
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import yaml
from pathlib import Path

from utils.visa_manager import VISAManager
from instruments import INSTRUMENT_CLASSES
import logging

logger = logging.getLogger(__name__)

class InstrumentControlGUI:
    """Main GUI application window"""

    def __init__(self, config_path: str = "config/config.yaml"):
        self.config_path = config_path
        self.config = self.load_config()
        self.logger = logger.getChild("MainWindow")

        # Initialize VISA manager
        self.visa_manager = VISAManager("config/instruments.yaml")

        # Configure CustomTkinter
        theme = self.config.get('gui', {}).get('theme', 'dark')
        ctk.set_appearance_mode(theme)
        ctk.set_default_color_theme("blue")

        # Initialize main window
        self.root = ctk.CTk()
        self.setup_window()

        # Initialize GUI components
        self.instruments = {}
        self.instrument_widgets = {}
        self.create_widgets()

        # Load instruments from config
        self.load_instruments_from_config()

        self.logger.info("Main GUI window initialized")

    def load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            return {}
    
    def setup_window(self):
        """Setup main window properties"""

        app_name = self.config.get('application', {}).get('name', 'Python Instrument Control')
        self.root.title(app_name)

        # Window size and position
        gui_config = self.config.get('gui', {})
        size = gui_config.get('window_size', [1200, 800])
        pos = gui_config.get('window_position', [100, 100])

        self.root.geometry(f"{size[0]}x{size[1]}+{pos[0]}+{pos[1]}")
        self.root.minsize(800, 600)

        # Window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def load_instruments_from_config(self):
        """Load instruments from configuration file"""
        try:
            instrument_list = self.visa_manager.get_instrument_list()
            for inst_id, inst_info in instrument_list.items():
                self.add_instrument_to_list(inst_id, inst_info['config'])
        except Exception as e:
            self.logger.error(f"Failed to load instruments from config: {e}")

    def add_instrument_to_list(self, inst_id: str, config: Dict[str, Any]):
        """Add instrument to the instruments list"""
        display_name = config.get('name', inst_id)
        visa_address = config.get('visa_address', 'Unknown')
        enabled = config.get('enabled', True)

        # Add to listbox
        status = "✓" if enabled else "✗"
        self.instruments_listbox.insert(tk.END, f"{status} {display_name} ({visa_address})")

        # Store instrument info
        self.instruments[inst_id] = {
            'config': config,
            'instance': None,
            'connected': False
        }
    
    def create_widgets(self):
        """Create main GUI widgets"""

        # Main container
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Title
        title_label = ctk.CTkLabel(
            main_frame,
            text="Lab Instrument Control System",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(10, 20))

        # Create notebook for tabs
        self.notebook = ctk.CTkTabview(main_frame)
        self.notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Create tabs
        self.create_connection_tab()
        self.create_instruments_tab()
        self.create_data_tab()
        self.create_settings_tab()

        # Status bar
        self.create_status_bar(main_frame)

    def create_connection_tab(self):
        """Create VISA connection management tab"""

        conn_tab = self.notebook.add("Connections")

        # VISA Resources frame
        resources_frame = ctk.CTkFrame(conn_tab)
        resources_frame.pack(side="left", fill="y", padx=(10, 5), pady=10)

        ctk.CTkLabel(resources_frame, text="VISA Resources",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # Scan button
        ctk.CTkButton(resources_frame, text="Scan Resources",
                     command=self.scan_visa_resources).pack(pady=5, padx=10, fill="x")

        # Resources listbox
        self.resources_listbox = tk.Listbox(resources_frame, height=15, width=40)
        self.resources_listbox.pack(padx=10, pady=5)

        # Instrument configuration frame
        config_frame = ctk.CTkFrame(conn_tab)
        config_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)

        ctk.CTkLabel(config_frame, text="Instrument Configuration",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # Configuration controls will be added here
        self.create_instrument_config_controls(config_frame)
    
    def create_instruments_tab(self):
        """Create instruments control tab"""

        instruments_tab = self.notebook.add("Instruments")

        # Instruments list frame
        list_frame = ctk.CTkFrame(instruments_tab)
        list_frame.pack(side="left", fill="y", padx=(10, 5), pady=10)

        ctk.CTkLabel(list_frame, text="Configured Instruments",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # Instruments listbox
        self.instruments_listbox = tk.Listbox(list_frame, height=15, width=35)
        self.instruments_listbox.pack(padx=10, pady=5)
        self.instruments_listbox.bind('<<ListboxSelect>>', self.on_instrument_select)

        # Instrument control buttons
        btn_frame = ctk.CTkFrame(list_frame)
        btn_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkButton(btn_frame, text="Refresh List",
                     command=self.refresh_instrument_list).pack(pady=2, fill="x")
        ctk.CTkButton(btn_frame, text="Connect",
                     command=self.connect_instrument).pack(pady=2, fill="x")
        ctk.CTkButton(btn_frame, text="Disconnect",
                     command=self.disconnect_instrument).pack(pady=2, fill="x")
        ctk.CTkButton(btn_frame, text="Test Connection",
                     command=self.test_instrument_connection).pack(pady=2, fill="x")

        # Control panel frame
        control_frame = ctk.CTkFrame(instruments_tab)
        control_frame.pack(side="right", fill="both", expand=True, padx=(5, 10), pady=10)

        ctk.CTkLabel(control_frame, text="Instrument Control",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # Instrument-specific control area
        self.instrument_control_frame = ctk.CTkFrame(control_frame)
        self.instrument_control_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Default message
        self.no_instrument_label = ctk.CTkLabel(
            self.instrument_control_frame,
            text="Select an instrument to view controls",
            font=ctk.CTkFont(size=14)
        )
        self.no_instrument_label.pack(expand=True)

    def create_instrument_config_controls(self, parent):
        """Create instrument configuration controls"""

        # Instrument type selection
        type_frame = ctk.CTkFrame(parent)
        type_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(type_frame, text="Instrument Type:").pack(side="left", padx=5)
        self.inst_type_var = tk.StringVar(value="Select Type")
        type_menu = ctk.CTkOptionMenu(type_frame, variable=self.inst_type_var,
                                     values=list(INSTRUMENT_CLASSES.keys()))
        type_menu.pack(side="left", padx=5)

        # VISA address entry
        addr_frame = ctk.CTkFrame(parent)
        addr_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(addr_frame, text="VISA Address:").pack(side="left", padx=5)
        self.visa_addr_entry = ctk.CTkEntry(addr_frame, width=300)
        self.visa_addr_entry.pack(side="left", padx=5, fill="x", expand=True)

        # Name entry
        name_frame = ctk.CTkFrame(parent)
        name_frame.pack(fill="x", padx=10, pady=5)

        ctk.CTkLabel(name_frame, text="Display Name:").pack(side="left", padx=5)
        self.inst_name_entry = ctk.CTkEntry(name_frame, width=200)
        self.inst_name_entry.pack(side="left", padx=5)

        # Add button
        ctk.CTkButton(parent, text="Add to Configuration",
                     command=self.add_instrument_to_config).pack(pady=10)
    
    def create_data_tab(self):
        """Create data acquisition tab"""
        
        data_tab = self.notebook.add("Data")
        
        ctk.CTkLabel(data_tab, text="Data Acquisition", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=20)
        
        # Placeholder for data acquisition controls
        ctk.CTkLabel(data_tab, text="Data acquisition features will be implemented here").pack(pady=20)
    
    def create_settings_tab(self):
        """Create settings tab"""
        
        settings_tab = self.notebook.add("Settings")
        
        ctk.CTkLabel(settings_tab, text="Application Settings", 
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=20)
        
        # Theme selection
        theme_frame = ctk.CTkFrame(settings_tab)
        theme_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(theme_frame, text="Theme:").pack(side="left", padx=10)
        theme_var = ctk.StringVar(value=self.config.get("gui.theme", "dark"))
        theme_menu = ctk.CTkOptionMenu(theme_frame, variable=theme_var, 
                                      values=["light", "dark", "system"],
                                      command=self.change_theme)
        theme_menu.pack(side="left", padx=10)
    
    def create_status_bar(self, parent):
        """Create status bar"""
        
        status_frame = ctk.CTkFrame(parent)
        status_frame.pack(fill="x", padx=10, pady=(0, 10))
        
        self.status_label = ctk.CTkLabel(status_frame, text="Ready")
        self.status_label.pack(side="left", padx=10, pady=5)
    
    # VISA and Connection Methods
    def scan_visa_resources(self):
        """Scan for available VISA resources"""
        try:
            self.update_status("Scanning VISA resources...")
            resources = self.visa_manager.scan_resources()

            # Update resources listbox
            self.resources_listbox.delete(0, tk.END)
            for resource in resources:
                self.resources_listbox.insert(tk.END, resource)

            self.update_status(f"Found {len(resources)} VISA resources")

        except Exception as e:
            self.logger.error(f"Failed to scan VISA resources: {e}")
            messagebox.showerror("Error", f"Failed to scan VISA resources: {e}")
            self.update_status("VISA scan failed")

    def add_instrument_to_config(self):
        """Add instrument to configuration"""
        try:
            inst_type = self.inst_type_var.get()
            visa_addr = self.visa_addr_entry.get().strip()
            inst_name = self.inst_name_entry.get().strip()

            if inst_type == "Select Type" or not visa_addr or not inst_name:
                messagebox.showwarning("Invalid Input", "Please fill all fields")
                return

            # Add to instruments list
            config = {
                'name': inst_name,
                'visa_address': visa_addr,
                'type': inst_type,
                'enabled': True
            }

            self.add_instrument_to_list(inst_name.lower().replace(' ', '_'), config)

            # Clear entries
            self.visa_addr_entry.delete(0, tk.END)
            self.inst_name_entry.delete(0, tk.END)

            self.update_status(f"Added {inst_name} to configuration")

        except Exception as e:
            self.logger.error(f"Failed to add instrument: {e}")
            messagebox.showerror("Error", f"Failed to add instrument: {e}")

    def refresh_instrument_list(self):
        """Refresh the instruments list"""
        try:
            self.instruments_listbox.delete(0, tk.END)
            self.instruments.clear()
            self.load_instruments_from_config()
            self.update_status("Instrument list refreshed")
        except Exception as e:
            self.logger.error(f"Failed to refresh instrument list: {e}")

    def on_instrument_select(self, event):
        """Handle instrument selection"""
        selection = self.instruments_listbox.curselection()
        if not selection:
            return

        # Get selected instrument ID
        selected_text = self.instruments_listbox.get(selection[0])
        # Extract instrument name from display text
        inst_name = selected_text.split(' ', 1)[1].split(' (')[0]

        # Find instrument ID
        inst_id = None
        for id, info in self.instruments.items():
            if info['config'].get('name') == inst_name:
                inst_id = id
                break

        if inst_id:
            self.show_instrument_controls(inst_id)

    def show_instrument_controls(self, inst_id):
        """Show controls for selected instrument"""
        try:
            # Clear current controls
            for widget in self.instrument_control_frame.winfo_children():
                widget.destroy()

            if inst_id not in self.instruments:
                return

            instrument_info = self.instruments[inst_id]

            if instrument_info['connected'] and instrument_info['instance']:
                # Show instrument-specific controls
                from .instrument_control_widget import create_instrument_widget

                widget = create_instrument_widget(
                    self.instrument_control_frame,
                    instrument_info['instance']
                )
                widget.get_frame().pack(fill="both", expand=True)

                self.instrument_widgets[inst_id] = widget
            else:
                # Show connection status
                status_label = ctk.CTkLabel(
                    self.instrument_control_frame,
                    text=f"{instrument_info['config']['name']}\nNot Connected",
                    font=ctk.CTkFont(size=14)
                )
                status_label.pack(expand=True)

        except Exception as e:
            self.logger.error(f"Failed to show instrument controls: {e}")

    def connect_instrument(self):
        """Connect to selected instrument"""
        selection = self.instruments_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an instrument to connect")
            return

        try:
            # Get selected instrument
            selected_text = self.instruments_listbox.get(selection[0])
            inst_name = selected_text.split(' ', 1)[1].split(' (')[0]

            # Find instrument ID
            inst_id = None
            for id, info in self.instruments.items():
                if info['config'].get('name') == inst_name:
                    inst_id = id
                    break

            if not inst_id:
                messagebox.showerror("Error", "Instrument not found")
                return

            self.update_status(f"Connecting to {inst_name}...")

            # Create instrument instance
            config = self.instruments[inst_id]['config']
            inst_type = config.get('type', 'unknown')

            if inst_type in INSTRUMENT_CLASSES:
                instrument_class = INSTRUMENT_CLASSES[inst_type]
                instrument = instrument_class(
                    name=config['name'],
                    visa_address=config['visa_address'],
                    visa_manager=self.visa_manager
                )

                # Connect
                if instrument.connect():
                    # Initialize
                    if instrument.initialize():
                        self.instruments[inst_id]['instance'] = instrument
                        self.instruments[inst_id]['connected'] = True

                        # Update display
                        self.update_instrument_display(inst_id)
                        self.show_instrument_controls(inst_id)

                        self.update_status(f"Connected to {inst_name}")
                        messagebox.showinfo("Success", f"Connected to {inst_name}")
                    else:
                        instrument.disconnect()
                        messagebox.showerror("Error", f"Failed to initialize {inst_name}")
                        self.update_status("Connection failed")
                else:
                    messagebox.showerror("Error", f"Failed to connect to {inst_name}")
                    self.update_status("Connection failed")
            else:
                messagebox.showerror("Error", f"Unknown instrument type: {inst_type}")

        except Exception as e:
            self.logger.error(f"Failed to connect to instrument: {e}")
            messagebox.showerror("Error", f"Connection failed: {e}")
            self.update_status("Connection failed")

    def disconnect_instrument(self):
        """Disconnect from selected instrument"""
        selection = self.instruments_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an instrument to disconnect")
            return

        try:
            # Get selected instrument
            selected_text = self.instruments_listbox.get(selection[0])
            inst_name = selected_text.split(' ', 1)[1].split(' (')[0]

            # Find instrument ID
            inst_id = None
            for id, info in self.instruments.items():
                if info['config'].get('name') == inst_name:
                    inst_id = id
                    break

            if not inst_id:
                return

            if self.instruments[inst_id]['connected']:
                # Disconnect instrument
                if self.instruments[inst_id]['instance']:
                    self.instruments[inst_id]['instance'].disconnect()

                self.instruments[inst_id]['instance'] = None
                self.instruments[inst_id]['connected'] = False

                # Update display
                self.update_instrument_display(inst_id)
                self.show_instrument_controls(inst_id)

                # Remove widget
                if inst_id in self.instrument_widgets:
                    del self.instrument_widgets[inst_id]

                self.update_status(f"Disconnected from {inst_name}")
                messagebox.showinfo("Success", f"Disconnected from {inst_name}")

        except Exception as e:
            self.logger.error(f"Failed to disconnect instrument: {e}")
            messagebox.showerror("Error", f"Disconnect failed: {e}")

    def test_instrument_connection(self):
        """Test connection to selected instrument"""
        selection = self.instruments_listbox.curselection()
        if not selection:
            messagebox.showwarning("No Selection", "Please select an instrument to test")
            return

        try:
            # Get selected instrument
            selected_text = self.instruments_listbox.get(selection[0])
            inst_name = selected_text.split(' ', 1)[1].split(' (')[0]

            # Find instrument ID
            inst_id = None
            for id, info in self.instruments.items():
                if info['config'].get('name') == inst_name:
                    inst_id = id
                    break

            if not inst_id:
                return

            if self.instruments[inst_id]['connected'] and self.instruments[inst_id]['instance']:
                # Test with *IDN? query
                try:
                    idn = self.instruments[inst_id]['instance'].get_id()
                    messagebox.showinfo("Connection Test", f"Instrument Response:\n{idn}")
                    self.update_status("Connection test successful")
                except Exception as e:
                    messagebox.showerror("Connection Test", f"Test failed: {e}")
                    self.update_status("Connection test failed")
            else:
                messagebox.showwarning("Not Connected", "Instrument is not connected")

        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            messagebox.showerror("Error", f"Connection test failed: {e}")

    def update_instrument_display(self, inst_id):
        """Update instrument display in listbox"""
        try:
            config = self.instruments[inst_id]['config']
            connected = self.instruments[inst_id]['connected']

            display_name = config.get('name', inst_id)
            visa_address = config.get('visa_address', 'Unknown')

            status = "🟢" if connected else "🔴"
            display_text = f"{status} {display_name} ({visa_address})"

            # Find and update the item in listbox
            for i in range(self.instruments_listbox.size()):
                item_text = self.instruments_listbox.get(i)
                if display_name in item_text:
                    self.instruments_listbox.delete(i)
                    self.instruments_listbox.insert(i, display_text)
                    break

        except Exception as e:
            self.logger.error(f"Failed to update instrument display: {e}")
    
    def change_theme(self, theme):
        """Change application theme"""
        ctk.set_appearance_mode(theme)
        # Update config
        if 'gui' not in self.config:
            self.config['gui'] = {}
        self.config['gui']['theme'] = theme
        self.save_config()

    def save_config(self):
        """Save configuration to file"""
        try:
            with open(self.config_path, 'w') as f:
                yaml.dump(self.config, f, default_flow_style=False)
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")

    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.configure(text=message)
        self.root.update_idletasks()  # Force GUI update

    def on_closing(self):
        """Handle window closing"""
        self.logger.info("Application closing")

        try:
            # Disconnect all instruments
            for inst_id, inst_info in self.instruments.items():
                if inst_info['connected'] and inst_info['instance']:
                    inst_info['instance'].disconnect()

            # Disconnect VISA manager
            self.visa_manager.disconnect_all()

            # Save window position and size
            geometry = self.root.geometry()
            # Parse geometry string (e.g., "1200x800+100+100")
            size_pos = geometry.split('+')
            size = size_pos[0].split('x')
            pos = size_pos[1:] if len(size_pos) > 1 else [100, 100]

            if 'gui' not in self.config:
                self.config['gui'] = {}
            self.config['gui']['window_size'] = [int(size[0]), int(size[1])]
            self.config['gui']['window_position'] = [int(pos[0]), int(pos[1])]
            self.save_config()

        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")

        self.root.destroy()

    def run(self):
        """Start the GUI application"""
        self.logger.info("Starting GUI application")
        self.update_status("Application started - Ready to connect instruments")

        # Initial VISA scan
        try:
            self.scan_visa_resources()
        except Exception as e:
            self.logger.warning(f"Initial VISA scan failed: {e}")

        self.root.mainloop()
