"""
HP 6063B Electronic Load Driver
Single channel DC electronic load: 0-240V, 0-240A, 240W
"""

from typing import Dict, Any, List, Optional
import time
from .base_instrument import BaseInstrument


class HP6063B(BaseInstrument):
    """Driver for HP 6063B Electronic Load"""
    
    def __init__(self, name: str = "HP6063B", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        
        # Specifications
        self.max_voltage = 240.0  # V
        self.max_current = 240.0  # A
        self.max_power = 240.0    # W
        
        # Operating modes
        self.modes = ['CC', 'CV', 'CR', 'CP']  # Constant Current, Voltage, Resistance, Power
        self.current_mode = 'CC'
        
        # Current settings
        self.settings = {
            'mode': 'CC',
            'current': 0.0,
            'voltage': 0.0,
            'resistance': 1000.0,
            'power': 0.0,
            'enabled': False
        }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get electronic load capabilities"""
        return {
            'type': 'electronic_load',
            'channels': 1,
            'max_voltage': self.max_voltage,
            'max_current': self.max_current,
            'max_power': self.max_power,
            'modes': self.modes,
            'voltage_range': [0, self.max_voltage],
            'current_range': [0, self.max_current],
            'power_range': [0, self.max_power],
            'resistance_range': [0.1, 10000]  # Ohms
        }
    
    def initialize(self) -> bool:
        """Initialize electronic load to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(1)
            
            # Set to safe initial state
            self.set_load_enabled(False)
            self.set_mode('CC')
            self.set_current(0.0)
            
            # Clear any errors
            self.write("*CLS")
            
            self.logger.info("HP 6063B initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize HP 6063B: {e}")
            return False
    
    # Mode Control
    def set_mode(self, mode: str) -> bool:
        """Set operating mode (CC, CV, CR, CP)"""
        mode = mode.upper()
        if mode not in self.modes:
            raise ValueError(f"Mode must be one of: {self.modes}")
        
        mode_commands = {
            'CC': 'CURR',
            'CV': 'VOLT',
            'CR': 'RES',
            'CP': 'POW'
        }
        
        success = self.write(f"MODE {mode_commands[mode]}")
        if success:
            self.current_mode = mode
            self.settings['mode'] = mode
        return success
    
    def get_mode(self) -> str:
        """Get current operating mode"""
        response = self.query("MODE?")
        mode_map = {
            'CURR': 'CC',
            'VOLT': 'CV',
            'RES': 'CR',
            'POW': 'CP'
        }
        return mode_map.get(response, 'CC')
    
    # Load Control
    def set_load_enabled(self, enabled: bool) -> bool:
        """Enable or disable the load"""
        state = "ON" if enabled else "OFF"
        success = self.write(f"INPUT {state}")
        if success:
            self.settings['enabled'] = enabled
        return success
    
    def get_load_enabled(self) -> bool:
        """Get load enabled state"""
        response = self.query("INPUT?")
        return response.strip() == "1"
    
    # Current Mode Settings
    def set_current(self, current: float) -> bool:
        """Set current setpoint (A)"""
        if not 0 <= current <= self.max_current:
            raise ValueError(f"Current must be 0-{self.max_current} A")
        
        success = self.write(f"CURR {current}")
        if success:
            self.settings['current'] = current
        return success
    
    def get_current_setpoint(self) -> float:
        """Get current setpoint"""
        response = self.query("CURR?")
        return float(response)
    
    # Voltage Mode Settings
    def set_voltage(self, voltage: float) -> bool:
        """Set voltage setpoint (V)"""
        if not 0 <= voltage <= self.max_voltage:
            raise ValueError(f"Voltage must be 0-{self.max_voltage} V")
        
        success = self.write(f"VOLT {voltage}")
        if success:
            self.settings['voltage'] = voltage
        return success
    
    def get_voltage_setpoint(self) -> float:
        """Get voltage setpoint"""
        response = self.query("VOLT?")
        return float(response)
    
    # Resistance Mode Settings
    def set_resistance(self, resistance: float) -> bool:
        """Set resistance setpoint (Ohms)"""
        if not 0.1 <= resistance <= 10000:
            raise ValueError("Resistance must be 0.1-10000 Ohms")
        
        success = self.write(f"RES {resistance}")
        if success:
            self.settings['resistance'] = resistance
        return success
    
    def get_resistance_setpoint(self) -> float:
        """Get resistance setpoint"""
        response = self.query("RES?")
        return float(response)
    
    # Power Mode Settings
    def set_power(self, power: float) -> bool:
        """Set power setpoint (W)"""
        if not 0 <= power <= self.max_power:
            raise ValueError(f"Power must be 0-{self.max_power} W")
        
        success = self.write(f"POW {power}")
        if success:
            self.settings['power'] = power
        return success
    
    def get_power_setpoint(self) -> float:
        """Get power setpoint"""
        response = self.query("POW?")
        return float(response)
    
    # Measurement Methods
    def measure_voltage(self) -> float:
        """Measure actual voltage (V)"""
        response = self.query("MEAS:VOLT?")
        return float(response)
    
    def measure_current(self) -> float:
        """Measure actual current (A)"""
        response = self.query("MEAS:CURR?")
        return float(response)
    
    def measure_power(self) -> float:
        """Measure actual power (W)"""
        response = self.query("MEAS:POW?")
        return float(response)
    
    def measure_resistance(self) -> float:
        """Calculate resistance from V/I measurements"""
        voltage = self.measure_voltage()
        current = self.measure_current()
        if current > 0.001:  # Avoid division by zero
            return voltage / current
        else:
            return float('inf')
    
    def get_all_measurements(self) -> Dict[str, float]:
        """Get all measurements"""
        try:
            measurements = {
                'voltage': self.measure_voltage(),
                'current': self.measure_current(),
                'power': self.measure_power(),
                'resistance': self.measure_resistance()
            }
            return measurements
        except Exception as e:
            self.logger.error(f"Failed to get measurements: {e}")
            return {}
    
    # Protection and Limits
    def set_voltage_limit(self, limit: float) -> bool:
        """Set voltage protection limit (V)"""
        if not 0 <= limit <= self.max_voltage:
            raise ValueError(f"Voltage limit must be 0-{self.max_voltage} V")
        
        return self.write(f"VOLT:PROT {limit}")
    
    def get_voltage_limit(self) -> float:
        """Get voltage protection limit"""
        response = self.query("VOLT:PROT?")
        return float(response)
    
    def set_current_limit(self, limit: float) -> bool:
        """Set current protection limit (A)"""
        if not 0 <= limit <= self.max_current:
            raise ValueError(f"Current limit must be 0-{self.max_current} A")
        
        return self.write(f"CURR:PROT {limit}")
    
    def get_current_limit(self) -> float:
        """Get current protection limit"""
        response = self.query("CURR:PROT?")
        return float(response)
    
    def set_power_limit(self, limit: float) -> bool:
        """Set power protection limit (W)"""
        if not 0 <= limit <= self.max_power:
            raise ValueError(f"Power limit must be 0-{self.max_power} W")
        
        return self.write(f"POW:PROT {limit}")
    
    def get_power_limit(self) -> float:
        """Get power protection limit"""
        response = self.query("POW:PROT?")
        return float(response)
    
    # Status and Error Checking
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status"""
        base_status = super().get_status()
        
        if self.connected:
            try:
                load_status = {
                    'mode': self.get_mode(),
                    'enabled': self.get_load_enabled(),
                    'measurements': self.get_all_measurements(),
                    'setpoints': {
                        'current': self.get_current_setpoint(),
                        'voltage': self.get_voltage_setpoint(),
                        'resistance': self.get_resistance_setpoint(),
                        'power': self.get_power_setpoint()
                    },
                    'limits': {
                        'voltage': self.get_voltage_limit(),
                        'current': self.get_current_limit(),
                        'power': self.get_power_limit()
                    }
                }
                base_status.update(load_status)
            except Exception as e:
                self.logger.error(f"Failed to get status: {e}")
        
        return base_status
    
    def check_errors(self) -> List[str]:
        """Check for instrument errors"""
        errors = []
        try:
            while True:
                error = self.query("SYST:ERR?")
                if error.startswith("0,"):
                    break
                errors.append(error)
        except Exception as e:
            self.logger.error(f"Failed to check errors: {e}")
        
        return errors
    
    def clear_errors(self) -> bool:
        """Clear all errors"""
        return self.write("*CLS")
    
    # Convenience Methods
    def set_cc_mode(self, current: float) -> bool:
        """Set to constant current mode with specified current"""
        success = self.set_mode('CC')
        if success:
            success = self.set_current(current)
        return success
    
    def set_cv_mode(self, voltage: float) -> bool:
        """Set to constant voltage mode with specified voltage"""
        success = self.set_mode('CV')
        if success:
            success = self.set_voltage(voltage)
        return success
    
    def set_cr_mode(self, resistance: float) -> bool:
        """Set to constant resistance mode with specified resistance"""
        success = self.set_mode('CR')
        if success:
            success = self.set_resistance(resistance)
        return success
    
    def set_cp_mode(self, power: float) -> bool:
        """Set to constant power mode with specified power"""
        success = self.set_mode('CP')
        if success:
            success = self.set_power(power)
        return success
