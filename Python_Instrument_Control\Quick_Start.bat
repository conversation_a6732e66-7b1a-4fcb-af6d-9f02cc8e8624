@echo off
title Lab Instrument Control System - Quick Start
color 0A
echo.
echo ================================================
echo    Lab Instrument Control System - Quick Start
echo ================================================
echo.
echo Checking Python installation...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found! Please install Python 3.8 or higher.
    pause
    exit /b 1
)

echo.
echo Checking dependencies...
python -c "import pyvisa, customtkinter, yaml" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing required dependencies...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies!
        pause
        exit /b 1
    )
) else (
    echo Dependencies OK
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

echo.
echo ================================================
echo    Starting Lab Instrument Control System
echo ================================================
echo.
python src/main_app.py

echo.
echo Application closed. Press any key to exit.
pause >nul
