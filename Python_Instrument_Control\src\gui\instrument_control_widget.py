"""
Instrument Control Widget
Individual control panels for different instrument types
"""

import customtkinter as ctk
import tkinter as tk
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class BaseInstrumentWidget:
    """Base class for instrument control widgets"""
    
    def __init__(self, parent, instrument, update_callback=None):
        self.parent = parent
        self.instrument = instrument
        self.update_callback = update_callback
        self.logger = logger.getChild(f"Widget.{instrument.name}")
        
        self.frame = ctk.CTkFrame(parent)
        self.create_widgets()
    
    def create_widgets(self):
        """Create control widgets - to be implemented by subclasses"""
        pass
    
    def update_display(self):
        """Update display with current values"""
        pass
    
    def get_frame(self):
        """Get the main frame"""
        return self.frame


class PowerSupplyWidget(BaseInstrumentWidget):
    """Control widget for power supplies"""
    
    def create_widgets(self):
        # Title
        title = ctk.CTkLabel(self.frame, text=f"{self.instrument.name} Control",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # Get capabilities
        caps = self.instrument.get_capabilities()
        channels = caps.get('channels', 1)
        
        # Create channel controls
        self.channel_frames = {}
        for ch in range(1, channels + 1):
            self.create_channel_control(ch)
        
        # Status display
        self.create_status_display()
        
        # Control buttons
        self.create_control_buttons()
    
    def create_channel_control(self, channel):
        """Create controls for a single channel"""
        ch_frame = ctk.CTkFrame(self.frame)
        ch_frame.pack(fill="x", padx=10, pady=5)
        
        # Channel label
        ctk.CTkLabel(ch_frame, text=f"Channel {channel}",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        # Voltage control
        v_frame = ctk.CTkFrame(ch_frame)
        v_frame.pack(fill="x", padx=5, pady=2)
        
        ctk.CTkLabel(v_frame, text="Voltage (V):").pack(side="left", padx=5)
        v_entry = ctk.CTkEntry(v_frame, width=100)
        v_entry.pack(side="left", padx=5)
        ctk.CTkButton(v_frame, text="Set", width=60,
                     command=lambda: self.set_voltage(channel, v_entry.get())).pack(side="left", padx=5)
        
        # Current control
        i_frame = ctk.CTkFrame(ch_frame)
        i_frame.pack(fill="x", padx=5, pady=2)
        
        ctk.CTkLabel(i_frame, text="Current (A):").pack(side="left", padx=5)
        i_entry = ctk.CTkEntry(i_frame, width=100)
        i_entry.pack(side="left", padx=5)
        ctk.CTkButton(i_frame, text="Set", width=60,
                     command=lambda: self.set_current(channel, i_entry.get())).pack(side="left", padx=5)
        
        # Output enable
        enable_var = tk.BooleanVar()
        enable_cb = ctk.CTkCheckBox(ch_frame, text="Output Enabled", variable=enable_var,
                                   command=lambda: self.set_output_enabled(channel, enable_var.get()))
        enable_cb.pack(pady=5)
        
        # Store references
        self.channel_frames[channel] = {
            'frame': ch_frame,
            'voltage_entry': v_entry,
            'current_entry': i_entry,
            'enable_var': enable_var
        }
    
    def create_status_display(self):
        """Create status display"""
        status_frame = ctk.CTkFrame(self.frame)
        status_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(status_frame, text="Status",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        self.status_text = ctk.CTkTextbox(status_frame, height=100)
        self.status_text.pack(fill="x", padx=5, pady=5)
    
    def create_control_buttons(self):
        """Create control buttons"""
        btn_frame = ctk.CTkFrame(self.frame)
        btn_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkButton(btn_frame, text="Update Readings",
                     command=self.update_readings).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Emergency Stop",
                     command=self.emergency_stop).pack(side="left", padx=5)
    
    def set_voltage(self, channel, value_str):
        """Set voltage for channel"""
        try:
            value = float(value_str)
            if hasattr(self.instrument, 'set_voltage'):
                success = self.instrument.set_voltage(channel, value)
                if success:
                    self.logger.info(f"Set CH{channel} voltage to {value}V")
                    self.update_readings()
                else:
                    self.logger.error(f"Failed to set CH{channel} voltage")
        except ValueError:
            self.logger.error("Invalid voltage value")
        except Exception as e:
            self.logger.error(f"Error setting voltage: {e}")
    
    def set_current(self, channel, value_str):
        """Set current for channel"""
        try:
            value = float(value_str)
            if hasattr(self.instrument, 'set_current'):
                success = self.instrument.set_current(channel, value)
                if success:
                    self.logger.info(f"Set CH{channel} current to {value}A")
                    self.update_readings()
                else:
                    self.logger.error(f"Failed to set CH{channel} current")
        except ValueError:
            self.logger.error("Invalid current value")
        except Exception as e:
            self.logger.error(f"Error setting current: {e}")
    
    def set_output_enabled(self, channel, enabled):
        """Set output enabled state"""
        try:
            if hasattr(self.instrument, 'set_channel_enabled'):
                success = self.instrument.set_channel_enabled(channel, enabled)
                if success:
                    state = "ON" if enabled else "OFF"
                    self.logger.info(f"Set CH{channel} output {state}")
                    self.update_readings()
                else:
                    self.logger.error(f"Failed to set CH{channel} output state")
        except Exception as e:
            self.logger.error(f"Error setting output state: {e}")
    
    def update_readings(self):
        """Update measurement readings"""
        try:
            if hasattr(self.instrument, 'get_status'):
                status = self.instrument.get_status()
                self.display_status(status)
        except Exception as e:
            self.logger.error(f"Error updating readings: {e}")
    
    def display_status(self, status):
        """Display instrument status"""
        self.status_text.delete("1.0", tk.END)
        
        # Format status information
        status_str = f"Instrument: {status.get('name', 'Unknown')}\n"
        status_str += f"Connected: {status.get('connected', False)}\n\n"
        
        if 'channels' in status:
            for ch, ch_status in status['channels'].items():
                status_str += f"Channel {ch}:\n"
                if 'measurements' in ch_status:
                    meas = ch_status['measurements']
                    status_str += f"  Voltage: {meas.get('voltage', 0):.3f} V\n"
                    status_str += f"  Current: {meas.get('current', 0):.3f} A\n"
                    status_str += f"  Power: {meas.get('power', 0):.3f} W\n"
                status_str += f"  Enabled: {ch_status.get('enabled', False)}\n\n"
        
        self.status_text.insert("1.0", status_str)
    
    def emergency_stop(self):
        """Emergency stop all outputs"""
        try:
            if hasattr(self.instrument, 'emergency_stop'):
                self.instrument.emergency_stop()
                self.logger.info("Emergency stop activated")
                self.update_readings()
        except Exception as e:
            self.logger.error(f"Error during emergency stop: {e}")


class MultimeterWidget(BaseInstrumentWidget):
    """Control widget for multimeters"""
    
    def create_widgets(self):
        # Title
        title = ctk.CTkLabel(self.frame, text=f"{self.instrument.name} Control",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # Function selection
        func_frame = ctk.CTkFrame(self.frame)
        func_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(func_frame, text="Function:").pack(side="left", padx=5)
        
        caps = self.instrument.get_capabilities()
        functions = caps.get('functions', ['DC_VOLTAGE'])
        
        self.function_var = tk.StringVar(value=functions[0])
        function_menu = ctk.CTkOptionMenu(func_frame, variable=self.function_var,
                                         values=functions,
                                         command=self.on_function_change)
        function_menu.pack(side="left", padx=5)
        
        # Range selection
        range_frame = ctk.CTkFrame(self.frame)
        range_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(range_frame, text="Range:").pack(side="left", padx=5)
        self.range_var = tk.StringVar(value="AUTO")
        self.range_menu = ctk.CTkOptionMenu(range_frame, variable=self.range_var,
                                           values=["AUTO"])
        self.range_menu.pack(side="left", padx=5)
        
        # Measurement display
        self.create_measurement_display()
        
        # Control buttons
        self.create_control_buttons()
    
    def create_measurement_display(self):
        """Create measurement display"""
        meas_frame = ctk.CTkFrame(self.frame)
        meas_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        ctk.CTkLabel(meas_frame, text="Measurement",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        # Large reading display
        self.reading_label = ctk.CTkLabel(meas_frame, text="0.000000",
                                         font=ctk.CTkFont(size=24, weight="bold"))
        self.reading_label.pack(pady=10)
        
        # Unit label
        self.unit_label = ctk.CTkLabel(meas_frame, text="V",
                                      font=ctk.CTkFont(size=16))
        self.unit_label.pack()
        
        # Statistics
        self.stats_text = ctk.CTkTextbox(meas_frame, height=80)
        self.stats_text.pack(fill="x", padx=5, pady=5)
    
    def create_control_buttons(self):
        """Create control buttons"""
        btn_frame = ctk.CTkFrame(self.frame)
        btn_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkButton(btn_frame, text="Single Reading",
                     command=self.take_reading).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Statistics",
                     command=self.get_statistics).pack(side="left", padx=5)
    
    def on_function_change(self, function):
        """Handle function change"""
        try:
            if hasattr(self.instrument, 'set_function'):
                self.instrument.set_function(function)
                self.update_range_options(function)
                self.update_unit_display(function)
        except Exception as e:
            self.logger.error(f"Error changing function: {e}")
    
    def update_range_options(self, function):
        """Update range options based on function"""
        caps = self.instrument.get_capabilities()
        ranges = caps.get('ranges', {}).get(function, [])
        range_values = ["AUTO"] + [str(r) for r in ranges]
        
        self.range_menu.configure(values=range_values)
        self.range_var.set("AUTO")
    
    def update_unit_display(self, function):
        """Update unit display based on function"""
        units = {
            'DC_VOLTAGE': 'V', 'AC_VOLTAGE': 'V',
            'DC_CURRENT': 'A', 'AC_CURRENT': 'A',
            'RESISTANCE_2W': 'Ω', 'RESISTANCE_4W': 'Ω',
            'FREQUENCY': 'Hz', 'PERIOD': 's'
        }
        unit = units.get(function, '')
        self.unit_label.configure(text=unit)
    
    def take_reading(self):
        """Take a single reading"""
        try:
            if hasattr(self.instrument, 'read_measurement'):
                reading = self.instrument.read_measurement()
                self.reading_label.configure(text=f"{reading:.6f}")
        except Exception as e:
            self.logger.error(f"Error taking reading: {e}")
            self.reading_label.configure(text="ERROR")
    
    def get_statistics(self):
        """Get measurement statistics"""
        try:
            if hasattr(self.instrument, 'get_statistics'):
                stats = self.instrument.get_statistics()
                self.display_statistics(stats)
        except Exception as e:
            self.logger.error(f"Error getting statistics: {e}")
    
    def display_statistics(self, stats):
        """Display measurement statistics"""
        self.stats_text.delete("1.0", tk.END)
        
        stats_str = f"Mean: {stats.get('mean', 0):.6f}\n"
        stats_str += f"Std Dev: {stats.get('std_dev', 0):.6f}\n"
        stats_str += f"Min: {stats.get('min', 0):.6f}\n"
        stats_str += f"Max: {stats.get('max', 0):.6f}\n"
        stats_str += f"Count: {stats.get('count', 0)}"
        
        self.stats_text.insert("1.0", stats_str)


class OscilloscopeWidget(BaseInstrumentWidget):
    """Control widget for oscilloscopes"""

    def create_widgets(self):
        # Title
        title = ctk.CTkLabel(self.frame, text=f"{self.instrument.name} Control",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)

        # Get capabilities
        caps = self.instrument.get_capabilities()
        channels = caps.get('channels', 4)

        # Create scrollable frame for all controls
        self.scrollable_frame = ctk.CTkScrollableFrame(self.frame, height=400)
        self.scrollable_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Channel controls
        self.create_channel_controls(channels)

        # Timebase controls
        self.create_timebase_controls()

        # Trigger controls
        self.create_trigger_controls()

        # Measurement controls
        self.create_measurement_controls()

        # Control buttons
        self.create_control_buttons()

    def create_channel_controls(self, channels):
        """Create controls for oscilloscope channels"""
        ch_main_frame = ctk.CTkFrame(self.scrollable_frame)
        ch_main_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(ch_main_frame, text="Channel Controls",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        self.channel_frames = {}
        for ch in range(1, channels + 1):
            self.create_single_channel_control(ch_main_frame, ch)

    def create_single_channel_control(self, parent, channel):
        """Create controls for a single channel"""
        ch_frame = ctk.CTkFrame(parent)
        ch_frame.pack(fill="x", padx=5, pady=2)

        # Channel header
        header_frame = ctk.CTkFrame(ch_frame)
        header_frame.pack(fill="x", padx=5, pady=2)

        # Channel enable checkbox
        enable_var = tk.BooleanVar()
        enable_cb = ctk.CTkCheckBox(header_frame, text=f"CH{channel}",
                                   variable=enable_var,
                                   command=lambda: self.set_channel_enabled(channel, enable_var.get()))
        enable_cb.pack(side="left", padx=5)

        # Channel label
        label_frame = ctk.CTkFrame(ch_frame)
        label_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(label_frame, text="Label:").pack(side="left", padx=5)
        label_entry = ctk.CTkEntry(label_frame, width=120, placeholder_text=f"CH{channel}")
        label_entry.pack(side="left", padx=5)
        ctk.CTkButton(label_frame, text="Set", width=50,
                     command=lambda: self.set_channel_label(channel, label_entry.get())).pack(side="left", padx=5)

        # Vertical scale
        v_scale_frame = ctk.CTkFrame(ch_frame)
        v_scale_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(v_scale_frame, text="V/div:").pack(side="left", padx=5)
        v_scale_entry = ctk.CTkEntry(v_scale_frame, width=80, placeholder_text="1.0")
        v_scale_entry.pack(side="left", padx=5)
        ctk.CTkButton(v_scale_frame, text="Set", width=50,
                     command=lambda: self.set_vertical_scale(channel, v_scale_entry.get())).pack(side="left", padx=5)

        # Vertical position
        v_pos_frame = ctk.CTkFrame(ch_frame)
        v_pos_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(v_pos_frame, text="V Pos:").pack(side="left", padx=5)
        v_pos_entry = ctk.CTkEntry(v_pos_frame, width=80, placeholder_text="0.0")
        v_pos_entry.pack(side="left", padx=5)
        ctk.CTkButton(v_pos_frame, text="Set", width=50,
                     command=lambda: self.set_vertical_position(channel, v_pos_entry.get())).pack(side="left", padx=5)

        # Coupling
        coupling_frame = ctk.CTkFrame(ch_frame)
        coupling_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(coupling_frame, text="Coupling:").pack(side="left", padx=5)
        coupling_var = tk.StringVar(value="DC")
        coupling_menu = ctk.CTkOptionMenu(coupling_frame, variable=coupling_var,
                                         values=["DC", "AC", "DCREJ"],
                                         command=lambda val: self.set_coupling(channel, val))
        coupling_menu.pack(side="left", padx=5)

        # Store references
        self.channel_frames[channel] = {
            'frame': ch_frame,
            'enable_var': enable_var,
            'v_scale_entry': v_scale_entry,
            'v_pos_entry': v_pos_entry,
            'coupling_var': coupling_var,
            'label_entry': label_entry
        }

    def create_timebase_controls(self):
        """Create timebase controls"""
        tb_frame = ctk.CTkFrame(self.scrollable_frame)
        tb_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(tb_frame, text="Timebase Controls",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Horizontal scale
        h_scale_frame = ctk.CTkFrame(tb_frame)
        h_scale_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(h_scale_frame, text="Time/div (s):").pack(side="left", padx=5)
        self.h_scale_entry = ctk.CTkEntry(h_scale_frame, width=100, placeholder_text="1e-3")
        self.h_scale_entry.pack(side="left", padx=5)
        ctk.CTkButton(h_scale_frame, text="Set", width=60,
                     command=lambda: self.set_horizontal_scale(self.h_scale_entry.get())).pack(side="left", padx=5)

        # Horizontal position
        h_pos_frame = ctk.CTkFrame(tb_frame)
        h_pos_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(h_pos_frame, text="Time Pos (s):").pack(side="left", padx=5)
        self.h_pos_entry = ctk.CTkEntry(h_pos_frame, width=100, placeholder_text="0.0")
        self.h_pos_entry.pack(side="left", padx=5)
        ctk.CTkButton(h_pos_frame, text="Set", width=60,
                     command=lambda: self.set_horizontal_position(self.h_pos_entry.get())).pack(side="left", padx=5)

    def create_trigger_controls(self):
        """Create trigger controls"""
        trig_frame = ctk.CTkFrame(self.scrollable_frame)
        trig_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(trig_frame, text="Trigger Controls",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Trigger source
        src_frame = ctk.CTkFrame(trig_frame)
        src_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(src_frame, text="Source:").pack(side="left", padx=5)
        self.trig_source_var = tk.StringVar(value="CH1")
        caps = self.instrument.get_capabilities()
        channels = caps.get('channels', 4)
        sources = [f"CH{i}" for i in range(1, channels + 1)] + ["EXT", "LINE"]
        trig_source_menu = ctk.CTkOptionMenu(src_frame, variable=self.trig_source_var,
                                           values=sources,
                                           command=lambda val: self.set_trigger_source(val))
        trig_source_menu.pack(side="left", padx=5)

        # Trigger level
        level_frame = ctk.CTkFrame(trig_frame)
        level_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(level_frame, text="Level (V):").pack(side="left", padx=5)
        self.trig_level_entry = ctk.CTkEntry(level_frame, width=100, placeholder_text="0.0")
        self.trig_level_entry.pack(side="left", padx=5)
        ctk.CTkButton(level_frame, text="Set", width=60,
                     command=lambda: self.set_trigger_level(self.trig_level_entry.get())).pack(side="left", padx=5)

        # Trigger slope
        slope_frame = ctk.CTkFrame(trig_frame)
        slope_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(slope_frame, text="Slope:").pack(side="left", padx=5)
        self.trig_slope_var = tk.StringVar(value="RISE")
        trig_slope_menu = ctk.CTkOptionMenu(slope_frame, variable=self.trig_slope_var,
                                          values=["RISE", "FALL", "EITHER"],
                                          command=lambda val: self.set_trigger_slope(val))
        trig_slope_menu.pack(side="left", padx=5)

        # Trigger mode
        mode_frame = ctk.CTkFrame(trig_frame)
        mode_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(mode_frame, text="Mode:").pack(side="left", padx=5)
        self.trig_mode_var = tk.StringVar(value="AUTO")
        trig_mode_menu = ctk.CTkOptionMenu(mode_frame, variable=self.trig_mode_var,
                                         values=["AUTO", "NORMAL"],
                                         command=lambda val: self.set_trigger_mode(val))
        trig_mode_menu.pack(side="left", padx=5)

    def create_measurement_controls(self):
        """Create measurement controls"""
        meas_frame = ctk.CTkFrame(self.scrollable_frame)
        meas_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(meas_frame, text="Measurements",
                    font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # Measurement display
        self.meas_text = ctk.CTkTextbox(meas_frame, height=120)
        self.meas_text.pack(fill="x", padx=5, pady=5)

    def create_control_buttons(self):
        """Create control buttons"""
        btn_frame = ctk.CTkFrame(self.scrollable_frame)
        btn_frame.pack(fill="x", padx=5, pady=5)

        # Acquisition controls
        ctk.CTkButton(btn_frame, text="Start", width=80,
                     command=self.start_acquisition).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Stop", width=80,
                     command=self.stop_acquisition).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Single", width=80,
                     command=self.single_acquisition).pack(side="left", padx=5)
        ctk.CTkButton(btn_frame, text="Continuous", width=100,
                     command=self.continuous_acquisition).pack(side="left", padx=5)

        # Measurement update
        ctk.CTkButton(btn_frame, text="Update Readings", width=120,
                     command=self.update_measurements).pack(side="left", padx=5)

        # Auto setup
        ctk.CTkButton(btn_frame, text="Auto Setup", width=100,
                     command=self.auto_setup).pack(side="left", padx=5)

        # Screen capture
        ctk.CTkButton(btn_frame, text="Screenshot", width=100,
                     command=self.capture_screenshot).pack(side="left", padx=5)

        # Display style controls
        display_frame = ctk.CTkFrame(self.scrollable_frame)
        display_frame.pack(fill="x", padx=5, pady=5)

        ctk.CTkLabel(display_frame, text="Display Style", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        style_frame = ctk.CTkFrame(display_frame)
        style_frame.pack(fill="x", padx=5, pady=2)

        ctk.CTkLabel(style_frame, text="View:").pack(side="left", padx=5)
        self.display_style_var = tk.StringVar(value="OVERLAY")
        display_style_menu = ctk.CTkOptionMenu(style_frame, variable=self.display_style_var,
                                             values=["OVERLAY", "STACKED"],
                                             command=lambda val: self.set_display_style(val))
        display_style_menu.pack(side="left", padx=5)

    # Control methods
    def set_channel_enabled(self, channel, enabled):
        """Enable/disable channel"""
        try:
            if hasattr(self.instrument, 'set_channel_enabled'):
                success = self.instrument.set_channel_enabled(channel, enabled)
                if success:
                    state = "ON" if enabled else "OFF"
                    self.logger.info(f"Set CH{channel} {state}")
                else:
                    self.logger.error(f"Failed to set CH{channel} state")
        except Exception as e:
            self.logger.error(f"Error setting channel state: {e}")

    def set_vertical_scale(self, channel, value_str):
        """Set vertical scale for channel"""
        try:
            value = float(value_str)
            # Try both method names for compatibility
            if hasattr(self.instrument, 'set_channel_scale'):
                success = self.instrument.set_channel_scale(channel, value)
            elif hasattr(self.instrument, 'set_vertical_scale'):
                success = self.instrument.set_vertical_scale(channel, value)
            else:
                self.logger.error("Vertical scale method not available")
                return

            if success:
                self.logger.info(f"Set CH{channel} vertical scale to {value} V/div")
            else:
                self.logger.error(f"Failed to set CH{channel} vertical scale")
        except ValueError:
            self.logger.error("Invalid vertical scale value")
        except Exception as e:
            self.logger.error(f"Error setting vertical scale: {e}")

    def set_vertical_position(self, channel, value_str):
        """Set vertical position for channel"""
        try:
            value = float(value_str)
            # Try both method names for compatibility
            if hasattr(self.instrument, 'set_channel_position'):
                success = self.instrument.set_channel_position(channel, value)
            elif hasattr(self.instrument, 'set_vertical_position'):
                success = self.instrument.set_vertical_position(channel, value)
            else:
                self.logger.error("Vertical position method not available")
                return

            if success:
                self.logger.info(f"Set CH{channel} vertical position to {value} V")
            else:
                self.logger.error(f"Failed to set CH{channel} vertical position")
        except ValueError:
            self.logger.error("Invalid vertical position value")
        except Exception as e:
            self.logger.error(f"Error setting vertical position: {e}")

    def set_coupling(self, channel, coupling):
        """Set channel coupling"""
        try:
            # Try both method names for compatibility
            if hasattr(self.instrument, 'set_channel_coupling'):
                success = self.instrument.set_channel_coupling(channel, coupling)
            elif hasattr(self.instrument, 'set_coupling'):
                success = self.instrument.set_coupling(channel, coupling)
            else:
                self.logger.error("Coupling method not available")
                return

            if success:
                self.logger.info(f"Set CH{channel} coupling to {coupling}")
            else:
                self.logger.error(f"Failed to set CH{channel} coupling")
        except Exception as e:
            self.logger.error(f"Error setting coupling: {e}")

    def set_horizontal_scale(self, value_str):
        """Set horizontal scale"""
        try:
            value = float(value_str)
            # Try both method names for compatibility
            if hasattr(self.instrument, 'set_timebase_scale'):
                success = self.instrument.set_timebase_scale(value)
            elif hasattr(self.instrument, 'set_horizontal_scale'):
                success = self.instrument.set_horizontal_scale(value)
            else:
                self.logger.error("Horizontal scale method not available")
                return

            if success:
                self.logger.info(f"Set horizontal scale to {value} s/div")
            else:
                self.logger.error("Failed to set horizontal scale")
        except ValueError:
            self.logger.error("Invalid horizontal scale value")
        except Exception as e:
            self.logger.error(f"Error setting horizontal scale: {e}")

    def set_horizontal_position(self, value_str):
        """Set horizontal position"""
        try:
            value = float(value_str)
            # Try both method names for compatibility
            if hasattr(self.instrument, 'set_timebase_position'):
                success = self.instrument.set_timebase_position(value)
            elif hasattr(self.instrument, 'set_horizontal_position'):
                success = self.instrument.set_horizontal_position(value)
            else:
                self.logger.error("Horizontal position method not available")
                return

            if success:
                self.logger.info(f"Set horizontal position to {value} s")
            else:
                self.logger.error("Failed to set horizontal position")
        except ValueError:
            self.logger.error("Invalid horizontal position value")
        except Exception as e:
            self.logger.error(f"Error setting horizontal position: {e}")

    def set_trigger_source(self, source):
        """Set trigger source"""
        try:
            if hasattr(self.instrument, 'set_trigger_source'):
                success = self.instrument.set_trigger_source(source)
                if success:
                    self.logger.info(f"Set trigger source to {source}")
                else:
                    self.logger.error("Failed to set trigger source")
        except Exception as e:
            self.logger.error(f"Error setting trigger source: {e}")

    def set_trigger_level(self, value_str):
        """Set trigger level"""
        try:
            value = float(value_str)
            if hasattr(self.instrument, 'set_trigger_level'):
                # Get current trigger source for proper command formatting
                source = None
                if hasattr(self.instrument, 'get_trigger_source'):
                    try:
                        source = self.instrument.get_trigger_source()
                    except:
                        pass

                success = self.instrument.set_trigger_level(value, source)
                if success:
                    self.logger.info(f"Set trigger level to {value} V")
                else:
                    self.logger.error("Failed to set trigger level")
        except ValueError:
            self.logger.error("Invalid trigger level value")
        except Exception as e:
            self.logger.error(f"Error setting trigger level: {e}")

    def set_trigger_slope(self, slope):
        """Set trigger slope"""
        try:
            if hasattr(self.instrument, 'set_trigger_slope'):
                success = self.instrument.set_trigger_slope(slope)
                if success:
                    self.logger.info(f"Set trigger slope to {slope}")
                else:
                    self.logger.error("Failed to set trigger slope")
        except Exception as e:
            self.logger.error(f"Error setting trigger slope: {e}")

    def set_trigger_mode(self, mode):
        """Set trigger mode"""
        try:
            if hasattr(self.instrument, 'set_trigger_mode'):
                success = self.instrument.set_trigger_mode(mode)
                if success:
                    self.logger.info(f"Set trigger mode to {mode}")
                else:
                    self.logger.error("Failed to set trigger mode")
        except Exception as e:
            self.logger.error(f"Error setting trigger mode: {e}")

    def set_channel_label(self, channel, label):
        """Set channel label"""
        try:
            if hasattr(self.instrument, 'set_channel_label'):
                success = self.instrument.set_channel_label(channel, label)
                if success:
                    self.logger.info(f"Set CH{channel} label to '{label}'")
                else:
                    self.logger.error(f"Failed to set CH{channel} label")
        except Exception as e:
            self.logger.error(f"Error setting channel label: {e}")

    def capture_screenshot(self):
        """Capture screenshot"""
        try:
            import datetime
            import os

            # Create screenshots directory if it doesn't exist
            screenshots_dir = "screenshots"
            if not os.path.exists(screenshots_dir):
                os.makedirs(screenshots_dir)

            # Generate filename with timestamp
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(screenshots_dir, f"MSO_screenshot_{timestamp}.png")

            # Convert to absolute path for the instrument
            abs_filename = os.path.abspath(filename)

            if hasattr(self.instrument, 'save_screenshot'):
                success = self.instrument.save_screenshot(abs_filename)
                if success:
                    self.logger.info(f"Screenshot saved to {filename}")
                    # Show success message in GUI
                    import tkinter.messagebox as msgbox
                    msgbox.showinfo("Screenshot", f"Screenshot saved to:\n{filename}")
                else:
                    self.logger.error("Failed to save screenshot")
                    import tkinter.messagebox as msgbox
                    msgbox.showerror("Error", "Failed to save screenshot")
        except Exception as e:
            self.logger.error(f"Error capturing screenshot: {e}")
            import tkinter.messagebox as msgbox
            msgbox.showerror("Error", f"Error capturing screenshot: {e}")

    def set_display_style(self, style):
        """Set display style"""
        try:
            if hasattr(self.instrument, 'set_display_style'):
                success = self.instrument.set_display_style(style)
                if success:
                    self.logger.info(f"Set display style to {style}")
                else:
                    self.logger.error("Failed to set display style")
        except Exception as e:
            self.logger.error(f"Error setting display style: {e}")

    def start_acquisition(self):
        """Start acquisition"""
        try:
            if hasattr(self.instrument, 'start_acquisition'):
                success = self.instrument.start_acquisition()
                if success:
                    self.logger.info("Started acquisition")
                else:
                    self.logger.error("Failed to start acquisition")
        except Exception as e:
            self.logger.error(f"Error starting acquisition: {e}")

    def stop_acquisition(self):
        """Stop acquisition"""
        try:
            if hasattr(self.instrument, 'stop_acquisition'):
                success = self.instrument.stop_acquisition()
                if success:
                    self.logger.info("Stopped acquisition")
                else:
                    self.logger.error("Failed to stop acquisition")
        except Exception as e:
            self.logger.error(f"Error stopping acquisition: {e}")

    def single_acquisition(self):
        """Single acquisition"""
        try:
            if hasattr(self.instrument, 'single_acquisition'):
                success = self.instrument.single_acquisition()
                if success:
                    self.logger.info("Single acquisition triggered")
                else:
                    self.logger.error("Failed to trigger single acquisition")
        except Exception as e:
            self.logger.error(f"Error with single acquisition: {e}")

    def continuous_acquisition(self):
        """Continuous acquisition"""
        try:
            if hasattr(self.instrument, 'continuous_acquisition'):
                success = self.instrument.continuous_acquisition()
                if success:
                    self.logger.info("Continuous acquisition mode set")
                else:
                    self.logger.error("Failed to set continuous acquisition")
        except Exception as e:
            self.logger.error(f"Error with continuous acquisition: {e}")

    def auto_setup(self):
        """Auto setup"""
        try:
            if hasattr(self.instrument, 'auto_setup'):
                success = self.instrument.auto_setup()
                if success:
                    self.logger.info("Auto setup completed")
                    self.update_measurements()
                else:
                    self.logger.error("Failed to perform auto setup")
        except Exception as e:
            self.logger.error(f"Error with auto setup: {e}")

    def update_measurements(self):
        """Update measurement display"""
        try:
            if hasattr(self.instrument, 'get_measurements'):
                measurements = self.instrument.get_measurements()
                self.display_measurements(measurements)
        except Exception as e:
            self.logger.error(f"Error updating measurements: {e}")

    def display_measurements(self, measurements):
        """Display measurements"""
        self.meas_text.delete("1.0", tk.END)

        meas_str = f"Oscilloscope Measurements:\n"
        meas_str += f"Timestamp: {measurements.get('timestamp', 'N/A')}\n\n"

        if 'channels' in measurements:
            for ch, ch_meas in measurements['channels'].items():
                meas_str += f"Channel {ch}:\n"
                meas_str += f"  Peak-to-Peak: {ch_meas.get('peak_to_peak', 0):.3f} V\n"
                meas_str += f"  RMS: {ch_meas.get('rms', 0):.3f} V\n"
                meas_str += f"  Frequency: {ch_meas.get('frequency', 0):.3f} Hz\n"
                meas_str += f"  Mean: {ch_meas.get('mean', 0):.3f} V\n\n"

        self.meas_text.insert("1.0", meas_str)


# Widget factory
def create_instrument_widget(parent, instrument, update_callback=None):
    """Create appropriate widget for instrument type"""

    caps = instrument.get_capabilities()
    inst_type = caps.get('type', 'unknown')

    if inst_type == 'power_supply':
        return PowerSupplyWidget(parent, instrument, update_callback)
    elif inst_type == 'multimeter':
        return MultimeterWidget(parent, instrument, update_callback)
    elif inst_type in ['oscilloscope', 'tektronix_mso58b']:
        return OscilloscopeWidget(parent, instrument, update_callback)
    else:
        # Return base widget for unknown types
        return BaseInstrumentWidget(parent, instrument, update_callback)
