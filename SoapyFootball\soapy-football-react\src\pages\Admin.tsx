import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Booking, Slot } from '../types';

const Admin = () => {
  const navigate = useNavigate();
  const { user, isAdmin } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [slots, setSlots] = useState<Slot[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalAmount, setTotalAmount] = useState(0);
  const [totalToDate, setTotalToDate] = useState(0);
  const [additionalPayment, setAdditionalPayment] = useState<{ [key: string]: number }>({});
  const [activeTab, setActiveTab] = useState<'daily' | 'monthly'>('daily');

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    if (!isAdmin) {
      navigate('/');
      return;
    }

    fetchData();
  }, [user, isAdmin, navigate, selectedDate, activeTab]);

  const fetchData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Format date for query
      const formattedDate = selectedDate.toISOString().split('T')[0];
      
      // Fetch bookings for the selected date
      let query = supabase
        .from('bookings')
        .select('*');
      
      if (activeTab === 'daily') {
        query = query.eq('date', formattedDate);
      } else if (activeTab === 'monthly') {
        const year = selectedDate.getFullYear();
        const month = selectedDate.getMonth() + 1;
        const startDate = `${year}-${month.toString().padStart(2, '0')}-01`;
        const endDate = new Date(year, month, 0).toISOString().split('T')[0];
        query = query.gte('date', startDate).lte('date', endDate);
      }
      
      const { data: bookingsData, error: bookingsError } = await query;
      
      if (bookingsError) throw bookingsError;
      
      setBookings(bookingsData || []);
      
      // Calculate total amount
      const completedBookings = bookingsData?.filter(b => b.payment_status === 'completed') || [];
      const total = completedBookings.reduce((sum, booking) => {
        return sum + booking.amount + (booking.additional_payment || 0);
      }, 0);
      
      setTotalAmount(total);
      
      // Generate all slots from 7 AM to 10 PM
      if (activeTab === 'daily') {
        const allSlots: Slot[] = [];
        for (let hour = 7; hour <= 22; hour++) {
          const startTime = `${hour.toString().padStart(2, '0')}:00`;
          const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;
          const timeSlot = `${startTime} - ${endTime}`;
          
          // Check if slot is booked
          const booking = bookingsData?.find(b => b.slot === timeSlot);
          
          allSlots.push({
            id: `slot-${hour}`,
            time: timeSlot,
            status: booking ? 'booked' : 'available',
            booking_id: booking?.id
          });
        }
        
        setSlots(allSlots);
      }
      
      // Fetch total amount to date
      const { data: allBookings, error: allBookingsError } = await supabase
        .from('bookings')
        .select('*')
        .eq('payment_status', 'completed');
      
      if (allBookingsError) throw allBookingsError;
      
      const totalToDate = allBookings?.reduce((sum, booking) => {
        return sum + booking.amount + (booking.additional_payment || 0);
      }, 0);
      
      setTotalToDate(totalToDate || 0);
    } catch (error) {
      console.error('Error fetching data:', error);
      setError('Failed to load data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReserveSlot = async (slot: Slot) => {
    if (slot.status !== 'available') return;
    
    try {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      
      // Create a reserved booking
      const { data, error } = await supabase
        .from('bookings')
        .insert([
          {
            user_id: user?.id,
            date: formattedDate,
            slot: slot.time,
            name: 'Reserved',
            phone: 'Reserved',
            payment_status: 'completed',
            amount: 0,
            additional_payment: 0
          }
        ])
        .select();
      
      if (error) throw error;
      
      // Refresh data
      fetchData();
    } catch (error) {
      console.error('Error reserving slot:', error);
      setError('Failed to reserve slot. Please try again.');
    }
  };

  const handleCancelBooking = async (bookingId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);
      
      if (error) throw error;
      
      // Refresh data
      fetchData();
    } catch (error) {
      console.error('Error canceling booking:', error);
      setError('Failed to cancel booking. Please try again.');
    }
  };

  const handleAdditionalPayment = async (bookingId: string) => {
    const amount = additionalPayment[bookingId];
    if (!amount) return;
    
    try {
      const booking = bookings.find(b => b.id === bookingId);
      if (!booking) return;
      
      const { error } = await supabase
        .from('bookings')
        .update({
          additional_payment: (booking.additional_payment || 0) + amount
        })
        .eq('id', bookingId);
      
      if (error) throw error;
      
      // Reset input and refresh data
      setAdditionalPayment(prev => ({ ...prev, [bookingId]: 0 }));
      fetchData();
    } catch (error) {
      console.error('Error adding additional payment:', error);
      setError('Failed to add additional payment. Please try again.');
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-dark">Admin Dashboard</h1>
            <button 
              onClick={() => navigate('/')}
              className="btn bg-gray-200 hover:bg-gray-300 text-gray-800"
            >
              Back to Home
            </button>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}
          
          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-600 mb-2">Today's Revenue</h3>
              <p className="text-3xl font-bold text-primary">₹{totalAmount}</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-600 mb-2">Total Revenue</h3>
              <p className="text-3xl font-bold text-primary">₹{totalToDate}</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-600 mb-2">Total Bookings</h3>
              <p className="text-3xl font-bold text-primary">{bookings.length}</p>
            </div>
          </div>
          
          {/* Tabs */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
            <div className="flex border-b">
              <button
                className={`px-6 py-3 font-medium ${
                  activeTab === 'daily'
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setActiveTab('daily')}
              >
                Daily View
              </button>
              <button
                className={`px-6 py-3 font-medium ${
                  activeTab === 'monthly'
                    ? 'text-primary border-b-2 border-primary'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                onClick={() => setActiveTab('monthly')}
              >
                Monthly View
              </button>
            </div>
            
            <div className="p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold mb-4">
                  {activeTab === 'daily' ? 'Select Date' : 'Select Month'}
                </h2>
                <DatePicker
                  selected={selectedDate}
                  onChange={setSelectedDate}
                  dateFormat={activeTab === 'daily' ? 'MMMM d, yyyy' : 'MMMM yyyy'}
                  showMonthYearPicker={activeTab === 'monthly'}
                  className="w-full p-2 border rounded"
                />
              </div>
              
              {isLoading ? (
                <div className="flex justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  {activeTab === 'daily' && (
                    <div className="mb-8">
                      <h2 className="text-xl font-semibold mb-4">Slots</h2>
                      <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-4">
                        {slots.map((slot) => (
                          <div
                            key={slot.id}
                            className={`p-3 rounded-lg text-center ${
                              slot.status === 'available'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            <div className="font-medium mb-2">{slot.time}</div>
                            {slot.status === 'available' ? (
                              <button
                                onClick={() => handleReserveSlot(slot)}
                                className="text-xs bg-primary text-white px-2 py-1 rounded"
                              >
                                Reserve
                              </button>
                            ) : (
                              <button
                                onClick={() => handleCancelBooking(slot.booking_id!)}
                                className="text-xs bg-red-500 text-white px-2 py-1 rounded"
                              >
                                Cancel
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h2 className="text-xl font-semibold mb-4">Bookings</h2>
                    
                    {bookings.length === 0 ? (
                      <div className="text-center py-8 text-gray-500">
                        No bookings found for the selected {activeTab === 'daily' ? 'date' : 'month'}.
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Time
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Customer
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Additional
                              </th>
                              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {bookings.map((booking) => (
                              <tr key={booking.id}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  {formatDate(booking.date)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  {booking.slot}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="font-medium">{booking.name}</div>
                                  <div className="text-sm text-gray-500">{booking.phone}</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    booking.payment_status === 'completed'
                                      ? 'bg-green-100 text-green-800'
                                      : booking.payment_status === 'pending'
                                      ? 'bg-yellow-100 text-yellow-800'
                                      : 'bg-red-100 text-red-800'
                                  }`}>
                                    {booking.payment_status}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  ₹{booking.amount}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center space-x-2">
                                    <span>₹{booking.additional_payment || 0}</span>
                                    <input
                                      type="number"
                                      min="0"
                                      value={additionalPayment[booking.id] || ''}
                                      onChange={(e) => setAdditionalPayment({
                                        ...additionalPayment,
                                        [booking.id]: parseInt(e.target.value) || 0
                                      })}
                                      className="w-16 p-1 border rounded"
                                    />
                                    <button
                                      onClick={() => handleAdditionalPayment(booking.id)}
                                      className="text-xs bg-primary text-white px-2 py-1 rounded"
                                    >
                                      Add
                                    </button>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <button
                                    onClick={() => handleCancelBooking(booking.id)}
                                    className="text-xs bg-red-500 text-white px-2 py-1 rounded"
                                  >
                                    Cancel
                                  </button>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Admin;
