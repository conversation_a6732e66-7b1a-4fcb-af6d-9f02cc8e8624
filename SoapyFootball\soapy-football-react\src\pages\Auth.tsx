import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Auth as SupabaseAuth } from '@supabase/auth-ui-react';
import { ThemeSupa } from '@supabase/auth-ui-shared';
import { supabase } from '../utils/supabaseClient';
import { motion } from 'framer-motion';

const Auth = () => {
  const navigate = useNavigate();
  const [authMode, setAuthMode] = useState<'sign_in' | 'sign_up'>('sign_in');
  
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-md w-full space-y-8 bg-white p-8 rounded-xl shadow-lg"
      >
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {authMode === 'sign_in' ? 'Sign in to your account' : 'Create a new account'}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {authMode === 'sign_in' 
              ? 'Sign in to book your soapy football slot' 
              : 'Register to start booking soapy football slots'}
          </p>
        </div>
        
        <div className="mt-8">
          <SupabaseAuth
            supabaseClient={supabase}
            appearance={{ 
              theme: ThemeSupa,
              variables: {
                default: {
                  colors: {
                    brand: '#0ea5e9',
                    brandAccent: '#0284c7',
                  },
                  radii: {
                    borderRadiusButton: '0.5rem',
                    inputBorderRadius: '0.5rem',
                  },
                },
              },
            }}
            providers={[]}
            view={authMode}
            redirectTo={window.location.origin + '/booking'}
          />
        </div>
        
        <div className="text-center mt-4">
          <button
            onClick={() => setAuthMode(authMode === 'sign_in' ? 'sign_up' : 'sign_in')}
            className="text-primary hover:text-primary/80 font-medium"
          >
            {authMode === 'sign_in' 
              ? 'Don\'t have an account? Sign up' 
              : 'Already have an account? Sign in'}
          </button>
        </div>
        
        <div className="text-center mt-4">
          <button
            onClick={() => navigate('/')}
            className="text-gray-500 hover:text-gray-700 font-medium"
          >
            Back to Home
          </button>
        </div>
      </motion.div>
    </div>
  );
};

export default Auth;
