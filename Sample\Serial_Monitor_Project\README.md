# Serial Monitor Project

A professional serial communication application with beautiful purple theme and modern GUI.

## Project Structure

```
Serial_Monitor_Project/
├── Source_Code/           # Main application source code
│   ├── serial_monitor_gui.py    # Main application file
│   └── purple_theme.json        # Custom purple theme
│
├── Assets/                # Icons and graphics
│   ├── serial_monitor_icon.ico  # Application icon
│   ├── serial_monitor_icon.png  # Icon preview
│   └── create_icon.py           # Icon creation script
│
├── Build_Tools/           # Build and compilation tools
│   ├── build_exe.py             # Main executable builder
│   ├── create_professional_installer.py  # Installer creator
│   ├── requirements_build.txt   # Build dependencies
│   ├── dist/                    # Built executable
│   └── build/                   # Build artifacts
│
├── Installer/             # Professional installer package
│   └── SerialMonitor_Installer/ # Complete installer
│
├── Distribution/          # Ready-to-distribute packages
│   ├── SerialMonitor_v1.0_*.zip # Complete distribution package
│   └── SerialMonitor_Portable/  # Portable version
│
└── Documentation/         # All documentation
    ├── INSTALLATION_GUIDE.md    # Installation instructions
    ├── serial_monitor_README.md # Application documentation
    └── DISTRIBUTION_README.txt  # Distribution guide
```

## Quick Start

### For Development
1. Navigate to `Source_Code/`
2. Run: `python serial_monitor_gui.py`

### For Building
1. Navigate to `Build_Tools/`
2. Run: `python build_exe.py`

### For Distribution
1. Use files in `Distribution/` folder
2. Professional installer in `Installer/` folder

## Features

- ✅ Beautiful purple theme
- ✅ COM port auto-detection
- ✅ Multiple baud rates (9600-921600)
- ✅ Real-time serial communication
- ✅ Arduino IDE-style interface
- ✅ Professional Windows installer
- ✅ Portable executable option
- ✅ Custom vector icons

## Installation Options

### Option 1: Professional Installer (Recommended)
- Navigate to `Installer/SerialMonitor_Installer/`
- Right-click `Setup.bat` and "Run as administrator"
- Follow installation wizard

### Option 2: Portable Version
- Navigate to `Distribution/SerialMonitor_Portable/`
- Run `SerialMonitor.exe` directly

### Option 3: Distribution Package
- Use `Distribution/SerialMonitor_v1.0_*.zip`
- Contains both installer and portable versions

## System Requirements

- Windows 10/11 (64-bit)
- Administrator privileges (for installer)
- 50MB free disk space
- COM port access permissions

## Development

### Dependencies
```bash
pip install -r Build_Tools/requirements_build.txt
```

### Building Executable
```bash
cd Build_Tools
python build_exe.py
```

### Creating Installer
```bash
cd Build_Tools
python create_professional_installer.py
```

---
Serial Monitor v1.0 - Professional Serial Communication Tool
