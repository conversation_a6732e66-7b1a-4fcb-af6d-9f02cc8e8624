<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football - Turf Booking Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #0284c7;
        }

        .btn-secondary {
            background-color: #10b981;
        }

        .btn-secondary:hover {
            background-color: #059669;
        }

        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #0ea5e9;
        }

        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero section */
        .hero {
            padding: 80px 0;
            background: linear-gradient(to bottom, rgba(14, 165, 233, 0.1), #f9fafb);
            text-align: center;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .hero h1 span {
            color: #0ea5e9;
        }

        .hero p {
            font-size: 1.2rem;
            color: #4b5563;
            margin-bottom: 30px;
        }

        .hero-btn {
            padding: 12px 30px;
            font-size: 1.1rem;
        }

        /* Features section */
        .features {
            padding: 80px 0;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 2rem;
            color: #1e293b;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            color: #0ea5e9;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        /* Testimonials section */
        .testimonials {
            padding: 80px 0;
            background-color: #f9fafb;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .testimonial-card {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .testimonial-text {
            font-style: italic;
            margin-bottom: 20px;
        }

        .testimonial-author {
            font-weight: 600;
            color: #1e293b;
        }

        /* FAQ section */
        .faq {
            padding: 80px 0;
            background-color: white;
        }

        .faq-item {
            margin-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 20px;
        }

        .faq-question {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .faq-answer {
            color: #4b5563;
            display: none;
        }

        .faq-answer.active {
            display: block;
        }

        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
            margin-top: auto;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .footer-col ul {
            list-style: none;
        }

        .footer-col ul li {
            margin-bottom: 10px;
        }

        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-col a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            nav ul.active {
                display: flex;
            }

            .hero h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">Soapy Football</a>
            <button class="mobile-menu-btn">☰</button>
            <nav>
                <ul id="menu">
                    <li><a href="#">Home</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="history.html">My Bookings</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="booking.html" class="nav-btn">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container hero-content">
            <h1><span>Soapy Football</span> Turf Booking</h1>
            <p>Experience the thrill of playing football on our premium soapy turf. Perfect for friendly matches, corporate events, and weekend fun with friends and family.</p>
            <a href="booking.html" class="btn hero-btn">Book Your Slot Now</a>
        </div>
    </section>

    <section class="features">
        <div class="container">
            <h2 class="section-title">Why Choose Us</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">⚽</div>
                    <h3>Premium Turf</h3>
                    <p>Our state-of-the-art soapy turf provides the perfect playing surface for an exciting football experience.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📅</div>
                    <h3>Easy Booking</h3>
                    <p>Book your preferred slot online in just a few clicks. No more waiting in queues or making phone calls.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🏆</div>
                    <h3>Tournament Support</h3>
                    <p>We provide complete support for organizing tournaments, including referees, equipment, and refreshments.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="testimonials">
        <div class="container">
            <h2 class="section-title">What Our Customers Say</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <p class="testimonial-text">"The soapy turf is amazing! We had a great time playing with our office team. Will definitely book again."</p>
                    <p class="testimonial-author">- Rahul Sharma</p>
                </div>
                <div class="testimonial-card">
                    <p class="testimonial-text">"The booking process was super easy, and the facilities are top-notch. Highly recommended!"</p>
                    <p class="testimonial-author">- Priya Patel</p>
                </div>
                <div class="testimonial-card">
                    <p class="testimonial-text">"We organized our college tournament here, and the staff was very helpful. Great experience overall."</p>
                    <p class="testimonial-author">- Amit Singh</p>
                </div>
            </div>
        </div>
    </section>

    <section class="faq">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="faq-list">
                <div class="faq-item">
                    <div class="faq-question">How do I book a slot? <span>+</span></div>
                    <div class="faq-answer">You can book a slot online through our website by selecting your preferred date and time, or you can call us directly.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">What is the cancellation policy? <span>+</span></div>
                    <div class="faq-answer">Cancellations made 24 hours before the scheduled time are eligible for a full refund. Late cancellations will be charged 50% of the booking amount.</div>
                </div>
                <div class="faq-item">
                    <div class="faq-question">Do you provide equipment? <span>+</span></div>
                    <div class="faq-answer">Yes, we provide footballs, bibs, and other basic equipment. You can also bring your own if you prefer.</div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best turf booking platform for football enthusiasts.</p>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9876543210</li>
                        <li>Address: 123 Sports Complex, Mumbai</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const menu = document.getElementById('menu');

        mobileMenuBtn.addEventListener('click', () => {
            menu.classList.toggle('active');
        });

        // FAQ toggle
        const faqQuestions = document.querySelectorAll('.faq-question');

        faqQuestions.forEach(question => {
            question.addEventListener('click', () => {
                const answer = question.nextElementSibling;
                answer.classList.toggle('active');

                const icon = question.querySelector('span');
                icon.textContent = answer.classList.contains('active') ? '-' : '+';
            });
        });
    </script>
</body>
</html>
