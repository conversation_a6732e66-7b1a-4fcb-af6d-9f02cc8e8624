# Soapy Football Turf Booking Platform

A beautiful and functional web application for booking soapy football turf slots. Built with React, Tailwind CSS, Supabase, and Razorpay.

## Features

- Beautiful vector graphics and animations
- User authentication with Supabase
- Dynamic booking system with date selection
- Real-time slot availability
- Secure payment integration with Razorpay
- Booking history for users
- Admin dashboard with booking management and financial reporting

## Tech Stack

- **Frontend**: React, TypeScript, Tailwind CSS, Framer Motion
- **Authentication**: Supabase Auth
- **Database**: Supabase PostgreSQL
- **Payment**: Razorpay
- **Deployment**: Netlify

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or yarn
- Supabase account
- Razorpay account

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/soapy-football.git
   cd soapy-football
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Create a `.env` file in the root directory with your Supabase and Razorpay credentials:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

### Setting up Supabase

1. Create a new Supabase project
2. Run the SQL script in `supabase/schema.sql` to set up the database schema
3. Set up authentication with phone number and email providers
4. Update your `.env` file with the Supabase URL and anon key

### Setting up Razorpay

1. Create a Razorpay account
2. Get your API key and secret
3. Update the `.env` file with your Razorpay key ID

## Deployment

### Deploying to Netlify

1. Create a new site on Netlify
2. Connect your GitHub repository
3. Set the build command to `npm run build`
4. Set the publish directory to `dist`
5. Add your environment variables in the Netlify dashboard
6. Deploy your site

## Project Structure

```
soapy-football/
├── public/
├── src/
│   ├── assets/           # Images, SVGs, and other static assets
│   ├── components/       # Reusable UI components
│   ├── context/          # React context providers
│   ├── hooks/            # Custom React hooks
│   ├── pages/            # Page components
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions
│   ├── App.tsx           # Main App component
│   ├── index.css         # Global styles
│   └── main.tsx          # Entry point
├── supabase/             # Supabase configuration and schema
├── .env                  # Environment variables
├── index.html            # HTML template
├── package.json          # Project dependencies
├── tailwind.config.js    # Tailwind CSS configuration
└── tsconfig.json         # TypeScript configuration
```

## Admin Setup

To set up an admin user:

1. Register a user through the application
2. Get the user ID from the Supabase dashboard
3. Run the following SQL query in the Supabase SQL editor:
   ```sql
   INSERT INTO admins (user_id) VALUES ('your-user-id');
   ```
