[Setup]
; Basic application information
AppName=Serial Monitor
AppVersion=1.0
AppPublisher=Your Company
AppPublisherURL=https://yourwebsite.com
AppSupportURL=https://yourwebsite.com/support
AppUpdatesURL=https://yourwebsite.com/updates
DefaultDirName={autopf}\Serial Monitor
DefaultGroupName=Serial Monitor
AllowNoIcons=yes
LicenseFile=
InfoBeforeFile=
InfoAfterFile=
OutputDir=installer_output
OutputBaseFilename=SerialMonitor_Setup
SetupIconFile=serial_monitor_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; Visual customization
WizardImageFile=
WizardSmallImageFile=
WizardImageStretch=no
WizardImageBackColor=clBlack

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1; Check: not IsAdminInstallMode

[Files]
Source: "dist\SerialMonitor.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "serial_monitor_icon.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "INSTALLATION_GUIDE.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "serial_monitor_icon.png"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Serial Monitor"; Filename: "{app}\SerialMonitor.exe"; IconFilename: "{app}\serial_monitor_icon.ico"
Name: "{group}\{cm:ProgramOnTheWeb,Serial Monitor}"; Filename: "https://yourwebsite.com"
Name: "{group}\{cm:UninstallProgram,Serial Monitor}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Serial Monitor"; Filename: "{app}\SerialMonitor.exe"; IconFilename: "{app}\serial_monitor_icon.ico"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Serial Monitor"; Filename: "{app}\SerialMonitor.exe"; IconFilename: "{app}\serial_monitor_icon.ico"; Tasks: quicklaunchicon

[Registry]
Root: HKCR; Subkey: ".serial"; ValueType: string; ValueName: ""; ValueData: "SerialMonitorFile"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "SerialMonitorFile"; ValueType: string; ValueName: ""; ValueData: "Serial Monitor File"; Flags: uninsdeletekey
Root: HKCR; Subkey: "SerialMonitorFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\serial_monitor_icon.ico"
Root: HKCR; Subkey: "SerialMonitorFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\SerialMonitor.exe"" ""%1"""

[Run]
Filename: "{app}\SerialMonitor.exe"; Description: "{cm:LaunchProgram,Serial Monitor}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
Type: filesandordirs; Name: "{app}"

[Code]
procedure InitializeWizard;
begin
  WizardForm.LicenseAcceptedRadio.Checked := True;
end;

function InitializeSetup(): Boolean;
begin
  Result := True;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    // Add any post-installation tasks here
  end;
end;
