"""
Serial Instrument Implementation
For instruments connected via RS232/USB Serial
"""

import serial
import time
from typing import Optional
from .base_instrument import BaseInstrument

class SerialInstrument(BaseInstrument):
    """Instrument connected via Serial/RS232"""
    
    def __init__(self, name: str, port: str, baudrate: int = 9600, **kwargs):
        super().__init__(name, port, **kwargs)
        self.port = port
        self.baudrate = baudrate
        self.timeout = kwargs.get('timeout', 1.0)
        self.termination = kwargs.get('termination', '\r\n')
        self.serial_conn: Optional[serial.Serial] = None
        
    def connect(self) -> bool:
        """Connect to serial instrument"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            self.connected = True
            self.logger.info(f"Connected to {self.name} on {self.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to {self.name}: {e}")
            self.connected = False
            return False
    
    def disconnect(self) -> bool:
        """Disconnect from serial instrument"""
        try:
            if self.serial_conn and self.serial_conn.is_open:
                self.serial_conn.close()
            self.connected = False
            self.logger.info(f"Disconnected from {self.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from {self.name}: {e}")
            return False
    
    def write(self, command: str) -> bool:
        """Send command to instrument"""
        if not self.connected or not self.serial_conn:
            raise RuntimeError("Instrument not connected")
        
        try:
            cmd_bytes = (command + self.termination).encode('utf-8')
            self.serial_conn.write(cmd_bytes)
            self.serial_conn.flush()
            self.logger.debug(f"Sent: {command}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending command '{command}': {e}")
            return False
    
    def query(self, command: str) -> str:
        """Send query and return response"""
        if not self.write(command):
            return ""
        
        try:
            # Wait a bit for response
            time.sleep(0.1)
            
            # Read response
            response = self.serial_conn.readline().decode('utf-8').strip()
            self.logger.debug(f"Query '{command}' -> '{response}'")
            return response
            
        except Exception as e:
            self.logger.error(f"Error reading response for '{command}': {e}")
            return ""
