<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football - Turf Booking Platform</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
        }

        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #0284c7;
        }

        .btn-secondary {
            background-color: #10b981;
        }

        .btn-secondary:hover {
            background-color: #059669;
        }

        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
            width: 100%;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }

        nav {
            flex: 1;
            display: flex;
            justify-content: flex-end;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: all 0.3s;
            background-color: #f3f4f6;
            padding: 8px 16px;
            border-radius: 6px;
            display: inline-block;
        }

        nav a:hover {
            color: #0ea5e9;
            background-color: #e5e7eb;
        }

        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 5px;
        }

        /* Page container */
        .page {
            display: none;
            min-height: calc(100vh - 70px);
        }

        .page.active {
            display: block;
        }

        /* Hero section */
        .hero {
            padding: 80px 0;
            background: linear-gradient(to bottom, rgba(14, 165, 233, 0.1), #f9fafb);
            text-align: center;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
        }

        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .hero h1 span {
            color: #0ea5e9;
        }

        .hero p {
            font-size: 1.2rem;
            color: #4b5563;
            margin-bottom: 30px;
        }

        .hero-btn {
            padding: 12px 30px;
            font-size: 1.1rem;
        }

        .hero-image {
            max-width: 500px;
            margin: 0 auto 30px;
        }

        /* Features section */
        .features {
            padding: 80px 0;
            background-color: white;
        }

        .section-title {
            text-align: center;
            margin-bottom: 50px;
            font-size: 2rem;
            color: #1e293b;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            color: #0ea5e9;
            margin-bottom: 20px;
        }

        .feature-card h3 {
            font-size: 1.3rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .footer-col ul {
            list-style: none;
        }

        .footer-col ul li {
            margin-bottom: 10px;
        }

        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-col a:hover {
            color: white;
        }

        .social-links {
            margin-top: 20px;
        }

        .social-link {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
            margin-bottom: 10px;
        }

        .social-link:hover {
            color: white;
        }

        .map-container {
            margin: 30px 0;
            border-radius: 8px;
            overflow: hidden;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            nav {
                position: static;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                z-index: 101;
            }

            nav ul.active {
                display: flex;
            }

            nav ul li {
                margin: 10px 0;
                width: 100%;
            }

            nav a {
                display: block;
                width: 100%;
                text-align: center;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .header-container {
                padding: 0 15px;
            }
        }

        @media (max-width: 480px) {
            .logo {
                font-size: 1.2rem;
            }

            .mobile-menu-btn {
                font-size: 1.2rem;
            }
        }
    /* Booking page styles */
        .booking-section {
            padding: 60px 0;
        }

        .booking-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .date-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .date-option {
            min-width: 100px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .date-option:hover {
            border-color: #0ea5e9;
        }

        .date-option.selected {
            background-color: #0ea5e9;
            color: white;
            border-color: #0ea5e9;
        }

        .date-day {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .date-date {
            font-size: 0.9rem;
            color: #6b7280;
        }

        .date-option.selected .date-date {
            color: #e5e7eb;
        }

        .today-date {
            border: 2px solid #10b981;
            position: relative;
        }

        .today-marker {
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #10b981;
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .time-slots-container {
            margin-bottom: 30px;
        }

        .time-slots-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .time-slots-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 10px;
        }

        .time-slot {
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .time-slot:hover:not(.booked) {
            border-color: #0ea5e9;
        }

        .time-slot.selected {
            background-color: #10b981;
            color: white;
            border-color: #10b981;
        }

        .time-slot.booked {
            background-color: #fee2e2;
            color: #b91c1c;
            border-color: #fecaca;
            cursor: not-allowed;
        }

        .booking-form {
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e293b;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Inter', sans-serif;
        }

        .form-group input:focus {
            outline: none;
            border-color: #0ea5e9;
        }

        .booking-summary {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .summary-title {
            font-size: 1.2rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 500;
            color: #4b5563;
        }

        .summary-value {
            font-weight: 600;
            color: #1e293b;
        }

        .total-amount {
            font-size: 1.2rem;
            font-weight: 700;
            color: #0ea5e9;
        }

        .book-btn {
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
        }

        /* History page styles */
        .history-section {
            padding: 60px 0;
        }

        .history-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .history-form {
            margin-bottom: 30px;
        }

        .booking-list {
            margin-top: 20px;
        }

        .booking-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .booking-date {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
        }

        .booking-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-upcoming {
            background-color: #e0f2fe;
            color: #0369a1;
        }

        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }

        .status-cancelled {
            background-color: #fee2e2;
            color: #b91c1c;
        }

        .booking-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .booking-detail {
            margin-bottom: 10px;
        }

        .detail-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 5px;
        }

        .detail-value {
            font-weight: 500;
            color: #1e293b;
        }

        .no-bookings {
            text-align: center;
            padding: 40px 0;
            color: #6b7280;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .success-icon, .alert-icon, .confirm-icon, .prompt-icon, .error-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
        }

        .success-icon {
            color: white;
            background-color: #10b981;
        }

        .alert-icon {
            color: white;
            background-color: #0ea5e9;
        }

        .error-icon {
            color: white;
            background-color: #ef4444;
        }

        .confirm-icon, .prompt-icon {
            color: white;
            background-color: #0ea5e9;
        }

        .modal-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .modal-text {
            margin-bottom: 25px;
            color: #4b5563;
        }

        .modal-btn {
            padding: 10px 20px;
        }

        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 10px;
        }

        .modal-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Inter', sans-serif;
            margin-bottom: 20px;
        }

        /* WhatsApp floating button */
        .whatsapp-float {
            position: fixed;
            width: 60px;
            height: 60px;
            bottom: 20px;
            right: 20px;
            background-color: #25d366;
            color: #FFF;
            border-radius: 50px;
            text-align: center;
            font-size: 30px;
            box-shadow: 2px 2px 3px #999;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .whatsapp-float:hover {
            background-color: #128C7E;
            transform: scale(1.1);
        }

        .whatsapp-icon {
            width: 30px;
            height: 30px;
        }

        /* Confetti animation */
        .confetti-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1001;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #0ea5e9;
            opacity: 0.8;
            animation: fall 5s linear infinite;
        }

        @keyframes fall {
            0% {
                transform: translateY(-100px) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Admin dashboard styles */
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .admin-tabs {
            display: flex;
            border-bottom: 1px solid #e5e7eb;
            margin-bottom: 20px;
        }

        .admin-tab {
            padding: 10px 20px;
            cursor: pointer;
            font-weight: 500;
            color: #4b5563;
            border-bottom: 2px solid transparent;
        }

        .admin-tab.active {
            color: #0ea5e9;
            border-bottom-color: #0ea5e9;
        }

        .admin-tab-content {
            display: none;
        }

        .admin-tab-content.active {
            display: block;
        }

        .admin-slots-container {
            margin-bottom: 30px;
        }

        .admin-bookings {
            margin-top: 30px;
        }

        .admin-booking-item {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-booking-info {
            flex: 1;
        }

        .admin-booking-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .admin-booking-mobile {
            color: #4b5563;
            font-size: 0.9rem;
        }

        .admin-booking-time {
            font-weight: 500;
            color: #0ea5e9;
            margin-right: 20px;
        }

        .admin-booking-actions {
            display: flex;
            gap: 10px;
        }

        .admin-action-btn {
            padding: 5px 10px;
            font-size: 0.9rem;
        }

        .financial-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .financial-card {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }

        .financial-card h3 {
            font-size: 1rem;
            color: #4b5563;
            margin-bottom: 10px;
        }

        .financial-amount {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0ea5e9;
        }

        .financial-table-container {
            overflow-x: auto;
        }

        .financial-table {
            width: 100%;
            border-collapse: collapse;
        }

        .financial-table th,
        .financial-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }

        .financial-table th {
            background-color: #f9fafb;
            font-weight: 600;
            color: #1e293b;
        }

        .financial-table td {
            color: #4b5563;
        }

        .financial-table tr:last-child td {
            border-bottom: none;
        }

        .financial-table tr:hover td {
            background-color: #f3f4f6;
        }

        .financial-filter {
            margin-bottom: 30px;
        }

        .filter-options {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .filter-option {
            padding: 8px 15px;
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .filter-option:hover {
            border-color: #0ea5e9;
        }

        .filter-option.active {
            background-color: #0ea5e9;
            color: white;
            border-color: #0ea5e9;
        }

        .custom-date-range {
            background-color: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
        }

        .custom-date-range .form-group {
            flex: 1;
            min-width: 200px;
            margin-bottom: 0;
        }

        #date-range-label {
            font-size: 1rem;
            font-weight: normal;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="#" class="logo" onclick="showPage('home')">Soapy Football</a>
            <nav>
                <ul id="menu">
                    <li><a href="#" onclick="showPageAndCloseMenu('home')">Home</a></li>
                    <li><a href="#" onclick="showPageAndCloseMenu('booking')">Book Now</a></li>
                    <li><a href="#" onclick="showPageAndCloseMenu('history')">My Bookings</a></li>
                    <li><a href="#" onclick="showPageAndCloseMenu('admin-login')">Admin</a></li>
                </ul>
            </nav>
            <button class="mobile-menu-btn" id="mobile-menu-btn">☰</button>
        </div>
    </header>

    <!-- Home Page -->
    <div id="home-page" class="page active">
        <section class="hero">
            <div class="container hero-content">
                <div class="hero-image">
                    <!-- SVG Vector Graphic for Soapy Football -->
                    <svg viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
                        <!-- Football field -->
                        <rect x="50" y="50" width="500" height="300" fill="#10b981" rx="10" />
                        <rect x="75" y="75" width="450" height="250" fill="#34d399" stroke="#ffffff" stroke-width="2" rx="5" />

                        <!-- Center circle -->
                        <circle cx="300" cy="200" r="50" fill="none" stroke="#ffffff" stroke-width="2" />
                        <circle cx="300" cy="200" r="5" fill="#ffffff" />

                        <!-- Center line -->
                        <line x1="300" y1="75" x2="300" y2="325" stroke="#ffffff" stroke-width="2" />

                        <!-- Goal areas -->
                        <rect x="75" y="150" width="50" height="100" fill="none" stroke="#ffffff" stroke-width="2" />
                        <rect x="475" y="150" width="50" height="100" fill="none" stroke="#ffffff" stroke-width="2" />

                        <!-- Football -->
                        <circle cx="250" cy="180" r="20" fill="#ffffff" />
                        <path d="M250,160 L260,170 L250,180 L240,170 Z" fill="#333333" />
                        <path d="M250,180 L260,190 L250,200 L240,190 Z" fill="#333333" />
                        <path d="M230,180 L240,190 L230,200 L220,190 Z" fill="#333333" />
                        <path d="M270,180 L280,190 L270,200 L260,190 Z" fill="#333333" />

                        <!-- Soap bubbles -->
                        <circle cx="200" cy="150" r="15" fill="rgba(255,255,255,0.7)" />
                        <circle cx="350" cy="220" r="10" fill="rgba(255,255,255,0.7)" />
                        <circle cx="180" cy="250" r="12" fill="rgba(255,255,255,0.7)" />
                        <circle cx="400" cy="180" r="8" fill="rgba(255,255,255,0.7)" />
                        <circle cx="280" cy="120" r="10" fill="rgba(255,255,255,0.7)" />
                    </svg>
                </div>
                <h1><span>Soapy Football</span> Turf Booking</h1>
                <p>Experience the thrill of playing football on our premium soapy turf. Perfect for friendly matches, corporate events, and weekend fun with friends and family.</p>
                <button class="btn hero-btn" onclick="showPage('booking')">Book Your Slot Now</button>
            </div>
        </section>

        <section class="features">
            <div class="container">
                <h2 class="section-title">Why Choose Us</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">⚽</div>
                        <h3>Premium Soapy Turf</h3>
                        <p>Our state-of-the-art soapy turf provides the perfect playing surface for an exciting and unique football experience.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📅</div>
                        <h3>Easy Booking</h3>
                        <p>Book your preferred slot online in just a few clicks. No more waiting in queues or making phone calls.</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🏆</div>
                        <h3>Tournament Support</h3>
                        <p>We provide complete support for organizing tournaments, including referees, equipment, and refreshments.</p>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Booking Page -->
    <div id="booking-page" class="page">
        <section class="booking-section">
            <div class="container">
                <h2 class="section-title">Book Your Slot</h2>
                <div class="booking-container">
                    <div class="date-selector" id="date-selector">
                        <!-- Date options will be dynamically generated -->
                    </div>

                    <div class="time-slots-container">
                        <h3 class="time-slots-title">Select Time Slot</h3>
                        <div class="time-slots-grid" id="time-slots-grid">
                            <!-- Time slots will be dynamically generated -->
                        </div>
                    </div>

                    <div class="booking-form" id="booking-form" style="display: none;">
                        <h3 class="section-title">Enter Your Details</h3>
                        <div class="form-group">
                            <label for="name">Full Name</label>
                            <input type="text" id="name" placeholder="Enter your full name" required>
                        </div>

                        <div class="form-group">
                            <label for="mobile">Mobile Number</label>
                            <input type="tel" id="mobile" placeholder="Enter your mobile number" required>
                        </div>

                        <div class="booking-summary">
                            <h3 class="summary-title">Booking Summary</h3>
                            <div class="summary-item">
                                <span class="summary-label">Date</span>
                                <span class="summary-value" id="summary-date">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Time Slot</span>
                                <span class="summary-value" id="summary-time">-</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Turf Booking Fee</span>
                                <span class="summary-value">₹200</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total Amount</span>
                                <span class="summary-value total-amount">₹200</span>
                            </div>
                        </div>

                        <button class="btn book-btn" id="book-btn" disabled>Proceed to Payment</button>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- History Page -->
    <div id="history-page" class="page">
        <section class="history-section">
            <div class="container">
                <h2 class="section-title">Your Booking History</h2>
                <div class="history-container">
                    <div class="history-form">
                        <div class="form-group">
                            <label for="history-mobile">Enter Your Mobile Number</label>
                            <input type="tel" id="history-mobile" placeholder="Enter your mobile number" required>
                        </div>
                        <button class="btn" id="search-bookings-btn">Search Bookings</button>
                    </div>

                    <div class="booking-list" id="booking-list">
                        <!-- Bookings will be loaded here -->
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Admin Login Page -->
    <div id="admin-login-page" class="page">
        <section class="history-section">
            <div class="container">
                <h2 class="section-title">Admin Login</h2>
                <div class="history-container">
                    <div class="history-form">
                        <div class="form-group">
                            <label for="admin-username">Username</label>
                            <input type="text" id="admin-username" placeholder="Enter admin username" required>
                        </div>
                        <div class="form-group">
                            <label for="admin-password">Password</label>
                            <input type="password" id="admin-password" placeholder="Enter admin password" required>
                        </div>
                        <button class="btn" id="admin-login-btn">Login</button>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Admin Dashboard Page -->
    <div id="admin-dashboard-page" class="page">
        <section class="booking-section">
            <div class="container">
                <h2 class="section-title">Admin Dashboard</h2>
                <div class="booking-container">
                    <div class="admin-header">
                        <h3>Welcome, Admin</h3>
                        <div>
                            <button class="btn btn-secondary" onclick="showPage('home')">Logout</button>
                        </div>
                    </div>

                    <div class="admin-tabs">
                        <div class="admin-tab active" data-tab="bookings">Bookings</div>
                        <div class="admin-tab" data-tab="financial">Financial</div>
                    </div>

                    <div class="admin-tab-content active" id="bookings-tab">
                        <div class="date-selector" id="admin-date-selector">
                            <!-- Date options will be dynamically generated -->
                        </div>

                        <div class="admin-slots-container">
                            <h3 class="time-slots-title">Slot Status</h3>
                            <div class="time-slots-grid" id="admin-slots-grid">
                                <!-- Time slots will be dynamically generated -->
                            </div>
                        </div>

                        <div class="admin-bookings">
                            <h3 class="section-title">Booked Slots</h3>
                            <div class="admin-bookings-list" id="admin-bookings-list">
                                <!-- Bookings will be dynamically generated -->
                            </div>
                        </div>
                    </div>

                    <div class="admin-tab-content" id="financial-tab">
                        <div class="financial-summary">
                            <div class="financial-card">
                                <h3>Today's Revenue</h3>
                                <div class="financial-amount" id="today-revenue">₹0</div>
                            </div>
                            <div class="financial-card">
                                <h3>This Month's Revenue</h3>
                                <div class="financial-amount" id="month-revenue">₹0</div>
                            </div>
                            <div class="financial-card">
                                <h3>Total Revenue</h3>
                                <div class="financial-amount" id="total-revenue">₹0</div>
                            </div>
                        </div>

                        <div class="financial-filter">
                            <h3 class="section-title">Filter Revenue</h3>
                            <div class="filter-options">
                                <div class="filter-option active" data-filter="all">All Time</div>
                                <div class="filter-option" data-filter="today">Today</div>
                                <div class="filter-option" data-filter="month">This Month</div>
                                <div class="filter-option" data-filter="custom">Custom Range</div>
                            </div>

                            <div class="custom-date-range" id="custom-date-range" style="display: none;">
                                <div class="form-group">
                                    <label for="start-date">Start Date</label>
                                    <input type="date" id="start-date">
                                </div>
                                <div class="form-group">
                                    <label for="end-date">End Date</label>
                                    <input type="date" id="end-date">
                                </div>
                                <button class="btn" id="apply-date-filter">Apply Filter</button>
                            </div>
                        </div>

                        <div class="financial-details">
                            <h3 class="section-title">Revenue Report <span id="date-range-label">(All Time)</span></h3>
                            <div class="financial-table-container">
                                <table class="financial-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Bookings</th>
                                            <th>Advance + Balance</th>
                                            <th>Total Revenue</th>
                                        </tr>
                                    </thead>
                                    <tbody id="financial-table-body">
                                        <!-- Financial data will be dynamically generated -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Success Modal -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="success-icon">✓</div>
            <h3 class="modal-title">Booking Confirmed!</h3>
            <p class="modal-text">Your booking has been confirmed. A confirmation message will be sent to your mobile number.</p>
            <button class="btn modal-btn" onclick="closeSuccessModal()">Back to Home</button>
        </div>
    </div>

    <!-- Custom Alert Modal -->
    <div class="modal" id="alert-modal">
        <div class="modal-content">
            <div id="alert-icon" class="alert-icon">!</div>
            <h3 class="modal-title" id="alert-title">Alert</h3>
            <p class="modal-text" id="alert-message"></p>
            <button class="btn modal-btn" onclick="closeAlertModal()">OK</button>
        </div>
    </div>

    <!-- Custom Confirm Modal -->
    <div class="modal" id="confirm-modal">
        <div class="modal-content">
            <div class="confirm-icon">?</div>
            <h3 class="modal-title" id="confirm-title">Confirm</h3>
            <p class="modal-text" id="confirm-message"></p>
            <div class="modal-buttons" id="confirm-buttons">
                <button class="btn btn-secondary modal-btn" onclick="handleConfirmResponse(false)">Cancel</button>
                <button class="btn modal-btn" onclick="handleConfirmResponse(true)">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Custom Prompt Modal -->
    <div class="modal" id="prompt-modal">
        <div class="modal-content">
            <div class="prompt-icon">?</div>
            <h3 class="modal-title" id="prompt-title">Enter Information</h3>
            <p class="modal-text" id="prompt-message"></p>
            <div class="form-group">
                <input type="text" id="prompt-input" class="modal-input">
            </div>
            <div class="modal-buttons">
                <button class="btn btn-secondary modal-btn" onclick="handlePromptResponse(false)">Cancel</button>
                <button class="btn modal-btn" onclick="handlePromptResponse(true)">Submit</button>
            </div>
        </div>
    </div>

    <!-- Confetti Container for Success Animation -->
    <div class="confetti-container" id="confetti-container"></div>

    <!-- Floating WhatsApp Button -->
    <a href="https://wa.me/919633812015?text=Hello" class="whatsapp-float" target="_blank">
        <i class="whatsapp-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 30 448 512" fill="currentColor">
                <path d="M380.9 97.1C339 55.1 283.2 32 223.9 32c-122.4 0-222 99.6-222 222 0 39.1 10.2 77.3 29.6 111L0 480l117.7-30.9c32.4 17.7 68.9 27 106.1 27h.1c122.3 0 224.1-99.6 224.1-222 0-59.3-25.2-115-67.1-157zm-157 341.6c-33.2 0-65.7-8.9-94-25.7l-6.7-4-69.8 18.3L72 359.2l-4.4-7c-18.5-29.4-28.2-63.3-28.2-98.2 0-101.7 82.8-184.5 184.6-184.5 49.3 0 95.6 19.2 130.4 54.1 34.8 34.9 56.2 81.2 56.1 130.5 0 101.8-84.9 184.6-186.6 184.6zm101.2-138.2c-5.5-2.8-32.8-16.2-37.9-18-5.1-1.9-8.8-2.8-12.5 2.8-3.7 5.6-14.3 18-17.6 21.8-3.2 3.7-6.5 4.2-12 1.4-32.6-16.3-54-29.1-75.5-66-5.7-9.8 5.7-9.1 16.3-30.3 1.8-3.7.9-6.9-.5-9.7-1.4-2.8-12.5-30.1-17.1-41.2-4.5-10.8-9.1-9.3-12.5-9.5-3.2-.2-6.9-.2-10.6-.2-3.7 0-9.7 1.4-14.8 6.9-5.1 5.6-19.4 19-19.4 46.3 0 27.3 19.9 53.7 22.6 57.4 2.8 3.7 39.1 59.7 94.8 83.8 35.2 15.2 49 16.5 66.6 13.9 10.7-1.6 32.8-13.4 37.4-26.4 4.6-13 4.6-24.1 3.2-26.4-1.3-2.5-5-3.9-10.5-6.6z"/>
            </svg>
        </i>
    </a>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best Soapy turf for football enthusiasts.</p>
                    <div class="social-links">
                        <a href="https://www.instagram.com/seventees" target="_blank" class="social-link">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" width="24" height="24" fill="currentColor">
                                <path d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"/>
                            </svg>
                            <span>Seventees</span>
                        </a>
                    </div>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="#" onclick="showPage('home')">Home</a></li>
                        <li><a href="#" onclick="showPage('booking')">Book Now</a></li>
                        <li><a href="#" onclick="showPage('history')">My Bookings</a></li>
                        <li><a href="https://razorpay.com/privacy/" target="_blank">Privacy Policy</a></li>
                        <li><a href="https://razorpay.com/privacy/" target="_blank">Terms and Conditions</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9633812015, +91 9645507984</li>
                        <li>Address: Seventees | Soapy Football Poonoor</li>
                    </ul>
                </div>
            </div>

            <!-- Google Maps -->
            <div class="map-container">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3912.8394831161!2d75.8366!3d11.2989!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTHCsDE3JzU2LjAiTiA3NcKwNTAnMTEuOCJF!5e0!3m2!1sen!2sin!4v1620000000000!5m2!1sen!2sin"
                    width="100%"
                    height="300"
                    style="border:0;"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const menu = document.getElementById('menu');

        // Toggle menu when button is clicked
        mobileMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event from bubbling to document
            menu.classList.toggle('active');
        });

        // Close menu when clicking anywhere else on the page
        document.addEventListener('click', (e) => {
            // If the click is outside the menu and the menu button
            if (!menu.contains(e.target) && e.target !== mobileMenuBtn) {
                menu.classList.remove('active');
            }
        });

        // Prevent clicks inside the menu from closing it
        menu.addEventListener('click', (e) => {
            e.stopPropagation(); // Stop propagation to document
        });

        // Page navigation
        function showPage(pageId) {
            // Hide all pages
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // Show the selected page
            const selectedPage = document.getElementById(pageId + '-page');
            if (selectedPage) {
                selectedPage.classList.add('active');
            }

            // Initialize page-specific content
            if (pageId === 'booking') {
                initBookingPage();
            } else if (pageId === 'admin-dashboard') {
                initAdminDashboard();
            }
        }

        // Show page and close mobile menu
        function showPageAndCloseMenu(pageId) {
            // Close the mobile menu
            menu.classList.remove('active');

            // Show the selected page
            showPage(pageId);
        }

        // Database simulation
        const db = {
            bookings: [],
            slots: {},
            admin: {
                username: 'admin',
                password: 'admin123'
            },
            bookingCounter: 1 // Counter for sequential booking IDs
        };

        // Booking status constants
        const BOOKING_STATUS = {
            UPCOMING: 'upcoming',
            COMPLETED: 'completed',
            CANCELLED: 'cancelled'
        };

        // Function to generate a booking ID in the format BK[YEAR][MONTH][SEQUENTIAL NUMBER]
        function generateBookingId() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const sequentialNumber = db.bookingCounter.toString().padStart(2, '0');

            // Increment the counter for next booking
            db.bookingCounter++;

            return `BK${year}${month}${sequentialNumber}`;
        }

        // Generate time slots
        function generateTimeSlots() {
            const slots = [];
            for (let hour = 0; hour < 24; hour++) {
                // Format hours in 12-hour format with AM/PM
                const startHour12 = hour === 0 ? 12 : (hour > 12 ? hour - 12 : hour);
                const endHour12 = (hour + 1) === 0 ? 12 : ((hour + 1) > 12 ? (hour + 1) - 12 : (hour + 1));

                const startAmPm = hour < 12 ? 'AM' : 'PM';
                const endAmPm = (hour + 1) < 12 ? 'AM' : 'PM';

                // Also keep 24-hour format for internal use
                const startHour24 = hour.toString().padStart(2, '0') + ':00';
                const endHour24 = (hour + 1).toString().padStart(2, '0') + ':00';

                slots.push({
                    id: hour,
                    time: `${startHour12}:00 ${startAmPm} - ${endHour12}:00 ${endAmPm}`,
                    time24: `${startHour24} - ${endHour24}` // Keep 24-hour format for internal use if needed
                });
            }
            return slots;
        }

        const timeSlots = generateTimeSlots();

        // Initialize booking page
        function initBookingPage() {
            const dateSelector = document.getElementById('date-selector');
            const timeSlotsGrid = document.getElementById('time-slots-grid');

            // Clear previous content
            dateSelector.innerHTML = '';
            timeSlotsGrid.innerHTML = '';

            // Generate dates (today and next 2 days)
            const dates = [];
            for (let i = 0; i < 3; i++) {
                const date = new Date();
                date.setDate(date.getDate() + i);
                dates.push(date);
            }

            // Add date options
            dates.forEach((date, index) => {
                const dateOption = document.createElement('div');
                const isToday = index === 0;

                // Add classes: selected for today, and today-date for highlighting
                dateOption.className = 'date-option' + (isToday ? ' selected today-date' : '');
                dateOption.dataset.date = formatDate(date);

                const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
                const dateNum = date.getDate();
                const month = date.toLocaleDateString('en-US', { month: 'short' });

                dateOption.innerHTML = `
                    <div class="date-day">${dayName}</div>
                    <div class="date-date">${dateNum} ${month}</div>
                    ${isToday ? '<div class="today-marker">Today</div>' : ''}
                `;

                dateOption.addEventListener('click', () => {
                    // Remove selected class from all date options
                    document.querySelectorAll('.date-option').forEach(opt => opt.classList.remove('selected'));

                    // Add selected class to clicked date option
                    dateOption.classList.add('selected');

                    // Update time slots for selected date
                    updateTimeSlots(dateOption.dataset.date);
                });

                dateSelector.appendChild(dateOption);
            });

            // Initialize time slots for today
            updateTimeSlots(formatDate(new Date()));
        }

        // Update time slots for selected date
        function updateTimeSlots(date) {
            const timeSlotsGrid = document.getElementById('time-slots-grid');
            timeSlotsGrid.innerHTML = '';

            // Initialize slots for date if not exists
            if (!db.slots[date]) {
                db.slots[date] = {};
                // No dummy bookings - slots will be empty until booked
            }

            // Add time slots
            timeSlots.forEach(slot => {
                const timeSlot = document.createElement('div');
                const isBooked = db.slots[date][slot.id]?.booked || false;

                timeSlot.className = 'time-slot' + (isBooked ? ' booked' : '');
                timeSlot.dataset.id = slot.id;
                timeSlot.dataset.time = slot.time;
                timeSlot.textContent = slot.time;

                if (!isBooked) {
                    timeSlot.addEventListener('click', () => {
                        // Remove selected class from all time slots
                        document.querySelectorAll('.time-slot').forEach(s => s.classList.remove('selected'));

                        // Add selected class to clicked time slot
                        timeSlot.classList.add('selected');

                        // Show booking form
                        document.getElementById('booking-form').style.display = 'block';

                        // Update booking summary
                        const selectedDate = document.querySelector('.date-option.selected').dataset.date;
                        const formattedDate = new Date(selectedDate).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        });

                        document.getElementById('summary-date').textContent = formattedDate;
                        document.getElementById('summary-time').textContent = timeSlot.dataset.time;

                        // Enable book button when form is filled
                        checkBookingForm();
                    });
                }

                timeSlotsGrid.appendChild(timeSlot);
            });
        }

        // Check if booking form is filled
        function checkBookingForm() {
            const nameInput = document.getElementById('name');
            const mobileInput = document.getElementById('mobile');
            const bookBtn = document.getElementById('book-btn');

            const isFormFilled = nameInput.value.trim() !== '' && mobileInput.value.trim() !== '';
            const isSlotSelected = document.querySelector('.time-slot.selected') !== null;

            bookBtn.disabled = !(isFormFilled && isSlotSelected);
        }

        // Add event listeners to form inputs
        document.addEventListener('DOMContentLoaded', () => {
            const nameInput = document.getElementById('name');
            const mobileInput = document.getElementById('mobile');
            const bookBtn = document.getElementById('book-btn');

            nameInput.addEventListener('input', checkBookingForm);
            mobileInput.addEventListener('input', checkBookingForm);

            // Book button click handler
            bookBtn.addEventListener('click', () => {
                const selectedDate = document.querySelector('.date-option.selected').dataset.date;
                const selectedSlot = document.querySelector('.time-slot.selected');
                const name = nameInput.value.trim();
                const mobile = mobileInput.value.trim();

                if (selectedSlot && name && mobile) {
                    // Simulate Razorpay payment
                    simulateRazorpayPayment(selectedDate, selectedSlot.dataset.id, name, mobile);
                }
            });

            // Admin login button click handler
            document.getElementById('admin-login-btn').addEventListener('click', () => {
                const username = document.getElementById('admin-username').value;
                const password = document.getElementById('admin-password').value;

                if (username === db.admin.username && password === db.admin.password) {
                    showPage('admin-dashboard');
                } else {
                    showAlert('Invalid username or password', 'Login Failed', 'error');
                }
            });

            // Search bookings button click handler
            document.getElementById('search-bookings-btn').addEventListener('click', () => {
                const mobile = document.getElementById('history-mobile').value.trim();
                if (mobile) {
                    searchBookings(mobile);
                }
            });

            // Admin tabs click handlers
            document.querySelectorAll('.admin-tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    document.querySelectorAll('.admin-tab').forEach(t => t.classList.remove('active'));
                    document.querySelectorAll('.admin-tab-content').forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');
                    document.getElementById(tab.dataset.tab + '-tab').classList.add('active');

                    // Initialize date inputs for custom filter if financial tab is selected
                    if (tab.dataset.tab === 'financial') {
                        initFinancialDateInputs();
                    }
                });
            });

            // Financial filter options click handlers
            document.querySelectorAll('.filter-option').forEach(option => {
                option.addEventListener('click', () => {
                    // Remove active class from all options
                    document.querySelectorAll('.filter-option').forEach(o => o.classList.remove('active'));

                    // Add active class to clicked option
                    option.classList.add('active');

                    // Show/hide custom date range inputs
                    const customDateRange = document.getElementById('custom-date-range');
                    if (option.dataset.filter === 'custom') {
                        customDateRange.style.display = 'flex';
                    } else {
                        customDateRange.style.display = 'none';

                        // Apply the selected filter
                        applyFinancialFilter(option.dataset.filter);
                    }
                });
            });

            // Apply custom date filter button click handler
            document.getElementById('apply-date-filter').addEventListener('click', () => {
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                if (!startDate || !endDate) {
                    showAlert('Please select both start and end dates', 'Invalid Date Range', 'error');
                    return;
                }

                if (new Date(startDate) > new Date(endDate)) {
                    showAlert('Start date cannot be after end date', 'Invalid Date Range', 'error');
                    return;
                }

                applyFinancialFilter('custom', startDate, endDate);
            });
        });

        // Initialize date inputs for custom filter
        function initFinancialDateInputs() {
            const today = new Date();
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');

            // Set max date to today for both inputs
            const maxDate = formatDate(today);
            startDateInput.max = maxDate;
            endDateInput.max = maxDate;

            // Set default values if not already set
            if (!startDateInput.value) {
                // Default start date is 30 days ago
                const startDate = new Date();
                startDate.setDate(today.getDate() - 30);
                startDateInput.value = formatDate(startDate);
            }

            if (!endDateInput.value) {
                // Default end date is today
                endDateInput.value = maxDate;
            }
        }

        // Apply financial filter
        function applyFinancialFilter(filter, startDate, endDate) {
            let filterStartDate = null;
            let filterEndDate = null;
            let labelText = '';

            const today = new Date();

            switch (filter) {
                case 'today':
                    filterStartDate = formatDate(today);
                    filterEndDate = formatDate(today);
                    labelText = '(Today)';
                    break;

                case 'month':
                    // First day of current month
                    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                    filterStartDate = formatDate(firstDay);
                    filterEndDate = formatDate(today);
                    labelText = `(${today.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })})`;

                    // Debug log to verify dates
                    console.log('Month filter:', {
                        firstDay: filterStartDate,
                        today: filterEndDate
                    });
                    break;

                case 'custom':
                    filterStartDate = startDate;
                    filterEndDate = endDate;

                    // Format dates for label
                    const start = new Date(startDate);
                    const end = new Date(endDate);
                    const startFormatted = start.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' });
                    const endFormatted = end.toLocaleDateString('en-US', { day: 'numeric', month: 'short', year: 'numeric' });

                    labelText = `(${startFormatted} - ${endFormatted})`;
                    break;

                default: // 'all'
                    labelText = '(All Time)';
                    break;
            }

            // Update date range label
            document.getElementById('date-range-label').textContent = labelText;

            // Update financial table with filtered data
            updateFinancialTable(filterStartDate, filterEndDate);
        }

        // Update financial table with filtered data
        function updateFinancialTable(startDate, endDate) {
            const financialTableBody = document.getElementById('financial-table-body');
            financialTableBody.innerHTML = '';

            // Group bookings by date
            const bookingsByDate = {};

            // Debug log for filter dates
            console.log('Filtering financial table with dates:', { startDate, endDate });

            db.bookings.forEach(booking => {
                // Skip cancelled bookings
                if (booking.status === BOOKING_STATUS.CANCELLED) {
                    return;
                }

                // Apply date filter if provided
                if (startDate && endDate) {
                    // Convert dates to YYYY-MM-DD format for comparison
                    const bookingDateStr = booking.date;

                    // Debug log for date comparison
                    // console.log('Comparing dates:', {
                    //     bookingDate: bookingDateStr,
                    //     startDate,
                    //     endDate,
                    //     isInRange: (bookingDateStr >= startDate && bookingDateStr <= endDate)
                    // });

                    // Skip if booking date is outside filter range
                    if (bookingDateStr < startDate || bookingDateStr > endDate) {
                        return;
                    }
                }

                // Calculate total amount
                const totalAmount = booking.amount + (booking.balanceAmount || 0);

                // Group by date
                if (!bookingsByDate[booking.date]) {
                    bookingsByDate[booking.date] = {
                        count: 0,
                        revenue: 0,
                        advanceAmount: 0,
                        balanceAmount: 0
                    };
                }

                bookingsByDate[booking.date].count++;
                bookingsByDate[booking.date].revenue += totalAmount;
                bookingsByDate[booking.date].advanceAmount += booking.amount;
                bookingsByDate[booking.date].balanceAmount += (booking.balanceAmount || 0);
            });

            // Debug log for grouped bookings
            console.log('Grouped bookings by date:', bookingsByDate);

            // Sort dates in descending order
            const sortedDates = Object.keys(bookingsByDate).sort((a, b) => new Date(b) - new Date(a));

            // Add rows to table
            sortedDates.forEach(date => {
                const row = document.createElement('tr');

                const formattedDate = new Date(date).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });

                row.innerHTML = `
                    <td>${formattedDate}</td>
                    <td>${bookingsByDate[date].count}</td>
                    <td>₹${bookingsByDate[date].advanceAmount} + ₹${bookingsByDate[date].balanceAmount}</td>
                    <td>₹${bookingsByDate[date].revenue}</td>
                `;

                financialTableBody.appendChild(row);
            });

            // Show message if no data
            if (sortedDates.length === 0) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td colspan="4" style="text-align: center;">No bookings found for the selected date range</td>
                `;
                financialTableBody.appendChild(row);
            }
        }

        // Simulate Razorpay payment
        function simulateRazorpayPayment(date, slotId, name, mobile) {
            // In a real implementation, this would integrate with the Razorpay API
            console.log('Processing payment for:', { date, slotId, name, mobile });

            // Simulate payment processing
            setTimeout(() => {
                // Save booking
                saveBooking(date, slotId, name, mobile);

                // Show success modal with confetti
                showSuccessModal();
            }, 1500);
        }

        // Save booking
        function saveBooking(date, slotId, name, mobile) {
            // Initialize slots for date if not exists
            if (!db.slots[date]) {
                db.slots[date] = {};
            }

            // Generate booking ID in the format BK[YEAR][MONTH][SEQUENTIAL NUMBER]
            const bookingId = generateBookingId();

            // Mark slot as booked
            db.slots[date][slotId] = {
                booked: true,
                name: name,
                mobile: mobile,
                paymentDate: new Date().toISOString(),
                bookingId: bookingId,
                status: BOOKING_STATUS.UPCOMING,
                balanceAmount: 0,
                advanceReceived: true // Customer bookings always have advance payment
            };

            // Add to bookings list
            db.bookings.push({
                id: bookingId,
                date: date,
                slotId: slotId,
                time: timeSlots[slotId].time,
                name: name,
                mobile: mobile,
                amount: 200,
                balanceAmount: 0,
                paymentDate: new Date().toISOString(),
                status: BOOKING_STATUS.UPCOMING
            });

            console.log('Booking saved:', db.bookings[db.bookings.length - 1]);
        }

        // Show success modal with confetti
        function showSuccessModal() {
            const modal = document.getElementById('success-modal');
            const confettiContainer = document.getElementById('confetti-container');

            // Show modal
            modal.classList.add('active');

            // Create confetti
            for (let i = 0; i < 100; i++) {
                createConfetti(confettiContainer);
            }
        }

        // Create confetti element
        function createConfetti(container) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';

            // Random position
            const left = Math.random() * 100;
            confetti.style.left = left + '%';

            // Random color
            const colors = ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];
            const color = colors[Math.floor(Math.random() * colors.length)];
            confetti.style.backgroundColor = color;

            // Random size
            const size = Math.random() * 10 + 5;
            confetti.style.width = size + 'px';
            confetti.style.height = size + 'px';

            // Random rotation
            confetti.style.transform = 'rotate(' + Math.random() * 360 + 'deg)';

            // Random animation duration
            const duration = Math.random() * 3 + 2;
            confetti.style.animationDuration = duration + 's';

            // Random delay
            const delay = Math.random() * 2;
            confetti.style.animationDelay = delay + 's';

            // Add to container
            container.appendChild(confetti);

            // Remove after animation
            setTimeout(() => {
                confetti.remove();
            }, (duration + delay) * 1000);
        }

        // Close success modal
        function closeSuccessModal() {
            const modal = document.getElementById('success-modal');
            modal.classList.remove('active');

            // Clear booking form
            document.getElementById('name').value = '';
            document.getElementById('mobile').value = '';
            document.getElementById('booking-form').style.display = 'none';

            // Go back to home page
            showPage('home');
        }

        // Search bookings by mobile number
        function searchBookings(mobile) {
            const bookingList = document.getElementById('booking-list');
            bookingList.innerHTML = '';

            // Filter bookings by mobile number
            const userBookings = db.bookings.filter(booking => booking.mobile === mobile);

            if (userBookings.length === 0) {
                bookingList.innerHTML = `
                    <div class="no-bookings">
                        <p>No bookings found for this mobile number.</p>
                        <button class="btn" onclick="showPage('booking')">Book Now</button>
                    </div>
                `;
                return;
            }

            // Sort bookings by date (newest first)
            userBookings.sort((a, b) => new Date(b.date) - new Date(a.date));

            // Add bookings to list
            userBookings.forEach(booking => {
                const bookingItem = document.createElement('div');
                bookingItem.className = 'booking-item';

                const formattedDate = new Date(booking.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                // Determine status class
                let statusClass = '';
                switch(booking.status) {
                    case BOOKING_STATUS.UPCOMING:
                        statusClass = 'status-upcoming';
                        break;
                    case BOOKING_STATUS.COMPLETED:
                        statusClass = 'status-completed';
                        break;
                    case BOOKING_STATUS.CANCELLED:
                        statusClass = 'status-cancelled';
                        break;
                }

                // Calculate total amount
                const totalAmount = booking.amount + (booking.balanceAmount || 0);

                bookingItem.innerHTML = `
                    <div class="booking-header">
                        <div class="booking-date">Booking #${booking.id}</div>
                        <div class="booking-status ${statusClass}">${booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</div>
                    </div>
                    <div class="booking-details">
                        <div class="booking-detail">
                            <div class="detail-label">Date</div>
                            <div class="detail-value">${formattedDate}</div>
                        </div>
                        <div class="booking-detail">
                            <div class="detail-label">Time</div>
                            <div class="detail-value">${booking.time}</div>
                        </div>
                        <div class="booking-detail">
                            <div class="detail-label">Name</div>
                            <div class="detail-value">${booking.name}</div>
                        </div>
                        <div class="booking-detail">
                            <div class="detail-label">Advance Amount</div>
                            <div class="detail-value">₹${booking.amount}</div>
                        </div>
                        ${booking.status === BOOKING_STATUS.COMPLETED ? `
                        <div class="booking-detail">
                            <div class="detail-label">Balance Amount</div>
                            <div class="detail-value">₹${booking.balanceAmount || 0}</div>
                        </div>
                        <div class="booking-detail">
                            <div class="detail-label">Total Amount</div>
                            <div class="detail-value">₹${totalAmount}</div>
                        </div>
                        ` : ''}
                    </div>
                `;

                bookingList.appendChild(bookingItem);
            });
        }

        // Initialize admin dashboard
        function initAdminDashboard() {
            const adminDateSelector = document.getElementById('admin-date-selector');

            // Clear previous content
            adminDateSelector.innerHTML = '';

            // Generate dates (past 7 days, today, and next 2 days)
            const dates = [];
            for (let i = -7; i < 3; i++) {
                const date = new Date();
                date.setDate(date.getDate() + i);
                dates.push(date);
            }

            // Find today's index (should be 7)
            const todayIndex = 7;

            // Add date options
            dates.forEach((date, index) => {
                const dateOption = document.createElement('div');
                const isToday = index === todayIndex;

                // Add classes: selected for today, and today-date for highlighting
                dateOption.className = 'date-option' + (isToday ? ' selected today-date' : '');
                dateOption.dataset.date = formatDate(date);

                const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });
                const dateNum = date.getDate();
                const month = date.toLocaleDateString('en-US', { month: 'short' });

                dateOption.innerHTML = `
                    <div class="date-day">${dayName}</div>
                    <div class="date-date">${dateNum} ${month}</div>
                    ${isToday ? '<div class="today-marker">Today</div>' : ''}
                `;

                dateOption.addEventListener('click', () => {
                    // Remove selected class from all date options
                    document.querySelectorAll('#admin-date-selector .date-option').forEach(opt => opt.classList.remove('selected'));

                    // Add selected class to clicked date option
                    dateOption.classList.add('selected');

                    // Update admin slots for selected date
                    updateAdminSlots(dateOption.dataset.date);
                });

                adminDateSelector.appendChild(dateOption);
            });

            // Scroll to center today's date
            setTimeout(() => {
                const todayElement = adminDateSelector.querySelector('.today-date');
                if (todayElement) {
                    // Calculate scroll position to center today's date
                    const containerWidth = adminDateSelector.offsetWidth;
                    const elementWidth = todayElement.offsetWidth;
                    const elementLeft = todayElement.offsetLeft;
                    const scrollPosition = elementLeft - (containerWidth / 2) + (elementWidth / 2);

                    adminDateSelector.scrollLeft = scrollPosition;
                }
            }, 100);

            // Initialize slots for today
            updateAdminSlots(formatDate(new Date()));

            // Update financial data
            updateFinancialData();
        }

        // Update admin slots for selected date
        function updateAdminSlots(date) {
            const adminSlotsGrid = document.getElementById('admin-slots-grid');
            const adminBookingsList = document.getElementById('admin-bookings-list');

            // Clear existing content
            adminSlotsGrid.innerHTML = '';
            adminBookingsList.innerHTML = '';

            console.log('Updating admin slots for date:', date);
            console.log('Current slots data:', db.slots[date]);

            // Initialize slots for date if not exists
            if (!db.slots[date]) {
                db.slots[date] = {};
            }

            // Add time slots
            timeSlots.forEach(slot => {
                const timeSlot = document.createElement('div');

                // Check if slot is booked
                const slotData = db.slots[date][slot.id];
                const isBooked = slotData && slotData.booked === true;

                console.log(`Slot ${slot.id} (${slot.time}) booked status:`, isBooked, slotData);

                timeSlot.className = 'time-slot' + (isBooked ? ' booked' : '');
                timeSlot.dataset.id = slot.id;
                timeSlot.dataset.time = slot.time;
                timeSlot.textContent = slot.time;

                timeSlot.addEventListener('click', () => {
                    if (isBooked) {
                        // Show booking details
                        showAlert(`Slot booked by: ${db.slots[date][slot.id].name}\nMobile: ${db.slots[date][slot.id].mobile}`, 'Booking Details', 'info');
                    } else {
                        // Check if date is in the past
                        const selectedDate = new Date(date);
                        const today = new Date();
                        today.setHours(0, 0, 0, 0); // Reset time to start of day for comparison

                        if (selectedDate < today) {
                            showAlert('Cannot reserve slots for past dates', 'Action Not Allowed', 'error');
                            return;
                        }

                        // Admin can reserve a slot
                        showConfirm('Do you want to reserve this slot?', (confirmed) => {
                            if (confirmed) {
                                // Ask if advance was received with Yes/No buttons
                                showConfirm('Was advance payment of ₹200 received?', (advanceReceived) => {
                                    console.log('Advance received response:', advanceReceived, typeof advanceReceived);

                                    // Generate booking ID in the format BK[YEAR][MONTH][SEQUENTIAL NUMBER]
                                    const bookingId = generateBookingId();
                                    const advanceAmount = advanceReceived ? 200 : 0;

                                    // Prompt for customer name
                                    showPrompt('Enter customer name:', (customerName) => {
                                        let name = customerName;
                                        if (name === null || name.trim() === '') {
                                            name = 'Reserved by Admin';
                                        }

                                        // Prompt for customer mobile
                                        showPrompt('Enter customer mobile number:', (customerMobile) => {
                                            let mobile = customerMobile;
                                            if (mobile === null || mobile.trim() === '') {
                                                mobile = 'Admin';
                                            }

                                            try {
                                                console.log('Before reservation - Current slots:', JSON.stringify(db.slots[date]));
                                                console.log('Attempting to reserve slot:', slot.id);

                                                // Create the slot booking
                                                db.slots[date][slot.id] = {
                                                    booked: true,
                                                    name: name,
                                                    mobile: mobile,
                                                    paymentDate: new Date().toISOString(),
                                                    bookingId: bookingId,
                                                    status: BOOKING_STATUS.UPCOMING,
                                                    balanceAmount: 0,
                                                    advanceReceived: advanceReceived
                                                };

                                                console.log('After slot update - Current slots:', JSON.stringify(db.slots[date]));

                                                // Add to bookings list
                                                db.bookings.push({
                                                    id: bookingId,
                                                    date: date,
                                                    slotId: slot.id,
                                                    time: timeSlots[slot.id].time,
                                                    name: name,
                                                    mobile: mobile,
                                                    amount: advanceAmount,
                                                    balanceAmount: 0,
                                                    paymentDate: new Date().toISOString(),
                                                    status: BOOKING_STATUS.UPCOMING
                                                });

                                                console.log('Slot reserved:', {
                                                    date,
                                                    slotId: slot.id,
                                                    name,
                                                    mobile,
                                                    advanceReceived,
                                                    advanceAmount
                                                });
                                                console.log('Current bookings:', db.bookings.length);

                                                // Force refresh of the UI
                                                setTimeout(() => {
                                                    // Update admin slots
                                                    updateAdminSlots(date);

                                                    // Update financial data
                                                    updateFinancialData();

                                                    showAlert(`Slot reserved successfully${advanceReceived ? ' with advance payment' : ' without advance payment'}`, 'Success', 'success');
                                                }, 100);
                                            } catch (error) {
                                                console.error('Error reserving slot:', error);
                                                showAlert('An error occurred while reserving the slot. Please try again.', 'Error', 'error');
                                            }
                                        }, '', 'Customer Mobile');
                                    }, '', 'Customer Name');
                                }, 'Advance Payment', 'yesno'); // Use Yes/No buttons
                            }
                        }, 'Confirm Reservation');
                    }
                });

                adminSlotsGrid.appendChild(timeSlot);

                // Add booked slots to bookings list
                if (isBooked) {
                    try {
                        const bookingInfo = db.slots[date][slot.id];
                        console.log(`Adding booking to list: Slot ${slot.id}, Info:`, bookingInfo);

                        const bookingItem = document.createElement('div');
                        bookingItem.className = 'admin-booking-item';

                        bookingItem.innerHTML = `
                            <div class="admin-booking-info">
                                <div class="admin-booking-name">${bookingInfo.name}</div>
                                <div class="admin-booking-mobile">${bookingInfo.mobile}</div>
                            </div>
                            <div class="admin-booking-time">${slot.time}</div>
                            <div class="admin-booking-actions">
                                <button class="btn btn-secondary admin-action-btn" onclick="cancelBooking('${date}', ${slot.id})">Cancel</button>
                                <button class="btn admin-action-btn" onclick="updateBalanceAmount('${date}', ${slot.id})">Update Balance</button>
                            </div>
                        `;

                        adminBookingsList.appendChild(bookingItem);
                    } catch (error) {
                        console.error(`Error adding booking for slot ${slot.id} to list:`, error);
                    }
                }
            });
        }

        // Cancel booking (admin function)
        function cancelBooking(date, slotId) {
            showConfirm('Are you sure you want to cancel this booking?', (confirmed) => {
                if (confirmed) {
                    // Get the booking ID from the slot
                    const bookingId = db.slots[date][slotId].bookingId;

                    // Update slot status to cancelled
                    db.slots[date][slotId].status = BOOKING_STATUS.CANCELLED;

                    // Update booking status in bookings array
                    const bookingIndex = db.bookings.findIndex(b => b.id === bookingId);
                    if (bookingIndex !== -1) {
                        db.bookings[bookingIndex].status = BOOKING_STATUS.CANCELLED;
                    }

                    // Remove from slots (make it available again)
                    delete db.slots[date][slotId];

                    // Update admin slots
                    updateAdminSlots(date);

                    // Update financial data
                    updateFinancialData();

                    showAlert('Booking cancelled successfully', 'Cancellation Complete', 'error');
                }
            }, 'Confirm Cancellation');
        }

        // Update balance amount (admin function)
        function updateBalanceAmount(date, slotId) {
            const bookingInfo = db.slots[date][slotId];
            const bookingId = bookingInfo.bookingId;

            // Check if advance was received
            const advanceReceived = bookingInfo.advanceReceived !== false; // Default to true if not specified
            const defaultAmount = advanceReceived ? '0' : '200'; // If no advance, suggest 200 as default
            const promptMessage = advanceReceived ?
                'Enter balance amount received:' :
                'No advance was received. Enter total amount received (suggested: ₹200):';

            // Prompt for balance amount
            showPrompt(promptMessage, (value) => {
                if (value !== null) {
                    // Convert to number and validate
                    const amount = parseInt(value);

                    if (isNaN(amount) || amount < 0) {
                        showAlert('Please enter a valid amount', 'Invalid Input', 'error');
                        return;
                    }

                    // Update slot balance amount
                    db.slots[date][slotId].balanceAmount = amount;

                    // Update booking balance amount in bookings array
                    const bookingIndex = db.bookings.findIndex(b => b.id === bookingId);
                    if (bookingIndex !== -1) {
                        db.bookings[bookingIndex].balanceAmount = amount;
                        db.bookings[bookingIndex].status = BOOKING_STATUS.COMPLETED;
                    }

                    // Update admin slots
                    updateAdminSlots(date);

                    // Update financial data
                    updateFinancialData();

                    showAlert(`Amount of ₹${amount} recorded successfully`, 'Success', 'success');
                }
            }, defaultAmount, 'Update Amount');
        }

        // Update financial data
        function updateFinancialData() {
            // Calculate revenue
            const today = formatDate(new Date());
            const currentMonth = new Date().getMonth();
            const currentYear = new Date().getFullYear();

            let todayRevenue = 0;
            let monthRevenue = 0;
            let totalRevenue = 0;

            db.bookings.forEach(booking => {
                // Skip cancelled bookings for revenue calculations
                if (booking.status === BOOKING_STATUS.CANCELLED) {
                    return;
                }

                // Calculate total amount (advance + balance)
                const totalAmount = booking.amount + (booking.balanceAmount || 0);

                // Add to total revenue
                totalRevenue += totalAmount;

                // Check if booking is from today
                if (booking.date === today) {
                    todayRevenue += totalAmount;
                }

                // Check if booking is from current month
                const bookingDate = new Date(booking.date);
                if (bookingDate.getMonth() === currentMonth && bookingDate.getFullYear() === currentYear) {
                    monthRevenue += totalAmount;
                }
            });

            // Update revenue displays
            document.getElementById('today-revenue').textContent = '₹' + todayRevenue;
            document.getElementById('month-revenue').textContent = '₹' + monthRevenue;
            document.getElementById('total-revenue').textContent = '₹' + totalRevenue;

            // Update financial table with all data (no filter)
            updateFinancialTable(null, null);

            // Reset filter options to "All Time"
            document.querySelectorAll('.filter-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.filter === 'all') {
                    option.classList.add('active');
                }
            });

            // Hide custom date range
            document.getElementById('custom-date-range').style.display = 'none';

            // Update date range label
            document.getElementById('date-range-label').textContent = '(All Time)';
        }

        // Helper function to format date as YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            return `${year}-${month}-${day}`;
        }



        // Custom modal functions
        let confirmCallback = null;
        let promptCallback = null;

        // Custom alert function
        function showAlert(message, title = 'Alert', type = 'info') {
            const alertModal = document.getElementById('alert-modal');
            const alertTitle = document.getElementById('alert-title');
            const alertMessage = document.getElementById('alert-message');
            const alertIcon = document.getElementById('alert-icon');

            alertTitle.textContent = title;
            alertMessage.textContent = message;

            // Set icon based on alert type
            alertIcon.className = type === 'error' ? 'error-icon' :
                                 type === 'success' ? 'success-icon' : 'alert-icon';

            // Set icon content
            alertIcon.textContent = type === 'error' ? '!' :
                                   type === 'success' ? '✓' : 'i';

            alertModal.classList.add('active');
        }

        function closeAlertModal() {
            const alertModal = document.getElementById('alert-modal');
            alertModal.classList.remove('active');
        }

        // Custom confirm function
        function showConfirm(message, callback, title = 'Confirm', buttonStyle = 'default') {
            const confirmModal = document.getElementById('confirm-modal');
            const confirmTitle = document.getElementById('confirm-title');
            const confirmMessage = document.getElementById('confirm-message');
            const confirmButtons = document.getElementById('confirm-buttons');

            confirmTitle.textContent = title;
            confirmMessage.textContent = message;
            confirmCallback = callback;

            // Set button style based on parameter
            if (buttonStyle === 'yesno') {
                confirmButtons.innerHTML = `
                    <button class="btn btn-secondary modal-btn" onclick="handleConfirmResponse(false)">No</button>
                    <button class="btn modal-btn" onclick="handleConfirmResponse(true)">Yes</button>
                `;
            } else {
                confirmButtons.innerHTML = `
                    <button class="btn btn-secondary modal-btn" onclick="handleConfirmResponse(false)">Cancel</button>
                    <button class="btn modal-btn" onclick="handleConfirmResponse(true)">Confirm</button>
                `;
            }

            confirmModal.classList.add('active');
        }

        function handleConfirmResponse(response) {
            console.log('Confirm response received:', response);
            const confirmModal = document.getElementById('confirm-modal');
            confirmModal.classList.remove('active');

            // Store the callback in a local variable to prevent it from being cleared before execution
            const callback = confirmCallback;
            confirmCallback = null;

            // Execute the callback after a short delay to ensure the modal is closed
            if (callback) {
                setTimeout(() => {
                    console.log('Executing confirm callback with response:', response);
                    callback(response);
                }, 50);
            }
        }

        // Custom prompt function
        function showPrompt(message, callback, defaultValue = '', title = 'Enter Information') {
            const promptModal = document.getElementById('prompt-modal');
            const promptTitle = document.getElementById('prompt-title');
            const promptMessage = document.getElementById('prompt-message');
            const promptInput = document.getElementById('prompt-input');

            promptTitle.textContent = title;
            promptMessage.textContent = message;
            promptInput.value = defaultValue;
            promptCallback = callback;

            // Focus the input after the modal is shown
            setTimeout(() => {
                promptInput.focus();
            }, 100);

            promptModal.classList.add('active');
        }

        function handlePromptResponse(confirmed) {
            console.log('Prompt response received:', confirmed);
            const promptModal = document.getElementById('prompt-modal');
            const promptInput = document.getElementById('prompt-input');

            promptModal.classList.remove('active');

            // Store the callback and input value in local variables
            const callback = promptCallback;
            const inputValue = promptInput.value;
            promptCallback = null;

            // Execute the callback after a short delay
            if (callback) {
                setTimeout(() => {
                    console.log('Executing prompt callback with value:', confirmed ? inputValue : null);
                    if (confirmed) {
                        callback(inputValue);
                    } else {
                        callback(null);
                    }
                }, 50);
            }
        }
    </script>
</body>
</html>
