import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: 'What is Soapy Football?',
    answer: 'Soapy Football is a unique twist on traditional football where the turf is covered with a slippery soapy solution. This adds an exciting challenge as players try to maintain their balance while playing the game. It\'s fun, unpredictable, and guaranteed to bring lots of laughter!'
  },
  {
    question: 'How do I book a slot?',
    answer: 'Booking a slot is easy! Simply create an account or log in, navigate to the booking page, select your preferred date and time slot, provide your details, and complete the payment. Once your booking is confirmed, you\'ll receive a confirmation notification.'
  },
  {
    question: 'What is the duration of each slot?',
    answer: 'Each booking slot is for 1 hour, starting from the time you select. We recommend arriving 15 minutes before your slot to get ready and familiarize yourself with the rules and safety guidelines.'
  },
  {
    question: 'How many players can play at once?',
    answer: 'Our soapy football turf can accommodate up to 12 players at a time (6 vs 6). However, you can book with fewer players if you prefer. The minimum recommended number is 6 players (3 vs 3) for an enjoyable experience.'
  },
  {
    question: 'What should I wear for Soapy Football?',
    answer: 'We recommend wearing comfortable sports clothes that you don\'t mind getting wet and soapy. Sports shoes with good grip are essential. Avoid wearing jewelry or accessories that might get lost or cause injury during play.'
  },
  {
    question: 'Can I cancel or reschedule my booking?',
    answer: 'Yes, you can cancel or reschedule your booking up to 24 hours before your slot. Please contact our customer support or use the booking management options in your account. Cancellations made less than 24 hours before the slot may not be eligible for a refund.'
  },
  {
    question: 'Is Soapy Football safe?',
    answer: 'We take safety seriously. Our soapy solution is non-toxic and skin-friendly. The playing area is designed with safety in mind, with padded boundaries to prevent injuries. However, like any physical activity, there is a risk of slipping and falling. We provide a brief safety orientation before each session.'
  },
  {
    question: 'Can I book for special events or parties?',
    answer: 'Absolutely! Soapy Football is perfect for birthday parties, corporate team building, and special events. Contact us directly for special event packages and group discounts.'
  }
];

const FAQ = () => {
  const [expandedIndex, setExpandedIndex] = useState<number | null>(null);
  
  const toggleExpand = (index: number) => {
    setExpandedIndex(expandedIndex === index ? null : index);
  };
  
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-dark mb-4">Frequently Asked Questions</h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Have questions about Soapy Football? Find answers to the most commonly asked questions below.
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          {faqItems.map((item, index) => (
            <div key={index} className="mb-4">
              <button
                onClick={() => toggleExpand(index)}
                className="w-full flex justify-between items-center p-4 bg-gray-50 hover:bg-gray-100 rounded-lg text-left focus:outline-none"
              >
                <span className="font-medium text-dark">{item.question}</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${
                    expandedIndex === index ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
              
              <AnimatePresence>
                {expandedIndex === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="p-4 bg-white border border-gray-100 rounded-b-lg">
                      <p className="text-gray-600">{item.answer}</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQ;
