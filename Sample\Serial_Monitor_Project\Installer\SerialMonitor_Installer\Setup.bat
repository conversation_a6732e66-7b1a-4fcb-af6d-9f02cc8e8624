@echo off
title Serial Monitor Setup
color 0B
cls
echo.
echo     ███████╗███████╗██████╗ ██╗ █████╗ ██╗         
echo     ██╔════╝██╔════╝██╔══██╗██║██╔══██╗██║         
echo     ███████╗█████╗  ██████╔╝██║███████║██║         
echo     ╚════██║██╔══╝  ██╔══██╗██║██╔══██║██║         
echo     ███████║███████╗██║  ██║██║██║  ██║███████╗    
echo     ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝╚═╝  ╚═╝╚══════╝    
echo.
echo     ███╗   ███╗ ██████╗ ███╗   ██╗██╗████████╗ ██████╗ ██████╗ 
echo     ████╗ ████║██╔═══██╗████╗  ██║██║╚══██╔══╝██╔═══██╗██╔══██╗
echo     ██╔████╔██║██║   ██║██╔██╗ ██║██║   ██║   ██║   ██║██████╔╝
echo     ██║╚██╔╝██║██║   ██║██║╚██╗██║██║   ██║   ██║   ██║██╔══██╗
echo     ██║ ╚═╝ ██║╚██████╔╝██║ ╚████║██║   ██║   ╚██████╔╝██║  ██║
echo     ╚═╝     ╚═╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo ===============================================================================
echo                        Professional Serial Communication Tool
echo                                    Version 1.0
echo ===============================================================================
echo.
echo Welcome to the Serial Monitor Setup Wizard!
echo.
echo This will install Serial Monitor on your computer.
echo.
echo Features:
echo   • Beautiful purple-themed interface
echo   • COM port auto-detection
echo   • Multiple baud rate support
echo   • Real-time serial communication
echo   • Professional vector icons
echo   • Arduino IDE-style serial monitor
echo.
echo ===============================================================================
echo.
echo IMPORTANT: This installer requires administrator privileges.
echo.
pause

REM Check if running as admin
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Administrator privileges confirmed
    echo.
    echo Starting installation...
    timeout /t 2 >nul
    call install.bat
) else (
    echo.
    echo ⚠ ERROR: Administrator privileges required!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    echo Press any key to exit...
    pause >nul
)
