"""
Rigol DP821A Programmable DC Power Supply Driver
Channel 1: 0-60V, 0-1A (60W)
Channel 2: 0-8V, 0-10A (80W)
"""

from typing import Dict, Any, List, Optional
import time
from .base_instrument import BaseInstrument


class RigolDP821A(BaseInstrument):
    """Driver for Rigol DP821A Dual Channel Power Supply"""
    
    def __init__(self, name: str = "DP821A", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        
        # Channel specifications
        self.channel_specs = {
            1: {'voltage_max': 60.0, 'current_max': 1.0, 'power_max': 60.0},
            2: {'voltage_max': 8.0, 'current_max': 10.0, 'power_max': 80.0}
        }
        
        # Channel settings
        self.channel_settings = {}
        for ch in range(1, 3):
            self.channel_settings[ch] = {
                'voltage': 0.0,
                'current': 0.0,
                'enabled': False,
                'ovp': self.channel_specs[ch]['voltage_max'],
                'ocp': self.channel_specs[ch]['current_max']
            }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get power supply capabilities"""
        return {
            'type': 'power_supply',
            'channels': 2,
            'channel_specs': self.channel_specs,
            'features': [
                'Independent channel control',
                'Over-voltage protection',
                'Over-current protection',
                'Timer function',
                'Delay function',
                'Track mode',
                'Remote sensing'
            ]
        }
    
    def initialize(self) -> bool:
        """Initialize power supply to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(2)
            
            # Turn off all outputs
            for ch in range(1, 3):
                self.set_channel_enabled(ch, False)
                self.set_voltage(ch, 0.0)
                self.set_current(ch, 0.1)  # Set minimum current limit
            
            # Clear any errors
            self.write("*CLS")
            
            self.logger.info("Rigol DP821A initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Rigol DP821A: {e}")
            return False
    
    # Channel Selection
    def select_channel(self, channel: int) -> bool:
        """Select active channel for subsequent commands"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        return self.write(f":INST:NSEL {channel}")
    
    # Voltage Control
    def set_voltage(self, channel: int, voltage: float) -> bool:
        """Set voltage for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        max_voltage = self.channel_specs[channel]['voltage_max']
        if not 0 <= voltage <= max_voltage:
            raise ValueError(f"Voltage must be 0-{max_voltage}V for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f":SOUR:VOLT {voltage}")
        if success:
            self.channel_settings[channel]['voltage'] = voltage
        return success
    
    def get_voltage_setpoint(self, channel: int) -> float:
        """Get voltage setpoint for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":SOUR:VOLT?")
        return float(response)
    
    def measure_voltage(self, channel: int) -> float:
        """Measure actual voltage for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":MEAS:VOLT?")
        return float(response)
    
    # Current Control
    def set_current(self, channel: int, current: float) -> bool:
        """Set current limit for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        max_current = self.channel_specs[channel]['current_max']
        if not 0 <= current <= max_current:
            raise ValueError(f"Current must be 0-{max_current}A for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f":SOUR:CURR {current}")
        if success:
            self.channel_settings[channel]['current'] = current
        return success
    
    def get_current_setpoint(self, channel: int) -> float:
        """Get current setpoint for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":SOUR:CURR?")
        return float(response)
    
    def measure_current(self, channel: int) -> float:
        """Measure actual current for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":MEAS:CURR?")
        return float(response)
    
    # Power Measurement
    def measure_power(self, channel: int) -> float:
        """Measure actual power for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":MEAS:POW?")
        return float(response)
    
    # Output Control
    def set_channel_enabled(self, channel: int, enabled: bool) -> bool:
        """Enable or disable output for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        state = "ON" if enabled else "OFF"
        success = self.write(f":OUTP {state}")
        if success:
            self.channel_settings[channel]['enabled'] = enabled
        return success
    
    def get_channel_enabled(self, channel: int) -> bool:
        """Get output state for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":OUTP?")
        return response.strip() == "ON"
    
    def set_all_outputs_enabled(self, enabled: bool) -> bool:
        """Enable or disable all outputs"""
        success = True
        for ch in range(1, 3):
            success &= self.set_channel_enabled(ch, enabled)
        return success
    
    # Protection Settings
    def set_ovp(self, channel: int, voltage: float) -> bool:
        """Set over-voltage protection for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        max_voltage = self.channel_specs[channel]['voltage_max']
        if not 0 <= voltage <= max_voltage * 1.1:  # Allow 10% over max
            raise ValueError(f"OVP must be 0-{max_voltage * 1.1}V for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f":SOUR:VOLT:PROT {voltage}")
        if success:
            self.channel_settings[channel]['ovp'] = voltage
        return success
    
    def get_ovp(self, channel: int) -> float:
        """Get over-voltage protection setting"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":SOUR:VOLT:PROT?")
        return float(response)
    
    def set_ocp(self, channel: int, current: float) -> bool:
        """Set over-current protection for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        max_current = self.channel_specs[channel]['current_max']
        if not 0 <= current <= max_current * 1.1:  # Allow 10% over max
            raise ValueError(f"OCP must be 0-{max_current * 1.1}A for channel {channel}")
        
        self.select_channel(channel)
        success = self.write(f":SOUR:CURR:PROT {current}")
        if success:
            self.channel_settings[channel]['ocp'] = current
        return success
    
    def get_ocp(self, channel: int) -> float:
        """Get over-current protection setting"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        response = self.query(":SOUR:CURR:PROT?")
        return float(response)
    
    # Operating Mode
    def get_operating_mode(self, channel: int) -> str:
        """Get operating mode for specified channel (CV or CC)"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        self.select_channel(channel)
        try:
            response = self.query(":OUTP:MODE?")
            return response.strip()
        except:
            # Fallback: determine mode from measurements
            voltage = self.measure_voltage(channel)
            current = self.measure_current(channel)
            voltage_set = self.get_voltage_setpoint(channel)
            current_set = self.get_current_setpoint(channel)
            
            # Simple heuristic to determine mode
            if abs(voltage - voltage_set) < 0.1:
                return "CV"
            elif abs(current - current_set) < 0.01:
                return "CC"
            else:
                return "OFF"
    
    # Measurement Methods
    def get_all_measurements(self, channel: int) -> Dict[str, float]:
        """Get all measurements for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        try:
            measurements = {
                'voltage': self.measure_voltage(channel),
                'current': self.measure_current(channel),
                'power': self.measure_power(channel)
            }
            return measurements
        except Exception as e:
            self.logger.error(f"Failed to get measurements for channel {channel}: {e}")
            return {}
    
    def get_all_channels_measurements(self) -> Dict[int, Dict[str, float]]:
        """Get measurements for all channels"""
        all_measurements = {}
        for ch in range(1, 3):
            all_measurements[ch] = self.get_all_measurements(ch)
        return all_measurements
    
    # Status and Monitoring
    def get_channel_status(self, channel: int) -> Dict[str, Any]:
        """Get comprehensive status for specified channel"""
        if not 1 <= channel <= 2:
            raise ValueError("Channel must be 1 or 2")
        
        try:
            status = {
                'enabled': self.get_channel_enabled(channel),
                'mode': self.get_operating_mode(channel),
                'voltage_setpoint': self.get_voltage_setpoint(channel),
                'current_setpoint': self.get_current_setpoint(channel),
                'measurements': self.get_all_measurements(channel),
                'protection': {
                    'ovp': self.get_ovp(channel),
                    'ocp': self.get_ocp(channel)
                },
                'specifications': self.channel_specs[channel]
            }
            return status
        except Exception as e:
            self.logger.error(f"Failed to get status for channel {channel}: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status for all channels"""
        base_status = super().get_status()
        
        if self.connected:
            try:
                ps_status = {
                    'channels': {}
                }
                
                for ch in range(1, 3):
                    ps_status['channels'][ch] = self.get_channel_status(ch)
                
                base_status.update(ps_status)
            except Exception as e:
                self.logger.error(f"Failed to get status: {e}")
        
        return base_status
    
    # Error Checking
    def check_errors(self) -> List[str]:
        """Check for instrument errors"""
        errors = []
        try:
            while True:
                error = self.query(":SYST:ERR?")
                if error.startswith("0,"):
                    break
                errors.append(error)
        except Exception as e:
            self.logger.error(f"Failed to check errors: {e}")
        
        return errors
    
    # Convenience Methods
    def set_channel_output(self, channel: int, voltage: float, current: float, enabled: bool = True) -> bool:
        """Set voltage, current, and enable state for a channel"""
        success = True
        success &= self.set_voltage(channel, voltage)
        success &= self.set_current(channel, current)
        success &= self.set_channel_enabled(channel, enabled)
        return success
    
    def emergency_stop(self) -> bool:
        """Emergency stop - turn off all outputs immediately"""
        return self.set_all_outputs_enabled(False)
    
    # Track Mode (if supported)
    def set_track_mode(self, enabled: bool) -> bool:
        """Enable or disable track mode (both channels follow CH1)"""
        try:
            state = "ON" if enabled else "OFF"
            return self.write(f":OUTP:TRAC {state}")
        except Exception as e:
            self.logger.warning(f"Track mode not supported or failed: {e}")
            return False
    
    def get_track_mode(self) -> bool:
        """Get track mode state"""
        try:
            response = self.query(":OUTP:TRAC?")
            return response.strip() == "ON"
        except Exception as e:
            self.logger.warning(f"Track mode query failed: {e}")
            return False
