<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football - Payment</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0ea5e9',
                        secondary: '#10b981',
                        accent: '#f59e0b',
                        dark: '#1e293b',
                    },
                }
            }
        }
    </script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // React components
        const Header = () => {
            return (
                <header className="bg-white shadow-md">
                    <div className="container mx-auto px-4 py-4 flex justify-between items-center">
                        <div className="text-xl font-bold text-primary">Soapy Football</div>
                        <nav>
                            <ul className="flex space-x-4">
                                <li><a href="react-demo.html" className="text-gray-700 hover:text-primary">Home</a></li>
                                <li><a href="booking-demo.html" className="text-gray-700 hover:text-primary">Book Now</a></li>
                                <li><a href="#" className="text-gray-700 hover:text-primary">My Bookings</a></li>
                                <li><a href="#" className="bg-primary text-white px-4 py-2 rounded-md">Sign In</a></li>
                            </ul>
                        </nav>
                    </div>
                </header>
            );
        };

        const Footer = () => {
            return (
                <footer className="bg-dark text-white py-8">
                    <div className="container mx-auto px-4 text-center">
                        <p>&copy; {new Date().getFullYear()} Soapy Football. All rights reserved.</p>
                    </div>
                </footer>
            );
        };

        const PaymentPage = () => {
            const [isLoading, setIsLoading] = React.useState(false);
            const [paymentSuccess, setPaymentSuccess] = React.useState(false);
            
            // Mock booking data
            const booking = {
                id: 'booking-123',
                date: new Date().toISOString().split('T')[0],
                slot: '18:00 - 19:00',
                name: 'John Doe',
                phone: '9876543210',
                amount: 200
            };
            
            const handlePayment = () => {
                setIsLoading(true);
                
                // Simulate payment processing
                setTimeout(() => {
                    setIsLoading(false);
                    setPaymentSuccess(true);
                }, 2000);
            };
            
            if (paymentSuccess) {
                return (
                    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
                        <div className="bg-white rounded-lg shadow-md p-8 max-w-md w-full text-center">
                            <div className="relative mb-6">
                                {/* Confetti animation */}
                                <div className="absolute inset-0 overflow-hidden">
                                    {[...Array(30)].map((_, i) => (
                                        <div
                                            key={i}
                                            className="absolute w-2 h-2 rounded-full animate-confetti"
                                            style={{
                                                background: ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][
                                                    Math.floor(Math.random() * 5)
                                                ],
                                                left: `${Math.random() * 100}%`,
                                                top: '0%',
                                                animation: `confetti ${Math.random() * 2 + 1}s ease-out ${Math.random() * 0.5}s infinite`,
                                            }}
                                        />
                                    ))}
                                </div>

                                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        className="h-10 w-10 text-green-600"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                    >
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="M5 13l4 4L19 7"
                                        />
                                    </svg>
                                </div>
                            </div>

                            <h2 className="text-3xl font-bold text-dark mb-4">Booking Confirmed!</h2>
                            <p className="text-gray-600 mb-6">
                                Your booking for {booking.date} at {booking.slot} has been confirmed. We look forward to seeing you!
                            </p>

                            <div className="space-y-4">
                                <a
                                    href="#"
                                    className="block w-full bg-primary text-white px-4 py-2 rounded-md font-medium hover:bg-primary/90"
                                >
                                    View My Bookings
                                </a>
                                <a
                                    href="react-demo.html"
                                    className="block w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-md font-medium hover:bg-gray-300"
                                >
                                    Back to Home
                                </a>
                            </div>
                        </div>
                    </div>
                );
            }
            
            return (
                <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
                    <div className="bg-white rounded-lg shadow-md p-6 max-w-md w-full">
                        <h2 className="text-2xl font-bold text-dark mb-6">Complete Your Payment</h2>

                        <div className="border-t border-b py-4 my-4">
                            <div className="flex justify-between mb-2">
                                <span className="text-gray-600">Date:</span>
                                <span className="font-medium">{booking.date}</span>
                            </div>
                            <div className="flex justify-between mb-2">
                                <span className="text-gray-600">Time:</span>
                                <span className="font-medium">{booking.slot}</span>
                            </div>
                            <div className="flex justify-between mb-2">
                                <span className="text-gray-600">Name:</span>
                                <span className="font-medium">{booking.name}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-gray-600">Phone:</span>
                                <span className="font-medium">{booking.phone}</span>
                            </div>
                        </div>

                        <div className="flex justify-between items-center mb-6">
                            <span className="text-lg font-semibold">Total Amount:</span>
                            <span className="text-2xl font-bold text-primary">₹{booking.amount}</span>
                        </div>

                        <button
                            onClick={handlePayment}
                            disabled={isLoading}
                            className={`w-full px-4 py-2 rounded-md font-medium text-white ${
                                isLoading ? 'bg-primary/70 cursor-not-allowed' : 'bg-primary hover:bg-primary/90'
                            }`}
                        >
                            {isLoading ? 'Processing...' : 'Pay Now'}
                        </button>

                        <a
                            href="booking-demo.html"
                            className="block w-full text-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md font-medium hover:bg-gray-300 mt-4"
                        >
                            Cancel
                        </a>
                    </div>
                </div>
            );
        };

        const App = () => {
            return (
                <div className="min-h-screen bg-gray-50 flex flex-col">
                    <Header />
                    <main className="flex-grow">
                        <PaymentPage />
                    </main>
                    <Footer />
                </div>
            );
        };

        // Add confetti animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confetti {
                0% { transform: translateY(0) rotate(0); opacity: 1; }
                100% { transform: translateY(300px) rotate(360deg); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // Render the App component
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
