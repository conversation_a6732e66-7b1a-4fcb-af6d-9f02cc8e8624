<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - Soapy Football</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #0284c7;
        }

        .btn-secondary {
            background-color: #10b981;
        }

        .btn-secondary:hover {
            background-color: #059669;
        }

        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #0ea5e9;
        }

        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Payment section */
        .payment-section {
            padding: 60px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2rem;
            color: #1e293b;
        }

        .payment-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .payment-card {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .booking-details {
            margin-bottom: 30px;
        }

        .booking-details h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #4b5563;
        }

        .detail-value {
            font-weight: 600;
            color: #1e293b;
        }

        .total-amount {
            font-size: 1.2rem;
            font-weight: 700;
            color: #0ea5e9;
        }

        .payment-methods {
            margin-bottom: 30px;
        }

        .payment-methods h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .payment-option {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .payment-option:hover {
            border-color: #0ea5e9;
        }

        .payment-option.selected {
            border-color: #0ea5e9;
            background-color: rgba(14, 165, 233, 0.05);
        }

        .payment-option input {
            margin-right: 15px;
        }

        .payment-option-label {
            font-weight: 500;
            color: #1e293b;
        }

        .payment-btn {
            width: 100%;
            padding: 12px;
            font-size: 1.1rem;
            background-color: #0ea5e9;
        }

        .razorpay-logo {
            height: 30px;
            margin-left: auto;
        }

        /* Payment success modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .modal.active {
            display: flex;
        }

        .modal-content {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .success-icon {
            font-size: 4rem;
            color: #10b981;
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #1e293b;
        }

        .modal-text {
            margin-bottom: 25px;
            color: #4b5563;
        }

        .modal-btn {
            padding: 10px 20px;
        }

        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
            margin-top: auto;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .footer-col ul {
            list-style: none;
        }

        .footer-col ul li {
            margin-bottom: 10px;
        }

        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-col a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            nav ul.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">Soapy Football</a>
            <button class="mobile-menu-btn">☰</button>
            <nav>
                <ul id="menu">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="history.html">My Bookings</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="booking.html" class="nav-btn">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="payment-section">
        <div class="container">
            <h2 class="section-title">Complete Your Payment</h2>
            <div class="payment-container">
                <div class="payment-card">
                    <div class="booking-details">
                        <h3>Booking Details</h3>
                        <div class="detail-item">
                            <span class="detail-label">Date</span>
                            <span class="detail-value" id="booking-date">May 15, 2025</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Time Slot</span>
                            <span class="detail-value" id="booking-time">7:00 - 8:00 AM</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Number of Players</span>
                            <span class="detail-value" id="booking-players">6 players</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Turf Booking Fee</span>
                            <span class="detail-value">₹200</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Total Amount</span>
                            <span class="detail-value total-amount">₹200</span>
                        </div>
                    </div>

                    <div class="payment-methods">
                        <h3>Select Payment Method</h3>
                        <div class="payment-option selected">
                            <input type="radio" name="payment-method" id="razorpay" checked>
                            <span class="payment-option-label">Razorpay</span>
                            <img src="https://razorpay.com/assets/razorpay-logo.svg" alt="Razorpay" class="razorpay-logo">
                        </div>
                    </div>

                    <button class="btn payment-btn" id="pay-now-btn">Pay Now</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Payment Success Modal -->
    <div class="modal" id="success-modal">
        <div class="modal-content">
            <div class="success-icon">✓</div>
            <h3 class="modal-title">Payment Successful!</h3>
            <p class="modal-text">Your booking has been confirmed. A confirmation email has been sent to your registered email address.</p>
            <a href="index.html" class="btn modal-btn">Back to Home</a>
        </div>
    </div>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best turf booking platform for football enthusiasts.</p>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9876543210</li>
                        <li>Address: 123 Sports Complex, Mumbai</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const menu = document.getElementById('menu');

        mobileMenuBtn.addEventListener('click', () => {
            menu.classList.toggle('active');
        });

        // Get booking details from URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                date: params.get('date') || 'May 15, 2025',
                time: params.get('time') || '7:00 - 8:00 AM',
                players: params.get('players') || '6 players'
            };
        }

        // Set booking details
        const bookingParams = getUrlParams();
        document.getElementById('booking-date').textContent = bookingParams.date;
        document.getElementById('booking-time').textContent = bookingParams.time;
        document.getElementById('booking-players').textContent = bookingParams.players;

        // Payment option selection
        const paymentOptions = document.querySelectorAll('.payment-option');

        paymentOptions.forEach(option => {
            option.addEventListener('click', () => {
                // Remove selected class from all options
                paymentOptions.forEach(o => o.classList.remove('selected'));

                // Add selected class to clicked option
                option.classList.add('selected');

                // Check the radio button
                option.querySelector('input').checked = true;
            });
        });

        // Pay now button click
        const payNowBtn = document.getElementById('pay-now-btn');
        const successModal = document.getElementById('success-modal');

        payNowBtn.addEventListener('click', () => {
            // Simulate payment processing
            payNowBtn.textContent = 'Processing...';
            payNowBtn.disabled = true;

            // Show success modal after 2 seconds (simulating payment processing)
            setTimeout(() => {
                successModal.classList.add('active');
            }, 2000);
        });
    </script>
</body>
</html>
