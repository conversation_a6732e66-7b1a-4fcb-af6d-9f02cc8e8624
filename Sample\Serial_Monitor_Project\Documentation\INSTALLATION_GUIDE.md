# Serial Monitor - Installation Guide

## 📦 Standalone Executable

The Serial Monitor application has been packaged as a standalone executable that can run on any Windows PC without requiring Python installation.

### 📁 Files

- **`SerialMonitor.exe`** - Main application executable (14.6 MB)
- **`serial_monitor_icon.ico`** - Application icon
- **`serial_monitor_icon.png`** - Icon preview

### 🚀 Installation

1. **Download**: Copy the `SerialMonitor.exe` file to any location on your PC
2. **Run**: Double-click `SerialMonitor.exe` to launch the application
3. **No Installation Required**: The exe file is completely portable
4. **Fixed**: Resource loading issue resolved - theme and icon now work properly in standalone exe

### ✨ Features

- **Beautiful Purple Theme**: Modern dark interface with purple accents
- **Custom Vector Icon**: Professional serial port connector icon
- **COM Port Detection**: Automatically finds available serial ports
- **Multiple Baud Rates**: Support for 9600 to 921600 baud
- **Real-time Communication**: Send commands and receive responses
- **Clean Interface**: Arduino IDE-style serial monitor
- **Portable**: No installation or dependencies required

### 🔧 Usage

1. **Connect Device**: Plug in your serial device
2. **Select Port**: Choose COM port from dropdown
3. **Set Baud Rate**: Select appropriate baud rate
4. **Connect**: Click Connect button
5. **Send Commands**: Type commands and press Enter or click Send
6. **Monitor Output**: View responses in the serial monitor

### 📋 System Requirements

- **OS**: Windows 10/11 (64-bit)
- **RAM**: 50MB minimum
- **Storage**: 15MB disk space
- **Permissions**: Standard user permissions

### 🎨 Interface Colors

- **Background**: Dark charcoal theme
- **Primary**: Purple (#8b5fbf)
- **Accents**: Various purple shades
- **Text**: White for maximum readability
- **Status**: White text for connection status

### 🔄 Updates

To update the application:
1. Download the new `SerialMonitor.exe` file
2. Replace the old file with the new one
3. No additional steps required

### 🛠️ Troubleshooting

**Application won't start:**
- Ensure you have Windows 10/11
- Check antivirus isn't blocking the exe
- Run as administrator if needed

**COM port not detected:**
- Check device is properly connected
- Install device drivers if needed
- Click refresh button (⟲) to update port list

**Connection fails:**
- Verify correct COM port selected
- Check baud rate matches device
- Ensure device isn't used by another application

### 📞 Support

If you encounter any issues:
1. Check the troubleshooting section above
2. Verify your device drivers are installed
3. Try different baud rates
4. Restart the application

### 🔒 Security

The executable is built with PyInstaller and contains:
- Python runtime
- CustomTkinter GUI framework
- PySerial communication library
- Application code and resources

No network connections are made by the application.

---

**Serial Monitor v1.0**  
*Professional Serial Communication Tool*
