import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Booking } from '../types';

// Mock Razorpay interface
declare global {
  interface Window {
    Razorpay: any;
  }
}

const Payment = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [booking, setBooking] = useState<Booking | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    const fetchBooking = async () => {
      try {
        const { data, error } = await supabase
          .from('bookings')
          .select('*')
          .eq('id', bookingId)
          .single();

        if (error) throw error;
        if (!data) throw new Error('Booking not found');

        // Check if booking belongs to current user
        if (data.user_id !== user.id) {
          navigate('/booking');
          return;
        }

        setBooking(data);

        // If payment is already completed, show success
        if (data.payment_status === 'completed') {
          setPaymentSuccess(true);
        }
      } catch (error) {
        console.error('Error fetching booking:', error);
        setError('Failed to load booking details. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBooking();

    // Load Razorpay script
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
    };
  }, [bookingId, navigate, user]);

  const handlePayment = () => {
    if (!booking) return;

    // Create a new Razorpay instance
    const options = {
      key: 'rzp_test_YOUR_KEY_ID', // Replace with your Razorpay key
      amount: booking.amount * 100, // Amount in paise
      currency: 'INR',
      name: 'Soapy Football',
      description: `Booking for ${booking.date} ${booking.slot}`,
      image: '/logo.png',
      order_id: '', // This would come from your backend
      handler: function (response: any) {
        // Handle successful payment
        updatePaymentStatus(response.razorpay_payment_id);
      },
      prefill: {
        name: booking.name,
        contact: booking.phone,
      },
      theme: {
        color: '#0ea5e9',
      },
    };

    const razorpay = new window.Razorpay(options);
    razorpay.open();
  };

  const updatePaymentStatus = async (paymentId: string) => {
    try {
      const { error } = await supabase
        .from('bookings')
        .update({
          payment_status: 'completed',
          payment_id: paymentId,
        })
        .eq('id', bookingId);

      if (error) throw error;

      setPaymentSuccess(true);
    } catch (error) {
      console.error('Error updating payment status:', error);
      setError('Failed to update payment status. Please contact support.');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <div className="bg-white rounded-lg shadow-md p-6 max-w-md w-full">
          <h2 className="text-2xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-700 mb-6">{error}</p>
          <button
            onClick={() => navigate('/booking')}
            className="btn btn-primary w-full"
          >
            Back to Booking
          </button>
        </div>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          className="bg-white rounded-lg shadow-md p-8 max-w-md w-full text-center"
        >
          <div className="relative mb-6">
            {/* Confetti animation */}
            <div className="absolute inset-0 overflow-hidden">
              {[...Array(30)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 rounded-full"
                  style={{
                    background: ['#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][
                      Math.floor(Math.random() * 5)
                    ],
                    left: `${Math.random() * 100}%`,
                    top: '0%',
                  }}
                  animate={{
                    y: [0, 300],
                    x: [0, (Math.random() - 0.5) * 200],
                    opacity: [1, 0],
                  }}
                  transition={{
                    duration: Math.random() * 2 + 1,
                    repeat: Infinity,
                    repeatDelay: Math.random() * 0.5,
                  }}
                />
              ))}
            </div>

            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-10 w-10 text-green-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>

          <h2 className="text-3xl font-bold text-dark mb-4">Booking Confirmed!</h2>
          <p className="text-gray-600 mb-6">
            Your booking for {booking?.date} at {booking?.slot} has been confirmed. We look forward to seeing you!
          </p>

          <div className="space-y-4">
            <button
              onClick={() => navigate('/history')}
              className="btn btn-primary w-full"
            >
              View My Bookings
            </button>
            <button
              onClick={() => navigate('/')}
              className="btn bg-gray-200 hover:bg-gray-300 text-gray-800 w-full"
            >
              Back to Home
            </button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="bg-white rounded-lg shadow-md p-6 max-w-md w-full">
        <h2 className="text-2xl font-bold text-dark mb-6">Complete Your Payment</h2>

        <div className="border-t border-b py-4 my-4">
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Date:</span>
            <span className="font-medium">{booking?.date}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Time:</span>
            <span className="font-medium">{booking?.slot}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Name:</span>
            <span className="font-medium">{booking?.name}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Phone:</span>
            <span className="font-medium">{booking?.phone}</span>
          </div>
        </div>

        <div className="flex justify-between items-center mb-6">
          <span className="text-lg font-semibold">Total Amount:</span>
          <span className="text-2xl font-bold text-primary">₹{booking?.amount}</span>
        </div>

        <button
          onClick={handlePayment}
          className="btn btn-primary w-full"
        >
          Pay Now
        </button>

        <button
          onClick={() => navigate('/booking')}
          className="btn bg-gray-200 hover:bg-gray-300 text-gray-800 w-full mt-4"
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default Payment;
