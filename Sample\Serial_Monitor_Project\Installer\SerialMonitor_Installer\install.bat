@echo off
title Serial Monitor Installer
color 0A
echo.
echo ===============================================
echo    Serial Monitor Professional Installer
echo ===============================================
echo.
echo Installing Serial Monitor...
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo.
    echo ERROR: This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Create installation directory
set "INSTALL_DIR=%ProgramFiles%\Serial Monitor"
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying application files...
xcopy /Y /Q "SerialMonitor\*.*" "%INSTALL_DIR%\"
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy files
    pause
    exit /b 1
)

REM Create Start Menu shortcuts
echo Creating Start Menu shortcuts...
set "START_MENU=%ProgramData%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\Serial Monitor" mkdir "%START_MENU%\Serial Monitor"

REM Create main shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Serial Monitor\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\serial_monitor_icon.ico'; $Shortcut.Save()"

REM Create uninstaller shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\Serial Monitor\Uninstall Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.Save()"

REM Copy uninstaller
copy /Y "uninstall.bat" "%INSTALL_DIR%\uninstall.bat"

REM Add to registry (for Add/Remove Programs)
echo Adding registry entries...
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /v "DisplayName" /t REG_SZ /d "Serial Monitor" /f
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\serial_monitor_icon.ico" /f
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /v "Publisher" /t REG_SZ /d "Serial Monitor Team" /f
reg add "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /v "DisplayVersion" /t REG_SZ /d "1.0" /f

echo.
echo ===============================================
echo    Installation completed successfully!
echo ===============================================
echo.
echo Serial Monitor has been installed to:
echo %INSTALL_DIR%
echo.
echo You can find it in:
echo - Start Menu ^> Serial Monitor
echo - Or run: "%INSTALL_DIR%\SerialMonitor.exe"
echo.

REM Ask to create desktop shortcut
set /p desktop="Create desktop shortcut? (Y/N): "
if /i "%desktop%"=="Y" (
    echo Creating desktop shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\serial_monitor_icon.ico'; $Shortcut.Save()"
    echo Desktop shortcut created!
)

REM Ask to launch application
echo.
set /p launch="Launch Serial Monitor now? (Y/N): "
if /i "%launch%"=="Y" (
    start "" "%INSTALL_DIR%\SerialMonitor.exe"
)

echo.
echo Installation complete! Press any key to exit.
pause >nul
