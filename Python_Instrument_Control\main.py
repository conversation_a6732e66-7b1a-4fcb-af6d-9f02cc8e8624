"""
Python Instrument Control - Main Application
A professional instrument control framework with GUI interface
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.gui.main_window import InstrumentControlGUI
from src.utils.logger import setup_logger
from src.utils.config_manager import ConfigManager

def main():
    """Main application entry point"""
    
    # Setup logging
    logger = setup_logger()
    logger.info("Starting Python Instrument Control Application")
    
    # Load configuration
    config = ConfigManager()
    
    try:
        # Create and run GUI application
        app = InstrumentControlGUI(config)
        app.run()
        
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    
    finally:
        logger.info("Application shutdown")

if __name__ == "__main__":
    main()
