application:
  debug: false
  name: Python Instrument Control
  version: 1.0.0
data:
  auto_save: true
  data_directory: data
  save_format: csv
gui:
  theme: dark
  window_position:
  - 387
  - 113
  window_size:
  - 1200
  - 800
instruments:
  auto_connect: false
  connection_timeout: 5.0
  default_settings:
    serial:
      baudrate: 9600
      termination: "\r\n"
      timeout: 1.0
    visa:
      read_termination: '

        '
      timeout: 5000
      write_termination: '

        '
  max_retries: 3
  refresh_interval: 1.0
logging:
  level: INFO
  max_log_files: 10
