@echo off
echo Creating desktop shortcut for Serial Monitor...
set "INSTALL_DIR=%ProgramFiles%\Serial Monitor"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\Desktop\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\serial_monitor_icon.ico'; $Shortcut.Save()"
echo Desktop shortcut created!
pause
