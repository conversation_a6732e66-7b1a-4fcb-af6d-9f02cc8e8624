"""
Base Instrument Class
Provides common interface for all instruments
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, List
import logging
import pyvisa
import time

logger = logging.getLogger(__name__)

class BaseInstrument(ABC):
    """Abstract base class for all instruments"""

    def __init__(self, name: str, visa_address: str, visa_manager=None, **kwargs):
        self.name = name
        self.visa_address = visa_address
        self.visa_manager = visa_manager
        self.instrument = None
        self.connected = False
        self.logger = logger.getChild(f"Instrument.{name}")
        self.config = kwargs
        self.timeout = kwargs.get('timeout', 5000)
        self.max_retries = kwargs.get('max_retries', 3)
        
    def connect(self) -> bool:
        """Connect to the instrument using VISA"""
        try:
            if self.visa_manager:
                self.instrument = self.visa_manager.connect_instrument(self.name, self.visa_address)
            else:
                # Direct VISA connection if no manager provided
                rm = pyvisa.ResourceManager()
                self.instrument = rm.open_resource(self.visa_address)
                self.instrument.timeout = self.timeout

            if self.instrument:
                self.connected = True
                self.logger.info(f"Connected to {self.name} at {self.visa_address}")
                return True
            else:
                self.logger.error(f"Failed to connect to {self.name}")
                return False

        except Exception as e:
            self.logger.error(f"Connection error for {self.name}: {e}")
            self.connected = False
            return False

    def disconnect(self) -> bool:
        """Disconnect from the instrument"""
        try:
            if self.instrument:
                self.instrument.close()
                self.instrument = None
            self.connected = False
            self.logger.info(f"Disconnected from {self.name}")
            return True
        except Exception as e:
            self.logger.error(f"Disconnect error for {self.name}: {e}")
            return False

    def query(self, command: str) -> str:
        """Send query command and return response"""
        if not self.connected or not self.instrument:
            raise RuntimeError(f"Instrument {self.name} not connected")

        for attempt in range(self.max_retries):
            try:
                response = self.instrument.query(command)
                return response.strip()
            except Exception as e:
                self.logger.warning(f"Query attempt {attempt + 1} failed for {command}: {e}")
                if attempt == self.max_retries - 1:
                    raise
                time.sleep(0.1)

    def query_binary(self, command: str) -> bytes:
        """Send query command and return binary response"""
        if not self.connected or not self.instrument:
            raise RuntimeError(f"Instrument {self.name} not connected")

        for attempt in range(self.max_retries):
            try:
                response = self.instrument.query_binary_values(command, datatype='B', container=bytes)
                return response
            except Exception as e:
                self.logger.warning(f"Binary query attempt {attempt + 1} failed for {command}: {e}")
                if attempt == self.max_retries - 1:
                    raise
                time.sleep(0.1)

    def write(self, command: str) -> bool:
        """Send command to instrument"""
        if not self.connected or not self.instrument:
            raise RuntimeError(f"Instrument {self.name} not connected")

        for attempt in range(self.max_retries):
            try:
                self.instrument.write(command)
                return True
            except Exception as e:
                self.logger.warning(f"Write attempt {attempt + 1} failed for {command}: {e}")
                if attempt == self.max_retries - 1:
                    return False
                time.sleep(0.1)
        return False

    def get_id(self) -> str:
        """Get instrument identification"""
        if not self.connected:
            raise RuntimeError("Instrument not connected")
        return self.query("*IDN?")

    def reset(self) -> bool:
        """Reset instrument to default state"""
        return self.write("*RST")

    def get_status(self) -> Dict[str, Any]:
        """Get instrument status"""
        return {
            "name": self.name,
            "visa_address": self.visa_address,
            "connected": self.connected,
            "id": self.get_id() if self.connected else "Not connected"
        }

    @abstractmethod
    def get_capabilities(self) -> Dict[str, Any]:
        """Get instrument capabilities and specifications"""
        pass

    @abstractmethod
    def initialize(self) -> bool:
        """Initialize instrument to known state"""
        pass
