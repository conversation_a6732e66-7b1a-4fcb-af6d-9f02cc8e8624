import { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: ReactNode;
  className?: string;
  hover?: boolean;
  onClick?: () => void;
}

const Card = ({ children, className = '', hover = false, onClick }: CardProps) => {
  const baseClasses = 'bg-white rounded-lg shadow-md overflow-hidden';
  const hoverClasses = hover ? 'transition-transform hover:shadow-lg' : '';
  const clickableClasses = onClick ? 'cursor-pointer' : '';
  
  if (onClick) {
    return (
      <motion.div
        whileHover={hover ? { scale: 1.02, y: -5 } : {}}
        whileTap={onClick ? { scale: 0.98 } : {}}
        className={`${baseClasses} ${hoverClasses} ${clickableClasses} ${className}`}
        onClick={onClick}
      >
        {children}
      </motion.div>
    );
  }
  
  return (
    <div className={`${baseClasses} ${className}`}>
      {children}
    </div>
  );
};

export const CardHeader = ({ children, className = '' }: { children: ReactNode; className?: string }) => {
  return (
    <div className={`p-6 border-b ${className}`}>
      {children}
    </div>
  );
};

export const CardBody = ({ children, className = '' }: { children: ReactNode; className?: string }) => {
  return (
    <div className={`p-6 ${className}`}>
      {children}
    </div>
  );
};

export const CardFooter = ({ children, className = '' }: { children: ReactNode; className?: string }) => {
  return (
    <div className={`p-6 border-t ${className}`}>
      {children}
    </div>
  );
};

export default Card;
