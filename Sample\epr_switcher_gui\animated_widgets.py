"""
Simple animated button for GUI.
"""

import customtkinter as ctk

class AnimatedButton(ctk.CTkButton):
    """Button with simple hover and click animations."""

    def __init__(self, master, **kwargs):
        # Handle animation parameters
        self._animate_on_hover = kwargs.pop("animate_on_hover", True)

        # Handle pulse parameters for compatibility
        if "pulse_animation" in kwargs:
            kwargs.pop("pulse_animation")
        if "pulse_color" in kwargs:
            kwargs.pop("pulse_color")
        if "pulse_intensity" in kwargs:
            kwargs.pop("pulse_intensity")

        super().__init__(master, **kwargs)

    def configure(self, **kwargs):
        """Override configure to handle animation parameters."""
        # Handle animation parameters
        if "animate_on_hover" in kwargs:
            self._animate_on_hover = kwargs.pop("animate_on_hover")

        # Handle pulse parameters for compatibility
        if "pulse_animation" in kwargs:
            kwargs.pop("pulse_animation")
        if "pulse_color" in kwargs:
            kwargs.pop("pulse_color")
        if "pulse_intensity" in kwargs:
            kwargs.pop("pulse_intensity")

        super().configure(**kwargs)

class AnimatedSwitch(ctk.CTkSwitch):
    """Switch with simple animations."""

    def __init__(self, master, **kwargs):
        # Handle animation parameters
        if "animate_on_hover" in kwargs:
            kwargs.pop("animate_on_hover")
        if "animation_speed" in kwargs:
            kwargs.pop("animation_speed")

        super().__init__(master, **kwargs)
