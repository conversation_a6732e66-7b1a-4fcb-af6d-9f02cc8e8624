import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { motion } from 'framer-motion';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Slot } from '../types';
import Button from '../components/Button';
import Card, { CardHeader, CardBody } from '../components/Card';
import LoadingSpinner from '../components/LoadingSpinner';

const Booking = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [slots, setSlots] = useState<Slot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<Slot | null>(null);
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get max date (today + 2 days)
  const maxDate = new Date();
  maxDate.setDate(maxDate.getDate() + 2);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
    } else {
      fetchSlots(selectedDate);
    }
  }, [user, navigate, selectedDate]);

  const fetchSlots = async (date: Date) => {
    setIsLoading(true);
    setError(null);

    try {
      const formattedDate = date.toISOString().split('T')[0];

      // Fetch bookings for the selected date
      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('*')
        .eq('date', formattedDate);

      if (bookingsError) throw bookingsError;

      // Generate all slots from 7 AM to 10 PM
      const allSlots: Slot[] = [];
      for (let hour = 7; hour <= 22; hour++) {
        const startTime = `${hour.toString().padStart(2, '0')}:00`;
        const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;
        const timeSlot = `${startTime} - ${endTime}`;

        // Check if slot is booked
        const booking = bookings?.find(b => b.slot === timeSlot);

        allSlots.push({
          id: `slot-${hour}`,
          time: timeSlot,
          status: booking ? 'booked' : 'available',
          booking_id: booking?.id
        });
      }

      setSlots(allSlots);
    } catch (error) {
      console.error('Error fetching slots:', error);
      setError('Failed to load available slots. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
    setSelectedSlot(null);
  };

  const handleSlotSelect = (slot: Slot) => {
    if (slot.status === 'available') {
      setSelectedSlot(slot);
    }
  };

  const handleBooking = async () => {
    if (!selectedSlot || !name || !phone) {
      setError('Please fill in all fields');
      return;
    }

    if (phone.length < 10) {
      setError('Please enter a valid phone number');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Create a booking record
      const { data: booking, error: bookingError } = await supabase
        .from('bookings')
        .insert([
          {
            user_id: user?.id,
            date: selectedDate.toISOString().split('T')[0],
            slot: selectedSlot.time,
            name,
            phone,
            payment_status: 'pending',
            amount: 200 // Fixed amount of 200 rupees
          }
        ])
        .select()
        .single();

      if (bookingError) throw bookingError;

      // Redirect to payment page
      navigate(`/payment/${booking.id}`);
    } catch (error) {
      console.error('Error creating booking:', error);
      setError('Failed to create booking. Please try again.');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-dark">Book Your Slot</h1>
            <div className="space-x-4">
              <Button
                variant="outline"
                onClick={() => navigate('/')}
              >
                Home
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/history')}
              >
                History
              </Button>
            </div>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}

          <Card>
            {/* Date Selection */}
            <CardHeader>
              <h2 className="text-xl font-semibold mb-4">Select Date</h2>
              <DatePicker
                selected={selectedDate}
                onChange={handleDateChange}
                minDate={new Date()}
                maxDate={maxDate}
                dateFormat="MMMM d, yyyy"
                className="w-full p-2 border rounded"
              />
            </CardHeader>

            {/* Time Slots */}
            <CardBody className="border-t border-b">
              <h2 className="text-xl font-semibold mb-4">Available Slots</h2>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {slots.map((slot) => (
                    <motion.button
                      key={slot.id}
                      whileHover={{ scale: slot.status === 'available' ? 1.05 : 1 }}
                      whileTap={{ scale: slot.status === 'available' ? 0.95 : 1 }}
                      onClick={() => handleSlotSelect(slot)}
                      className={`p-3 rounded-lg text-center transition-colors ${
                        selectedSlot?.id === slot.id
                          ? 'bg-primary text-white'
                          : slot.status === 'available'
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 cursor-not-allowed'
                      }`}
                      disabled={slot.status !== 'available'}
                    >
                      {slot.time}
                    </motion.button>
                  ))}
                </div>
              )}
            </CardBody>

            {/* Booking Form */}
            {selectedSlot && (
              <CardBody>
                <h2 className="text-xl font-semibold mb-4">Your Information</h2>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                      Full Name
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      className="w-full p-2 border rounded"
                      placeholder="Enter your full name"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="w-full p-2 border rounded"
                      placeholder="Enter your phone number"
                      required
                    />
                  </div>

                  <div className="pt-4">
                    <Button
                      variant="primary"
                      onClick={handleBooking}
                      disabled={!name || !phone}
                      isLoading={isLoading}
                      fullWidth
                    >
                      Book Now - ₹200
                    </Button>
                  </div>
                </div>
              </CardBody>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Booking;
