"""
Agilent 34401A Digital Multimeter Driver
6.5 digit precision DMM with multiple measurement functions
"""

from typing import Dict, Any, List, Optional
import time
from .base_instrument import BaseInstrument


class Agilent34401A(BaseInstrument):
    """Driver for Agilent 34401A Digital Multimeter"""
    
    def __init__(self, name: str = "34401A", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        
        # Measurement functions
        self.functions = {
            'DC_VOLTAGE': 'VOLT:DC',
            'AC_VOLTAGE': 'VOLT:AC',
            'DC_CURRENT': 'CURR:DC',
            'AC_CURRENT': 'CURR:AC',
            'RESISTANCE_2W': 'RES',
            'RESISTANCE_4W': 'FRES',
            'FREQUENCY': 'FREQ',
            'PERIOD': 'PER',
            'CONTINUITY': 'CONT',
            'DIODE': 'DIOD'
        }
        
        # Voltage ranges (V)
        self.voltage_ranges = [0.1, 1, 10, 100, 1000]
        
        # Current ranges (A)
        self.current_ranges = [0.01, 0.1, 1, 3]
        
        # Resistance ranges (Ohms)
        self.resistance_ranges = [100, 1000, 10000, 100000, 1000000, 10000000, 100000000]
        
        # Current settings
        self.current_function = 'DC_VOLTAGE'
        self.current_range = 'AUTO'
        self.resolution = 'DEF'
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get DMM capabilities"""
        return {
            'type': 'multimeter',
            'functions': list(self.functions.keys()),
            'voltage_ranges': self.voltage_ranges,
            'current_ranges': self.current_ranges,
            'resistance_ranges': self.resistance_ranges,
            'digits': 6.5,
            'accuracy': {
                'DC_VOLTAGE': '0.0035%',
                'AC_VOLTAGE': '0.06%',
                'DC_CURRENT': '0.05%',
                'AC_CURRENT': '0.1%',
                'RESISTANCE_2W': '0.01%',
                'RESISTANCE_4W': '0.008%'
            }
        }
    
    def initialize(self) -> bool:
        """Initialize DMM to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(1)
            
            # Set to DC voltage measurement
            self.set_function('DC_VOLTAGE')
            self.set_range('AUTO')
            
            # Clear any errors
            self.write("*CLS")
            
            self.logger.info("Agilent 34401A initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Agilent 34401A: {e}")
            return False
    
    # Function Control
    def set_function(self, function: str) -> bool:
        """Set measurement function"""
        function = function.upper()
        if function not in self.functions:
            raise ValueError(f"Function must be one of: {list(self.functions.keys())}")
        
        command = self.functions[function]
        success = self.write(f"CONF:{command}")
        if success:
            self.current_function = function
        return success
    
    def get_function(self) -> str:
        """Get current measurement function"""
        response = self.query("CONF?")
        # Parse response to determine function
        for func_name, func_cmd in self.functions.items():
            if func_cmd.replace(':', '') in response.upper():
                return func_name
        return 'DC_VOLTAGE'  # Default
    
    # Range Control
    def set_range(self, range_value) -> bool:
        """Set measurement range (AUTO or specific value)"""
        if isinstance(range_value, str) and range_value.upper() == 'AUTO':
            command = f"{self.functions[self.current_function]}:RANGE:AUTO ON"
        else:
            command = f"{self.functions[self.current_function]}:RANGE {range_value}"
        
        success = self.write(command)
        if success:
            self.current_range = range_value
        return success
    
    def get_range(self) -> float:
        """Get current measurement range"""
        response = self.query(f"{self.functions[self.current_function]}:RANGE?")
        return float(response)
    
    def set_auto_range(self, enabled: bool) -> bool:
        """Enable or disable auto ranging"""
        state = "ON" if enabled else "OFF"
        return self.write(f"{self.functions[self.current_function]}:RANGE:AUTO {state}")
    
    def get_auto_range(self) -> bool:
        """Get auto range state"""
        response = self.query(f"{self.functions[self.current_function]}:RANGE:AUTO?")
        return response.strip() == "1"
    
    # Resolution Control
    def set_resolution(self, resolution) -> bool:
        """Set measurement resolution (DEF, MIN, MAX, or specific value)"""
        command = f"{self.functions[self.current_function]}:RES {resolution}"
        success = self.write(command)
        if success:
            self.resolution = resolution
        return success
    
    def get_resolution(self) -> float:
        """Get current measurement resolution"""
        response = self.query(f"{self.functions[self.current_function]}:RES?")
        return float(response)
    
    # Measurement Methods
    def read_measurement(self) -> float:
        """Read current measurement"""
        response = self.query("READ?")
        return float(response)
    
    def measure_dc_voltage(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure DC voltage"""
        self.set_function('DC_VOLTAGE')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_ac_voltage(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure AC voltage"""
        self.set_function('AC_VOLTAGE')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_dc_current(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure DC current"""
        self.set_function('DC_CURRENT')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_ac_current(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure AC current"""
        self.set_function('AC_CURRENT')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_resistance_2w(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure 2-wire resistance"""
        self.set_function('RESISTANCE_2W')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_resistance_4w(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure 4-wire resistance"""
        self.set_function('RESISTANCE_4W')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_frequency(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure frequency"""
        self.set_function('FREQUENCY')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def measure_period(self, range_value='AUTO', resolution='DEF') -> float:
        """Measure period"""
        self.set_function('PERIOD')
        self.set_range(range_value)
        self.set_resolution(resolution)
        return self.read_measurement()
    
    def test_continuity(self) -> bool:
        """Test continuity (returns True if continuous)"""
        self.set_function('CONTINUITY')
        reading = self.read_measurement()
        return reading < 1000  # Typically < 1kΩ indicates continuity
    
    def test_diode(self) -> float:
        """Test diode (returns forward voltage drop)"""
        self.set_function('DIODE')
        return self.read_measurement()
    
    # Statistics and Multiple Readings
    def read_multiple(self, count: int = 10) -> List[float]:
        """Read multiple measurements"""
        readings = []
        for _ in range(count):
            readings.append(self.read_measurement())
            time.sleep(0.1)  # Small delay between readings
        return readings
    
    def get_statistics(self, count: int = 10) -> Dict[str, float]:
        """Get measurement statistics"""
        readings = self.read_multiple(count)
        
        if not readings:
            return {}
        
        mean_val = sum(readings) / len(readings)
        min_val = min(readings)
        max_val = max(readings)
        
        # Calculate standard deviation
        variance = sum((x - mean_val) ** 2 for x in readings) / len(readings)
        std_dev = variance ** 0.5
        
        return {
            'mean': mean_val,
            'min': min_val,
            'max': max_val,
            'std_dev': std_dev,
            'count': len(readings),
            'range': max_val - min_val
        }
    
    # Trigger Control
    def set_trigger_source(self, source: str) -> bool:
        """Set trigger source (IMM, EXT, BUS)"""
        source = source.upper()
        if source not in ['IMM', 'EXT', 'BUS']:
            raise ValueError("Trigger source must be IMM, EXT, or BUS")
        
        return self.write(f"TRIG:SOUR {source}")
    
    def get_trigger_source(self) -> str:
        """Get trigger source"""
        return self.query("TRIG:SOUR?")
    
    def trigger(self) -> bool:
        """Send software trigger"""
        return self.write("*TRG")
    
    # System Functions
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status"""
        base_status = super().get_status()
        
        if self.connected:
            try:
                dmm_status = {
                    'function': self.get_function(),
                    'range': self.get_range(),
                    'auto_range': self.get_auto_range(),
                    'resolution': self.get_resolution(),
                    'current_reading': self.read_measurement()
                }
                base_status.update(dmm_status)
            except Exception as e:
                self.logger.error(f"Failed to get status: {e}")
        
        return base_status
    
    def check_errors(self) -> List[str]:
        """Check for instrument errors"""
        errors = []
        try:
            while True:
                error = self.query("SYST:ERR?")
                if error.startswith("0,"):
                    break
                errors.append(error)
        except Exception as e:
            self.logger.error(f"Failed to check errors: {e}")
        
        return errors
    
    def self_test(self) -> bool:
        """Perform self test"""
        try:
            result = self.query("*TST?")
            return result.strip() == "0"  # 0 indicates pass
        except Exception as e:
            self.logger.error(f"Self test failed: {e}")
            return False
    
    # Calibration (read-only for user)
    def get_calibration_count(self) -> int:
        """Get calibration count"""
        try:
            response = self.query("CAL:COUNT?")
            return int(response)
        except Exception as e:
            self.logger.error(f"Failed to get calibration count: {e}")
            return -1
    
    def get_calibration_date(self) -> str:
        """Get last calibration date"""
        try:
            return self.query("CAL:DATE?")
        except Exception as e:
            self.logger.error(f"Failed to get calibration date: {e}")
            return "Unknown"
