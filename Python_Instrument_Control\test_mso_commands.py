#!/usr/bin/env python3
"""
Test script for MSO SCPI commands
This script helps verify which SCPI commands work with your specific MSO model
"""

import pyvisa
import time

def test_mso_commands():
    """Test various MSO SCPI commands"""
    
    # Connect to MSO (update address as needed)
    rm = pyvisa.ResourceManager()
    
    # Try to find MSO
    resources = rm.list_resources()
    print("Available VISA resources:")
    for i, resource in enumerate(resources):
        print(f"  {i}: {resource}")
    
    # Use the first TCP resource (usually the MSO)
    mso_address = None
    for resource in resources:
        if 'TCPIP' in resource and 'inst0' in resource:
            mso_address = resource
            break
    
    if not mso_address:
        print("No MSO found!")
        return
    
    print(f"\nConnecting to: {mso_address}")
    
    try:
        mso = rm.open_resource(mso_address)
        mso.timeout = 5000
        
        # Get ID
        idn = mso.query("*IDN?")
        print(f"Connected to: {idn}")
        
        # Test channel label commands
        print("\n=== Testing Channel Label Commands ===")
        label_commands = [
            'CH1:LABel:NAMe "Test1"',
            'CH1:LABel "Test2"',
            'DISPlay:CH1:LABel "Test3"',
            'DISPlay:LABel:CH1 "Test4"'
        ]
        
        for cmd in label_commands:
            try:
                mso.write(cmd)
                print(f"✓ {cmd} - SUCCESS")
                time.sleep(0.1)
            except Exception as e:
                print(f"✗ {cmd} - FAILED: {e}")
        
        # Test screenshot commands
        print("\n=== Testing Screenshot Commands ===")
        screenshot_commands = [
            ['SAVE:IMAGE:FILEFORMAT PNG', 'SAVE:IMAGE "test1.png"'],
            ['HARDCopy:FORMat PNG', 'HARDCopy:FILEName "test2.png"', 'HARDCopy START'],
            ['EXPORT:IMAGE "test3.png"']
        ]
        
        for cmd_sequence in screenshot_commands:
            try:
                for cmd in cmd_sequence:
                    mso.write(cmd)
                    time.sleep(0.1)
                print(f"✓ {cmd_sequence} - SUCCESS")
            except Exception as e:
                print(f"✗ {cmd_sequence} - FAILED: {e}")
        
        # Test display style commands
        print("\n=== Testing Display Style Commands ===")
        display_commands = [
            "DISPLAY:WAVEVIEW1:VIEW:STYLE OVERLAY",
            "DISPLAY:VIEW:STYLE OVERLAY", 
            "DISPLAY:STYLE OVERLAY",
            "WAVEVIEW1:VIEW:STYLE OVERLAY"
        ]
        
        for cmd in display_commands:
            try:
                mso.write(cmd)
                print(f"✓ {cmd} - SUCCESS")
                time.sleep(0.1)
            except Exception as e:
                print(f"✗ {cmd} - FAILED: {e}")
        
        # Test trigger level commands
        print("\n=== Testing Trigger Level Commands ===")
        trigger_commands = [
            "TRIGGER:A:LEVEL:CH1 1.0",
            "TRIGGER:A:LEVEL 1.0"
        ]
        
        for cmd in trigger_commands:
            try:
                mso.write(cmd)
                print(f"✓ {cmd} - SUCCESS")
                time.sleep(0.1)
            except Exception as e:
                print(f"✗ {cmd} - FAILED: {e}")
        
        mso.close()
        print("\n=== Test Complete ===")
        
    except Exception as e:
        print(f"Connection failed: {e}")

if __name__ == "__main__":
    test_mso_commands()
