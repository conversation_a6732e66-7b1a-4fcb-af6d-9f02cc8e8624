@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
body {
  font-family: 'Inter', sans-serif;
  margin: 0;
  padding: 0;
}

.text-primary {
  color: #0ea5e9;
}

.bg-primary {
  background-color: #0ea5e9;
}

.hover\:bg-primary\/90:hover {
  background-color: rgba(14, 165, 233, 0.9);
}

.hover\:text-primary:hover {
  color: #0ea5e9;
}

.from-primary\/10 {
  --tw-gradient-from: rgba(14, 165, 233, 0.1);
}

.bg-dark {
  background-color: #1e293b;
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900 min-h-screen;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-primary text-white hover:bg-primary/90;
  }

  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary/90;
  }

  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}
