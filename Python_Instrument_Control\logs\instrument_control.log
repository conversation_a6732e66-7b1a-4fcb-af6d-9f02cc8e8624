2025-06-12 15:56:06,325 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 15:56:06,610 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 15:56:06,634 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 15:56:07,506 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 15:56:07,506 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 15:56:07,836 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 15:56:17,474 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 15:57:36,095 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 15:57:36,269 - __main__ - INFO - Application closed normally
2025-06-12 15:59:47,540 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 15:59:47,663 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 15:59:47,670 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 15:59:48,078 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 15:59:48,079 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 15:59:48,307 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:00:12,319 - utils.visa_manager - INFO - Connected to MSO: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-12 16:00:12,319 - instruments.base_instrument.Instrument.MSO - INFO - Connected to MSO at TCPIP0::***********::inst0::INSTR
2025-06-12 16:00:14,438 - instruments.base_instrument.Instrument.MSO - INFO - MSO58B initialized successfully
2025-06-12 16:00:46,643 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO68B,B028273,CF:91.1CT FV:1.44.3.433
2025-06-12 16:00:46,643 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::************::inst0::INSTR
2025-06-12 16:00:48,750 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 16:01:24,189 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 16:01:24,202 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 16:01:24,218 - utils.visa_manager - INFO - Disconnected instrument: MSO
2025-06-12 16:01:24,218 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 16:01:24,344 - __main__ - INFO - Application closed normally
2025-06-12 16:06:32,948 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:06:33,094 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:06:33,106 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:06:33,616 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:06:33,616 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:06:33,851 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:06:40,975 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO68B,B028273,CF:91.1CT FV:1.44.3.433
2025-06-12 16:06:40,975 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::************::inst0::INSTR
2025-06-12 16:06:43,360 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 16:08:36,747 - utils.visa_manager - INFO - Instrument Tektronix MSO58B already connected
2025-06-12 16:08:36,747 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::************::inst0::INSTR
2025-06-12 16:08:38,867 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 16:10:16,114 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 16:10:16,131 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 16:10:16,131 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 16:10:16,276 - __main__ - INFO - Application closed normally
2025-06-12 16:11:56,707 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:11:56,922 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:11:56,932 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:11:57,432 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:11:57,433 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:11:57,662 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:12:03,910 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO68B,B028273,CF:91.1CT FV:1.44.3.433
2025-06-12 16:12:03,910 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::************::inst0::INSTR
2025-06-12 16:12:06,021 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 16:12:39,212 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 ON
2025-06-12 16:12:50,073 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Started acquisition
2025-06-12 16:12:52,811 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Stopped acquisition
2025-06-12 16:12:53,658 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Single acquisition triggered
2025-06-12 16:12:54,844 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Auto setup completed
2025-06-12 16:12:56,500 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Started acquisition
2025-06-12 16:13:02,850 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 ON
2025-06-12 16:13:54,382 - utils.visa_manager - INFO - Connected to MSO: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-12 16:13:54,383 - instruments.base_instrument.Instrument.MSO - INFO - Connected to MSO at TCPIP0::***********::inst0::INSTR
2025-06-12 16:13:56,601 - instruments.base_instrument.Instrument.MSO - INFO - MSO58B initialized successfully
2025-06-12 16:14:02,570 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH1 ON
2025-06-12 16:14:10,974 - gui.instrument_control_widget.Widget.MSO - ERROR - Invalid vertical scale value
2025-06-12 16:14:22,086 - gui.instrument_control_widget.Widget.MSO - ERROR - Invalid vertical position value
2025-06-12 16:14:40,936 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH1 OFF
2025-06-12 16:14:41,688 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH1 ON
2025-06-12 16:14:42,378 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH1 OFF
2025-06-12 16:14:42,926 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH1 ON
2025-06-12 16:14:55,312 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH2 ON
2025-06-12 16:14:55,862 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH2 OFF
2025-06-12 16:14:56,445 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH2 ON
2025-06-12 16:14:58,968 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH3 ON
2025-06-12 16:15:01,896 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH4 ON
2025-06-12 16:15:03,031 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH5 ON
2025-06-12 16:15:05,021 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH5 OFF
2025-06-12 16:15:07,259 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH8 ON
2025-06-12 16:15:09,856 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH7 ON
2025-06-12 16:15:11,086 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH7 OFF
2025-06-12 16:15:11,878 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH8 OFF
2025-06-12 16:15:14,074 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH6 ON
2025-06-12 16:15:14,765 - gui.instrument_control_widget.Widget.MSO - INFO - Set CH6 OFF
2025-06-12 16:15:17,696 - gui.instrument_control_widget.Widget.MSO - ERROR - Invalid horizontal scale value
2025-06-12 16:15:29,281 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger source to CH2
2025-06-12 16:16:52,759 - gui.instrument_control_widget.Widget.MSO - INFO - Started acquisition
2025-06-12 16:16:54,625 - gui.instrument_control_widget.Widget.MSO - INFO - Stopped acquisition
2025-06-12 16:16:56,105 - gui.instrument_control_widget.Widget.MSO - INFO - Started acquisition
2025-06-12 16:16:57,895 - gui.instrument_control_widget.Widget.MSO - INFO - Stopped acquisition
2025-06-12 16:16:59,198 - gui.instrument_control_widget.Widget.MSO - INFO - Single acquisition triggered
2025-06-12 16:17:03,015 - gui.instrument_control_widget.Widget.MSO - INFO - Single acquisition triggered
2025-06-12 16:17:06,443 - gui.instrument_control_widget.Widget.MSO - INFO - Auto setup completed
2025-06-12 16:17:08,811 - gui.instrument_control_widget.Widget.MSO - INFO - Single acquisition triggered
2025-06-12 16:17:10,662 - gui.instrument_control_widget.Widget.MSO - INFO - Auto setup completed
2025-06-12 16:17:11,869 - gui.instrument_control_widget.Widget.MSO - INFO - Stopped acquisition
2025-06-12 16:17:12,463 - gui.instrument_control_widget.Widget.MSO - INFO - Started acquisition
2025-06-12 16:17:19,096 - gui.instrument_control_widget.Widget.MSO - INFO - Stopped acquisition
2025-06-12 16:17:21,854 - gui.instrument_control_widget.Widget.MSO - INFO - Started acquisition
2025-06-12 16:17:26,363 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger slope to FALL
2025-06-12 16:17:31,125 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger level to 2.0 V
2025-06-12 16:17:32,565 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger level to 2.0 V
2025-06-12 16:17:32,772 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger level to 2.0 V
2025-06-12 16:17:32,947 - gui.instrument_control_widget.Widget.MSO - INFO - Set trigger level to 2.0 V
2025-06-12 16:22:32,436 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 16:22:32,445 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 16:22:32,452 - instruments.base_instrument.Instrument.MSO - INFO - Disconnected from MSO
2025-06-12 16:22:32,452 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 16:22:32,453 - utils.visa_manager - INFO - Disconnected instrument: MSO
2025-06-12 16:22:32,900 - __main__ - INFO - Application closed normally
2025-06-12 16:24:00,993 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:24:01,170 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:24:01,177 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:24:01,727 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:24:01,727 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:24:01,978 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:31:26,124 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 16:31:26,264 - __main__ - INFO - Application closed normally
2025-06-12 16:32:54,408 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:32:54,592 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:32:54,601 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:32:55,163 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:32:55,164 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:32:55,486 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:33:17,332 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO68B,B028273,CF:91.1CT FV:1.44.3.433
2025-06-12 16:33:17,333 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::************::inst0::INSTR
2025-06-12 16:33:19,443 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 16:33:24,191 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 ON
2025-06-12 16:33:26,120 - gui.instrument_control_widget.Widget.Tektronix MSO58B - ERROR - Invalid vertical scale value
2025-06-12 16:33:34,571 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Auto setup completed
2025-06-12 16:33:38,166 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Started acquisition
2025-06-12 16:33:39,174 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Stopped acquisition
2025-06-12 16:34:12,635 - utils.visa_manager - INFO - Connected to A: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-12 16:34:12,636 - instruments.base_instrument.Instrument.A - INFO - Connected to A at TCPIP0::***********::inst0::INSTR
2025-06-12 16:34:14,797 - instruments.base_instrument.Instrument.A - INFO - MSO58B initialized successfully
2025-06-12 16:34:35,817 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 ON
2025-06-12 16:34:41,541 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 OFF
2025-06-12 16:34:41,976 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 ON
2025-06-12 16:34:43,386 - gui.instrument_control_widget.Widget.A - ERROR - Invalid vertical scale value
2025-06-12 16:34:46,585 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical scale to 2.0 V/div
2025-06-12 16:34:50,349 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical scale to 3.0 V/div
2025-06-12 16:34:55,121 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical scale to 5.0 V/div
2025-06-12 16:34:59,238 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical position to 1.0 V
2025-06-12 16:35:07,268 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical position to 2.0 V
2025-06-12 16:35:14,400 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical position to -2.0 V
2025-06-12 16:35:21,320 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 vertical position to -3.0 V
2025-06-12 16:35:24,422 - gui.instrument_control_widget.Widget.A - INFO - Set CH2 ON
2025-06-12 16:35:38,451 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 coupling to AC
2025-06-12 16:35:42,155 - gui.instrument_control_widget.Widget.A - INFO - Set CH1 coupling to DC
2025-06-12 16:35:57,292 - gui.instrument_control_widget.Widget.A - INFO - Set CH6 ON
2025-06-12 16:36:03,065 - gui.instrument_control_widget.Widget.A - INFO - Set CH6 vertical scale to 5.0 V/div
2025-06-12 16:36:11,918 - gui.instrument_control_widget.Widget.A - ERROR - Invalid horizontal scale value
2025-06-12 16:36:14,194 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal scale to 1.0 s/div
2025-06-12 16:36:20,353 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal scale to 0.1 s/div
2025-06-12 16:36:28,132 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal position to 1.0 s
2025-06-12 16:36:35,076 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal position to 0.0 s
2025-06-12 16:36:39,768 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal position to 100.0 s
2025-06-12 16:36:46,430 - gui.instrument_control_widget.Widget.A - INFO - Set horizontal position to 50.0 s
2025-06-12 16:36:54,939 - gui.instrument_control_widget.Widget.A - INFO - Set trigger source to CH2
2025-06-12 16:36:57,846 - gui.instrument_control_widget.Widget.A - INFO - Set trigger slope to FALL
2025-06-12 16:37:01,497 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:04,280 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:15,356 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:15,958 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:16,209 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:16,372 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:16,562 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:18,136 - gui.instrument_control_widget.Widget.A - INFO - Set trigger slope to RISE
2025-06-12 16:37:18,821 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:25,670 - gui.instrument_control_widget.Widget.A - INFO - Set trigger source to CH1
2025-06-12 16:37:27,431 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:29,077 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:29,293 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:29,480 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:29,694 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:29,874 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:30,071 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:30,268 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:30,487 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:30,648 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:30,828 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:31,037 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:31,228 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:31,413 - gui.instrument_control_widget.Widget.A - INFO - Set trigger level to 4.0 V
2025-06-12 16:37:35,392 - gui.instrument_control_widget.Widget.A - INFO - Started acquisition
2025-06-12 16:37:36,232 - gui.instrument_control_widget.Widget.A - INFO - Stopped acquisition
2025-06-12 16:37:38,045 - gui.instrument_control_widget.Widget.A - INFO - Single acquisition triggered
2025-06-12 16:37:39,509 - gui.instrument_control_widget.Widget.A - INFO - Single acquisition triggered
2025-06-12 16:37:40,906 - gui.instrument_control_widget.Widget.A - INFO - Continuous acquisition mode set
2025-06-12 16:37:42,510 - gui.instrument_control_widget.Widget.A - INFO - Single acquisition triggered
2025-06-12 16:37:44,340 - gui.instrument_control_widget.Widget.A - INFO - Continuous acquisition mode set
2025-06-12 16:39:36,345 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 16:39:36,360 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 16:39:36,364 - instruments.base_instrument.Instrument.A - INFO - Disconnected from A
2025-06-12 16:39:36,365 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 16:39:36,365 - utils.visa_manager - INFO - Disconnected instrument: A
2025-06-12 16:39:36,962 - __main__ - INFO - Application closed normally
2025-06-12 16:43:21,779 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:43:21,889 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:43:21,899 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:43:22,510 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:43:22,510 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:43:22,802 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 16:44:44,773 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 16:44:44,977 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 16:44:44,989 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 16:44:45,317 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 16:44:45,317 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 16:44:45,526 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 18:03:00,460 - __main__ - INFO - Application interrupted by user
2025-06-12 18:03:07,601 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 18:03:07,737 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 18:03:07,747 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 18:03:08,265 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 18:03:08,266 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 18:03:08,489 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 18:03:13,662 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-12 18:03:13,663 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::***********::inst0::INSTR
2025-06-12 18:03:15,817 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 18:03:33,756 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 ON
2025-06-12 18:03:41,186 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 label to 'ABC'
2025-06-12 18:03:42,803 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 label to 'ABC'
2025-06-12 18:03:45,530 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 label to 'ABC'
2025-06-12 18:03:48,874 - gui.instrument_control_widget.Widget.Tektronix MSO58B - ERROR - Invalid vertical scale value
2025-06-12 18:03:52,836 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 vertical scale to 4.0 V/div
2025-06-12 18:03:59,511 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 vertical position to -4.0 V
2025-06-12 18:04:02,017 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 label to 'ABC'
2025-06-12 18:04:12,309 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250612_180412.png
2025-06-12 18:04:12,310 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250612_180412.png
2025-06-12 18:04:40,136 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250612_180440.png
2025-06-12 18:04:40,137 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250612_180440.png
2025-06-12 18:05:48,611 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set trigger level to 4.0 V
2025-06-12 18:05:52,143 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set trigger slope to FALL
2025-06-12 18:05:54,354 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set trigger slope to EITHER
2025-06-12 18:05:56,797 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set trigger mode to NORMAL
2025-06-12 18:05:59,704 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set trigger mode to AUTO
2025-06-12 18:06:10,018 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH8 coupling to AC
2025-06-12 18:06:20,953 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 label to 'Car'
2025-06-12 18:06:21,768 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 ON
2025-06-12 18:06:24,912 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 label to 'Car'
2025-06-12 18:06:32,491 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 18:06:32,525 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 18:06:32,525 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 18:06:33,268 - __main__ - INFO - Application closed normally
2025-06-12 18:09:27,441 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 18:09:27,610 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 18:09:27,619 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 18:09:28,326 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 18:09:28,326 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 18:09:28,581 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 18:12:53,783 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 18:12:53,961 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 18:12:53,971 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 18:12:54,298 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 18:12:54,299 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 18:12:54,543 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 18:25:37,948 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 18:25:38,103 - __main__ - INFO - Application closed normally
2025-06-12 18:25:47,606 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-12 18:25:47,740 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-12 18:25:47,748 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-12 18:25:48,257 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-12 18:25:48,257 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-12 18:25:48,483 - utils.visa_manager - INFO - Found 3 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR']
2025-06-12 18:25:52,697 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-12 18:25:52,697 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::***********::inst0::INSTR
2025-06-12 18:25:54,793 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-12 18:25:58,840 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 ON
2025-06-12 18:26:04,290 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Channel 1 label set using: CH1:LABel:NAMe "AA"
2025-06-12 18:26:04,291 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH1 label to 'AA'
2025-06-12 18:26:09,633 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 ON
2025-06-12 18:26:15,458 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Channel 2 label set using: CH2:LABel:NAMe "CAR"
2025-06-12 18:26:15,458 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set CH2 label to 'CAR'
2025-06-12 18:26:23,679 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Display style set to STACKED using: DISPLAY:WAVEVIEW1:VIEW:STYLE STACKED
2025-06-12 18:26:23,679 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set display style to STACKED
2025-06-12 18:26:26,276 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Display style set to OVERLAY using: DISPLAY:WAVEVIEW1:VIEW:STYLE OVERLAY
2025-06-12 18:26:26,277 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set display style to OVERLAY
2025-06-12 18:26:28,286 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Display style set to STACKED using: DISPLAY:WAVEVIEW1:VIEW:STYLE STACKED
2025-06-12 18:26:28,287 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Set display style to STACKED
2025-06-12 18:26:40,237 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Screenshot saved using: ['SAVE:IMAGE:FILEFORMAT PNG', 'SAVE:IMAGE "C:\\Users\\<USER>\\Documents\\augment-projects\\Python_Instrument_Control\\screenshots\\MSO_screenshot_20250612_182640.png"']
2025-06-12 18:26:40,238 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250612_182640.png
2025-06-12 18:26:52,328 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Stopped acquisition
2025-06-12 18:26:52,854 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Started acquisition
2025-06-12 18:27:07,971 - gui.main_window.MainWindow - INFO - Application closing
2025-06-12 18:27:07,975 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-12 18:27:07,976 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-12 18:27:08,464 - __main__ - INFO - Application closed normally
2025-06-13 10:17:35,606 - __main__ - INFO - Starting Lab Instrument Control System
2025-06-13 10:17:35,746 - utils.visa_manager - INFO - Using NI-VISA backend
2025-06-13 10:17:35,756 - utils.visa_manager - INFO - Loaded configuration from config/instruments.yaml
2025-06-13 10:17:36,327 - gui.main_window.MainWindow - INFO - Main GUI window initialized
2025-06-13 10:17:36,327 - gui.main_window.MainWindow - INFO - Starting GUI application
2025-06-13 10:17:36,639 - utils.visa_manager - INFO - Found 4 VISA resources: ['TCPIP0::************::inst0::INSTR', 'TCPIP0::***********::inst0::INSTR', 'ASRL3::INSTR', 'ASRL12::INSTR']
2025-06-13 10:17:43,339 - utils.visa_manager - INFO - Connected to Tektronix MSO58B: TEKTRONIX,MSO58B,B030549,CF:91.1CT FV:2.10.5.1825
2025-06-13 10:17:43,339 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Connected to Tektronix MSO58B at TCPIP0::***********::inst0::INSTR
2025-06-13 10:17:45,496 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - MSO58B initialized successfully
2025-06-13 10:17:57,649 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Screenshot saved using method 1: ['SAVE:IMAGE:FILEFORMAT PNG', 'SAVE:IMAGE "C:/Users/<USER>/Documents/augment-projects/Python_Instrument_Control/screenshots/MSO_screenshot_20250613_101756.png"']
2025-06-13 10:17:57,650 - gui.instrument_control_widget.Widget.Tektronix MSO58B - INFO - Screenshot saved to screenshots\MSO_screenshot_20250613_101756.png
2025-06-13 10:22:10,328 - gui.main_window.MainWindow - INFO - Application closing
2025-06-13 10:22:10,333 - instruments.base_instrument.Instrument.Tektronix MSO58B - INFO - Disconnected from Tektronix MSO58B
2025-06-13 10:22:10,333 - utils.visa_manager - INFO - Disconnected instrument: Tektronix MSO58B
2025-06-13 10:22:11,033 - __main__ - INFO - Application closed normally
