"""
Create a professional installer for Serial Monitor application
"""
import os
import subprocess
import sys
import urllib.request
import zipfile
import shutil

def download_inno_setup():
    """Download and install Inno Setup if not available"""
    inno_setup_path = r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    
    if os.path.exists(inno_setup_path):
        print("Inno Setup found!")
        return inno_setup_path
    
    print("Inno Setup not found. Please install it manually:")
    print("1. Go to: https://jrsoftware.org/isdl.php")
    print("2. Download 'Inno Setup 6.x.x'")
    print("3. Install it with default settings")
    print("4. Run this script again")
    
    # Try to open the download page
    try:
        import webbrowser
        webbrowser.open("https://jrsoftware.org/isdl.php")
    except:
        pass
    
    return None

def check_required_files():
    """Check if all required files exist"""
    required_files = [
        "dist/SerialMonitor.exe",
        "serial_monitor_icon.ico",
        "INSTALLATION_GUIDE.md",
        "serial_monitor_installer.iss"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("Error: Missing required files:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    return True

def create_installer():
    """Create the installer using Inno Setup"""
    
    print("Serial Monitor - Installer Creator")
    print("=" * 40)
    
    # Check required files
    if not check_required_files():
        print("\nPlease ensure all required files exist before creating installer.")
        return False
    
    # Find Inno Setup
    inno_setup_path = download_inno_setup()
    if not inno_setup_path:
        return False
    
    # Create installer output directory
    os.makedirs("installer_output", exist_ok=True)
    
    # Build installer
    print("\nBuilding installer...")
    try:
        cmd = [inno_setup_path, "serial_monitor_installer.iss"]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("\n" + "=" * 50)
            print("INSTALLER CREATED SUCCESSFULLY!")
            print("=" * 50)
            
            # Find the created installer
            installer_files = [f for f in os.listdir("installer_output") if f.endswith(".exe")]
            if installer_files:
                installer_path = os.path.join("installer_output", installer_files[0])
                installer_size = os.path.getsize(installer_path) / (1024 * 1024)
                
                print(f"Installer: {os.path.abspath(installer_path)}")
                print(f"Size: {installer_size:.1f} MB")
                print("\nFeatures:")
                print("✓ Professional Windows installer")
                print("✓ Start Menu shortcuts")
                print("✓ Desktop shortcut (optional)")
                print("✓ Proper uninstaller")
                print("✓ Registry entries")
                print("✓ Custom icon")
                print("\nYou can now distribute this installer to install Serial Monitor on any PC!")
                
                return True
            else:
                print("Error: Installer file not found in output directory")
                return False
        else:
            print(f"Error building installer:")
            print(result.stdout)
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"Error running Inno Setup: {e}")
        return False

def create_portable_package():
    """Create a portable package as alternative"""
    print("\nCreating portable package as alternative...")
    
    # Create portable directory
    portable_dir = "SerialMonitor_Portable"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    
    os.makedirs(portable_dir)
    
    # Copy files
    files_to_copy = [
        ("dist/SerialMonitor.exe", "SerialMonitor.exe"),
        ("serial_monitor_icon.ico", "serial_monitor_icon.ico"),
        ("INSTALLATION_GUIDE.md", "README.md"),
        ("serial_monitor_icon.png", "icon_preview.png")
    ]
    
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(portable_dir, dst))
    
    # Create run script
    with open(os.path.join(portable_dir, "Run_SerialMonitor.bat"), "w") as f:
        f.write("@echo off\n")
        f.write("echo Starting Serial Monitor...\n")
        f.write("SerialMonitor.exe\n")
        f.write("pause\n")
    
    # Create info file
    with open(os.path.join(portable_dir, "PORTABLE_INFO.txt"), "w") as f:
        f.write("Serial Monitor - Portable Version\n")
        f.write("=" * 35 + "\n\n")
        f.write("This is a portable version that doesn't require installation.\n\n")
        f.write("To run:\n")
        f.write("1. Double-click 'SerialMonitor.exe' or 'Run_SerialMonitor.bat'\n")
        f.write("2. No installation required\n")
        f.write("3. Can be run from USB drive or any folder\n\n")
        f.write("Files included:\n")
        f.write("- SerialMonitor.exe (Main application)\n")
        f.write("- serial_monitor_icon.ico (Application icon)\n")
        f.write("- README.md (Documentation)\n")
        f.write("- Run_SerialMonitor.bat (Launcher script)\n")
    
    print(f"Portable package created: {os.path.abspath(portable_dir)}")
    return True

def main():
    """Main function"""
    
    # Always create portable package
    create_portable_package()
    
    # Try to create installer
    if not create_installer():
        print("\n" + "=" * 50)
        print("INSTALLER CREATION FAILED")
        print("=" * 50)
        print("But don't worry! A portable package has been created.")
        print("\nTo create a proper installer:")
        print("1. Install Inno Setup from: https://jrsoftware.org/isdl.php")
        print("2. Run this script again")
        print("\nAlternatively, use the portable package for distribution.")

if __name__ == "__main__":
    main()
