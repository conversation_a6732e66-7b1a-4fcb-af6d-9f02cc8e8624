export interface User {
  id: string;
  email?: string;
  phone?: string;
  created_at: string;
}

export interface Booking {
  id: string;
  user_id: string;
  date: string;
  slot: string;
  name: string;
  phone: string;
  payment_status: 'pending' | 'completed' | 'failed';
  payment_id?: string;
  amount: number;
  created_at: string;
  additional_payment?: number;
}

export interface Slot {
  id: string;
  time: string;
  status: 'available' | 'booked' | 'reserved';
  booking_id?: string;
}

export interface DayBookings {
  date: string;
  slots: Slot[];
}

export interface PaymentInfo {
  amount: number;
  currency: string;
  receipt: string;
  payment_id: string;
}
