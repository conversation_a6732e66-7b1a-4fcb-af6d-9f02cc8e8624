import customtkinter as ctk
import serial
import serial.tools.list_ports
import threading
import time
from tkinter import messagebox
import queue
import sys
import os

def get_resource_path(relative_path):
    """Get absolute path to resource, works for dev and for PyInstaller"""
    try:
        # <PERSON>yInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")
    return os.path.join(base_path, relative_path)

class SerialMonitorGUI:
    def __init__(self):
        # Configure CustomTkinter
        ctk.set_appearance_mode("dark")

        # Load custom theme with proper resource path
        theme_path = get_resource_path("purple_theme.json")
        if os.path.exists(theme_path):
            ctk.set_default_color_theme(theme_path)
        else:
            # Fallback to built-in theme if custom theme not found
            ctk.set_default_color_theme("dark-blue")
        
        # Initialize main window
        self.root = ctk.CTk()
        self.root.title("Serial Monitor")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)

        # Set application icon
        try:
            icon_path = get_resource_path("serial_monitor_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except:
            pass  # If icon file not found, continue without icon
        
        # Serial connection variables
        self.serial_connection = None
        self.is_connected = False
        self.read_thread = None
        self.stop_thread = False
        
        # Queue for thread-safe GUI updates
        self.message_queue = queue.Queue()
        
        # Create GUI elements
        self.create_widgets()
        
        # Start message queue processor
        self.process_queue()
        
        # Populate COM ports on startup
        self.refresh_com_ports()
    
    def create_widgets(self):
        # Main container with padding
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Connection settings frame
        connection_frame = ctk.CTkFrame(main_frame)
        connection_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        # COM Port selection
        ctk.CTkLabel(connection_frame, text="COM Port:").grid(row=0, column=0, padx=10, pady=10, sticky="w")
        self.com_port_var = ctk.StringVar()
        self.com_port_dropdown = ctk.CTkComboBox(
            connection_frame, 
            variable=self.com_port_var,
            width=120,
            state="readonly"
        )
        self.com_port_dropdown.grid(row=0, column=1, padx=5, pady=10)
        
        # Refresh COM ports button
        self.refresh_btn = ctk.CTkButton(
            connection_frame,
            text="⟲",
            width=30,
            command=self.refresh_com_ports,
            font=ctk.CTkFont(size=16)
        )
        self.refresh_btn.grid(row=0, column=2, padx=5, pady=10)
        
        # Baud Rate selection
        ctk.CTkLabel(connection_frame, text="Baud Rate:").grid(row=0, column=3, padx=(20, 10), pady=10, sticky="w")
        self.baud_rate_var = ctk.StringVar(value="9600")
        self.baud_rate_dropdown = ctk.CTkComboBox(
            connection_frame,
            variable=self.baud_rate_var,
            values=["9600", "19200", "38400", "57600", "115200", "230400", "460800", "921600"],
            width=100,
            state="readonly"
        )
        self.baud_rate_dropdown.grid(row=0, column=4, padx=5, pady=10)
        
        # Connect/Disconnect button
        self.connect_btn = ctk.CTkButton(
            connection_frame,
            text="Connect",
            command=self.toggle_connection,
            width=100
        )
        self.connect_btn.grid(row=0, column=5, padx=(20, 10), pady=10)
        
        # Status label
        self.status_label = ctk.CTkLabel(
            connection_frame,
            text="Disconnected",
            text_color="red"
        )
        self.status_label.grid(row=0, column=6, padx=10, pady=10)
        
        # Command input frame
        command_frame = ctk.CTkFrame(main_frame)
        command_frame.pack(fill="x", padx=10, pady=5)
        
        # Command input
        ctk.CTkLabel(command_frame, text="Command:").pack(side="left", padx=10, pady=10)
        self.command_entry = ctk.CTkEntry(
            command_frame,
            placeholder_text="Enter command here..."
        )
        self.command_entry.pack(side="left", fill="x", expand=True, padx=5, pady=10)
        
        # Send button
        self.send_btn = ctk.CTkButton(
            command_frame,
            text="Send",
            command=self.send_command,
            width=80,
            state="disabled"
        )
        self.send_btn.pack(side="right", padx=10, pady=10)
        
        # Serial monitor frame
        monitor_frame = ctk.CTkFrame(main_frame)
        monitor_frame.pack(fill="both", expand=True, padx=10, pady=(5, 10))
        
        # Serial monitor label
        ctk.CTkLabel(monitor_frame, text="Serial Monitor", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(10, 5))
        
        # Serial monitor text area with scrollbar
        self.monitor_text = ctk.CTkTextbox(
            monitor_frame,
            wrap="word",
            font=ctk.CTkFont(family="Consolas", size=11)
        )
        self.monitor_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Clear monitor button
        clear_btn = ctk.CTkButton(
            monitor_frame,
            text="Clear Monitor",
            command=self.clear_monitor,
            width=100
        )
        clear_btn.pack(pady=(0, 10))
        
        # Bind Enter key to send command
        self.command_entry.bind("<Return>", lambda event: self.send_command())
    
    def refresh_com_ports(self):
        """Refresh the list of available COM ports"""
        ports = serial.tools.list_ports.comports()
        port_list = [port.device for port in ports]
        
        if port_list:
            self.com_port_dropdown.configure(values=port_list)
            if not self.com_port_var.get() or self.com_port_var.get() not in port_list:
                self.com_port_var.set(port_list[0])
        else:
            self.com_port_dropdown.configure(values=["No ports available"])
            self.com_port_var.set("No ports available")
    
    def toggle_connection(self):
        """Toggle serial connection"""
        if self.is_connected:
            self.disconnect_serial()
        else:
            self.connect_serial()
    
    def connect_serial(self):
        """Establish serial connection"""
        try:
            port = self.com_port_var.get()
            baud_rate = int(self.baud_rate_var.get())
            
            if port == "No ports available" or not port:
                messagebox.showerror("Error", "Please select a valid COM port")
                return
            
            self.serial_connection = serial.Serial(
                port=port,
                baudrate=baud_rate,
                timeout=1
            )
            
            self.is_connected = True
            self.connect_btn.configure(text="Disconnect", fg_color="#d63384", hover_color="#b02a5b")
            self.status_label.configure(text=f"Connected to {port}", text_color="white")
            self.send_btn.configure(state="normal")
            
            # Start reading thread
            self.stop_thread = False
            self.read_thread = threading.Thread(target=self.read_serial_data, daemon=True)
            self.read_thread.start()
            
            self.add_to_monitor(f"Connected to {port} at {baud_rate} baud\n", "green")
            
        except Exception as e:
            messagebox.showerror("Connection Error", f"Failed to connect: {str(e)}")
    
    def disconnect_serial(self):
        """Close serial connection"""
        try:
            self.is_connected = False
            self.stop_thread = True
            
            if self.serial_connection and self.serial_connection.is_open:
                self.serial_connection.close()
            
            self.connect_btn.configure(text="Connect", fg_color="#8b5fbf", hover_color="#7a4fb3")
            self.status_label.configure(text="Disconnected", text_color="white")
            self.send_btn.configure(state="disabled")
            
            self.add_to_monitor("Disconnected\n", "red")
            
        except Exception as e:
            messagebox.showerror("Disconnection Error", f"Failed to disconnect: {str(e)}")
    
    def send_command(self):
        """Send command through serial connection"""
        if not self.is_connected or not self.serial_connection:
            return

        command = self.command_entry.get().strip()
        if not command:
            return

        try:
            # Encode the command with newline
            command_bytes = (command + '\r\n').encode('utf-8')

            # Send the entire command at once
            self.serial_connection.write(command_bytes)

            # Force flush the output buffer to ensure immediate transmission
            self.serial_connection.flush()

            # Add to monitor and clear input
            self.add_to_monitor(f">> {command}\n", "blue")
            self.command_entry.delete(0, 'end')

        except Exception as e:
            self.add_to_monitor(f"Error sending command: {str(e)}\n", "red")
    
    def read_serial_data(self):
        """Read data from serial connection in separate thread"""
        while not self.stop_thread and self.is_connected:
            try:
                if self.serial_connection and self.serial_connection.in_waiting > 0:
                    data = self.serial_connection.readline().decode('utf-8', errors='ignore').strip()
                    if data:
                        self.message_queue.put(('data', data + '\n'))
                time.sleep(0.01)  # Small delay to prevent excessive CPU usage

            except Exception as e:
                if self.is_connected:  # Only show error if we're supposed to be connected
                    self.message_queue.put(('error', f"Read error: {str(e)}\n"))
                break
    
    def add_to_monitor(self, text, color="white"):
        """Add text to serial monitor with color"""
        self.monitor_text.insert("end", text)
        self.monitor_text.see("end")  # Auto-scroll to bottom
    
    def clear_monitor(self):
        """Clear the serial monitor"""
        self.monitor_text.delete("1.0", "end")
    
    def process_queue(self):
        """Process messages from the queue (for thread-safe GUI updates)"""
        try:
            while True:
                msg_type, message = self.message_queue.get_nowait()
                if msg_type == 'data':
                    self.add_to_monitor(message, "white")
                elif msg_type == 'error':
                    self.add_to_monitor(message, "red")
        except queue.Empty:
            pass

        # Schedule next queue check
        self.root.after(50, self.process_queue)
    
    def on_closing(self):
        """Handle application closing"""
        if self.is_connected:
            self.disconnect_serial()
        self.root.destroy()
    
    def run(self):
        """Start the GUI application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    app = SerialMonitorGUI()
    app.run()
