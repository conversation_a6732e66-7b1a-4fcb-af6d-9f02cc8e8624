"""
Basic Serial Instrument Control Example
Demonstrates how to use the serial instrument class
"""

import sys
from pathlib import Path

# Add src directory to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from instruments.serial_instrument import SerialInstrument
from utils.logger import setup_logger

def main():
    """Basic serial instrument example"""
    
    # Setup logging
    logger = setup_logger("SerialExample")
    
    # Create instrument instance
    # Replace 'COM3' with your actual COM port
    instrument = SerialInstrument(
        name="Test Instrument",
        port="COM3",
        baudrate=9600,
        timeout=2.0
    )
    
    try:
        # Connect to instrument
        logger.info("Connecting to instrument...")
        if instrument.connect():
            logger.info("Connected successfully!")
            
            # Get instrument ID
            try:
                instrument_id = instrument.get_id()
                logger.info(f"Instrument ID: {instrument_id}")
            except Exception as e:
                logger.warning(f"Could not get instrument ID: {e}")
            
            # Send some example commands
            commands = [
                "*IDN?",           # Standard identification query
                "*RST",            # Reset instrument
                "SYST:ERR?",       # Check for errors
            ]
            
            for cmd in commands:
                logger.info(f"Sending command: {cmd}")
                
                if cmd.endswith("?"):
                    # Query command - expect response
                    response = instrument.query(cmd)
                    logger.info(f"Response: {response}")
                else:
                    # Write command - no response expected
                    success = instrument.write(cmd)
                    logger.info(f"Command sent: {'Success' if success else 'Failed'}")
            
            # Get instrument status
            status = instrument.get_status()
            logger.info(f"Instrument status: {status}")
            
        else:
            logger.error("Failed to connect to instrument")
    
    except Exception as e:
        logger.error(f"Error during instrument communication: {e}")
    
    finally:
        # Always disconnect
        if instrument.connected:
            instrument.disconnect()
            logger.info("Disconnected from instrument")

if __name__ == "__main__":
    main()
