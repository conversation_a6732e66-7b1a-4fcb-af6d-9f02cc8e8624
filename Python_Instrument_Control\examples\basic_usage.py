"""
Basic Usage Example for Lab Instrument Control System

This example demonstrates how to use the instrument drivers programmatically
without the GUI interface.
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.visa_manager import VISAManager
from instruments.agilent_e3631a import AgilentE3631A
from instruments.agilent_34401a import Agilent34401A
from instruments.hp_6063b import HP6063B
import logging
import time

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_power_supply():
    """Example: Control Agilent E3631A Power Supply"""
    
    print("\n" + "="*50)
    print("Power Supply Control Example")
    print("="*50)
    
    # Initialize VISA manager
    visa_manager = VISAManager("../config/instruments.yaml")
    
    # Create power supply instance
    ps = AgilentE3631A(
        name="E3631A_Example",
        visa_address="GPIB0::5::INSTR",  # Update with your address
        visa_manager=visa_manager
    )
    
    try:
        # Connect to instrument
        if ps.connect():
            print(f"Connected to: {ps.get_id()}")
            
            # Initialize to safe state
            ps.initialize()
            
            # Configure Channel 1 (6V supply)
            print("\nConfiguring Channel 1...")
            ps.set_voltage(1, 3.3)  # Set 3.3V
            ps.set_current(1, 0.5)  # Set 500mA current limit
            ps.set_channel_enabled(1, True)  # Enable output
            
            # Wait and take measurements
            time.sleep(1)
            measurements = ps.get_all_measurements(1)
            print(f"CH1 Measurements: {measurements}")
            
            # Configure Channel 2 (+25V supply)
            print("\nConfiguring Channel 2...")
            ps.set_voltage(2, 12.0)  # Set 12V
            ps.set_current(2, 0.1)   # Set 100mA current limit
            ps.set_channel_enabled(2, True)  # Enable output
            
            # Wait and take measurements
            time.sleep(1)
            measurements = ps.get_all_measurements(2)
            print(f"CH2 Measurements: {measurements}")
            
            # Get comprehensive status
            status = ps.get_status()
            print(f"\nPower Supply Status:")
            for ch, ch_status in status.get('channels', {}).items():
                print(f"  Channel {ch}: {ch_status.get('enabled', False)}")
                if 'measurements' in ch_status:
                    meas = ch_status['measurements']
                    print(f"    V: {meas.get('voltage', 0):.3f}V, "
                          f"I: {meas.get('current', 0):.3f}A, "
                          f"P: {meas.get('power', 0):.3f}W")
            
            # Safety: Turn off all outputs
            print("\nTurning off all outputs...")
            ps.set_all_outputs_enabled(False)
            
        else:
            print("Failed to connect to power supply")
            
    except Exception as e:
        logger.error(f"Power supply example error: {e}")
        
    finally:
        ps.disconnect()


def example_multimeter():
    """Example: Control Agilent 34401A Multimeter"""
    
    print("\n" + "="*50)
    print("Multimeter Control Example")
    print("="*50)
    
    # Initialize VISA manager
    visa_manager = VISAManager("../config/instruments.yaml")
    
    # Create multimeter instance
    dmm = Agilent34401A(
        name="34401A_Example",
        visa_address="GPIB0::8::INSTR",  # Update with your address
        visa_manager=visa_manager
    )
    
    try:
        # Connect to instrument
        if dmm.connect():
            print(f"Connected to: {dmm.get_id()}")
            
            # Initialize
            dmm.initialize()
            
            # Measure DC voltage
            print("\nMeasuring DC Voltage...")
            voltage = dmm.measure_dc_voltage()
            print(f"DC Voltage: {voltage:.6f} V")
            
            # Measure resistance
            print("\nMeasuring 2-wire Resistance...")
            resistance = dmm.measure_resistance_2w()
            print(f"Resistance: {resistance:.3f} Ω")
            
            # Get measurement statistics
            print("\nTaking 10 readings for statistics...")
            stats = dmm.get_statistics(count=10)
            print(f"Statistics:")
            print(f"  Mean: {stats.get('mean', 0):.6f}")
            print(f"  Std Dev: {stats.get('std_dev', 0):.6f}")
            print(f"  Min: {stats.get('min', 0):.6f}")
            print(f"  Max: {stats.get('max', 0):.6f}")
            
        else:
            print("Failed to connect to multimeter")
            
    except Exception as e:
        logger.error(f"Multimeter example error: {e}")
        
    finally:
        dmm.disconnect()


def example_electronic_load():
    """Example: Control HP 6063B Electronic Load"""
    
    print("\n" + "="*50)
    print("Electronic Load Control Example")
    print("="*50)
    
    # Initialize VISA manager
    visa_manager = VISAManager("../config/instruments.yaml")
    
    # Create electronic load instance
    load = HP6063B(
        name="6063B_Example",
        visa_address="GPIB0::7::INSTR",  # Update with your address
        visa_manager=visa_manager
    )
    
    try:
        # Connect to instrument
        if load.connect():
            print(f"Connected to: {load.get_id()}")
            
            # Initialize to safe state
            load.initialize()
            
            # Set constant current mode
            print("\nSetting Constant Current Mode...")
            load.set_cc_mode(1.0)  # 1A constant current
            
            # Enable load (be careful - this will draw current!)
            print("WARNING: About to enable load - ensure power source is connected!")
            input("Press Enter to continue or Ctrl+C to abort...")
            
            load.set_load_enabled(True)
            
            # Take measurements
            time.sleep(2)
            measurements = load.get_all_measurements()
            print(f"Load Measurements: {measurements}")
            
            # Check operating mode
            mode = load.get_mode()
            print(f"Operating Mode: {mode}")
            
            # Disable load
            print("\nDisabling load...")
            load.set_load_enabled(False)
            
        else:
            print("Failed to connect to electronic load")
            
    except Exception as e:
        logger.error(f"Electronic load example error: {e}")
        
    finally:
        load.disconnect()


def scan_visa_resources():
    """Example: Scan for available VISA resources"""
    
    print("\n" + "="*50)
    print("VISA Resource Scan")
    print("="*50)
    
    visa_manager = VISAManager()
    
    try:
        resources = visa_manager.scan_resources()
        
        if resources:
            print(f"Found {len(resources)} VISA resources:")
            for i, resource in enumerate(resources, 1):
                print(f"  {i}. {resource}")
        else:
            print("No VISA resources found")
            
    except Exception as e:
        logger.error(f"VISA scan error: {e}")


def main():
    """Main example function"""
    
    print("Lab Instrument Control System - Basic Usage Examples")
    print("="*60)
    
    while True:
        print("\nSelect an example to run:")
        print("1. Scan VISA Resources")
        print("2. Power Supply Control (Agilent E3631A)")
        print("3. Multimeter Control (Agilent 34401A)")
        print("4. Electronic Load Control (HP 6063B)")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            scan_visa_resources()
        elif choice == '2':
            example_power_supply()
        elif choice == '3':
            example_multimeter()
        elif choice == '4':
            example_electronic_load()
        elif choice == '5':
            print("Goodbye!")
            break
        else:
            print("Invalid choice. Please try again.")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nExample interrupted by user.")
    except Exception as e:
        logger.error(f"Example error: {e}")
        print(f"Error: {e}")
