<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking History - Soapy Football</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #0284c7;
        }
        
        .btn-secondary {
            background-color: #10b981;
        }
        
        .btn-secondary:hover {
            background-color: #059669;
        }
        
        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }
        
        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        nav a:hover {
            color: #0ea5e9;
        }
        
        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }
        
        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }
        
        /* History section */
        .history-section {
            padding: 60px 0;
            flex-grow: 1;
        }
        
        .section-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2rem;
            color: #1e293b;
        }
        
        .history-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .history-card {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .booking-list {
            margin-top: 20px;
        }
        
        .booking-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .booking-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }
        
        .booking-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .booking-date {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1e293b;
        }
        
        .booking-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-completed {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .status-upcoming {
            background-color: #e0f2fe;
            color: #0369a1;
        }
        
        .status-cancelled {
            background-color: #fee2e2;
            color: #b91c1c;
        }
        
        .booking-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .booking-detail {
            margin-bottom: 10px;
        }
        
        .detail-label {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 5px;
        }
        
        .detail-value {
            font-weight: 500;
            color: #1e293b;
        }
        
        .booking-actions {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            padding: 8px 15px;
            font-size: 0.9rem;
        }
        
        .no-bookings {
            text-align: center;
            padding: 40px 0;
            color: #6b7280;
        }
        
        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
        }
        
        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }
        
        .footer-col ul {
            list-style: none;
        }
        
        .footer-col ul li {
            margin-bottom: 10px;
        }
        
        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-col a:hover {
            color: white;
        }
        
        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }
        
        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
            
            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            
            nav ul.active {
                display: flex;
            }
            
            .booking-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">Soapy Football</a>
            <button class="mobile-menu-btn">☰</button>
            <nav>
                <ul id="menu">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="booking.html" class="nav-btn">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="history-section">
        <div class="container">
            <h2 class="section-title">Your Booking History</h2>
            <div class="history-container">
                <div class="history-card">
                    <div class="booking-list" id="booking-list">
                        <!-- Bookings will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best turf booking platform for football enthusiasts.</p>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9876543210</li>
                        <li>Address: 123 Sports Complex, Mumbai</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const menu = document.getElementById('menu');
        
        mobileMenuBtn.addEventListener('click', () => {
            menu.classList.toggle('active');
        });
        
        // Sample booking data (in a real app, this would come from an API)
        const bookings = [
            {
                id: 'BK001',
                date: 'May 15, 2025',
                time: '7:00 - 8:00 AM',
                players: '6 players',
                amount: '₹200',
                status: 'upcoming',
                paymentId: 'PAY123456'
            },
            {
                id: 'BK002',
                date: 'May 10, 2025',
                time: '9:00 - 10:00 AM',
                players: '8 players',
                amount: '₹200',
                status: 'completed',
                paymentId: 'PAY123455'
            },
            {
                id: 'BK003',
                date: 'May 5, 2025',
                time: '6:00 - 7:00 AM',
                players: '10 players',
                amount: '₹200',
                status: 'cancelled',
                paymentId: 'PAY123454'
            }
        ];
        
        // Load bookings
        function loadBookings() {
            const bookingList = document.getElementById('booking-list');
            
            if (bookings.length === 0) {
                bookingList.innerHTML = '<div class="no-bookings">You have no bookings yet.</div>';
                return;
            }
            
            let html = '';
            
            bookings.forEach(booking => {
                let statusClass = '';
                let statusText = '';
                
                switch (booking.status) {
                    case 'upcoming':
                        statusClass = 'status-upcoming';
                        statusText = 'Upcoming';
                        break;
                    case 'completed':
                        statusClass = 'status-completed';
                        statusText = 'Completed';
                        break;
                    case 'cancelled':
                        statusClass = 'status-cancelled';
                        statusText = 'Cancelled';
                        break;
                }
                
                html += `
                    <div class="booking-item">
                        <div class="booking-header">
                            <div class="booking-date">Booking #${booking.id}</div>
                            <div class="booking-status ${statusClass}">${statusText}</div>
                        </div>
                        <div class="booking-details">
                            <div class="booking-detail">
                                <div class="detail-label">Date</div>
                                <div class="detail-value">${booking.date}</div>
                            </div>
                            <div class="booking-detail">
                                <div class="detail-label">Time</div>
                                <div class="detail-value">${booking.time}</div>
                            </div>
                            <div class="booking-detail">
                                <div class="detail-label">Players</div>
                                <div class="detail-value">${booking.players}</div>
                            </div>
                            <div class="booking-detail">
                                <div class="detail-label">Amount</div>
                                <div class="detail-value">${booking.amount}</div>
                            </div>
                        </div>
                        ${booking.status === 'upcoming' ? `
                            <div class="booking-actions">
                                <button class="btn btn-secondary action-btn" onclick="rescheduleBooking('${booking.id}')">Reschedule</button>
                                <button class="btn action-btn" onclick="cancelBooking('${booking.id}')">Cancel</button>
                            </div>
                        ` : ''}
                    </div>
                `;
            });
            
            bookingList.innerHTML = html;
        }
        
        // Reschedule booking
        function rescheduleBooking(bookingId) {
            alert(`Reschedule booking ${bookingId}`);
            // In a real app, this would open a modal or redirect to a reschedule page
        }
        
        // Cancel booking
        function cancelBooking(bookingId) {
            if (confirm(`Are you sure you want to cancel booking ${bookingId}?`)) {
                alert(`Booking ${bookingId} cancelled`);
                // In a real app, this would make an API call to cancel the booking
                // and then reload the bookings
                
                // For demo purposes, update the booking status
                const booking = bookings.find(b => b.id === bookingId);
                if (booking) {
                    booking.status = 'cancelled';
                    loadBookings();
                }
            }
        }
        
        // Load bookings when the page loads
        window.addEventListener('load', loadBookings);
    </script>
</body>
</html>
