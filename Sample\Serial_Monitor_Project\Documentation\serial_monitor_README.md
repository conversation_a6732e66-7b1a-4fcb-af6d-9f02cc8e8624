# Serial Monitor GUI - Arduino IDE Style

A modern Python GUI application for serial communication that mimics the Arduino IDE serial monitor interface.

## Features

- **COM Port Selection**: Automatically detects and lists all available COM ports
- **Baud Rate Selection**: Common baud rates from 9600 to 921600
- **Real-time Connection Status**: Visual indicators for connection state
- **Command Interface**: Send commands with Enter key or Send button
- **Serial Monitor**: Large output area with auto-scroll functionality
- **Modern UI**: Built with CustomTkinter for a sleek, dark-themed interface
- **Thread-safe**: Separate thread for reading serial data to prevent GUI freezing

## Installation

1. Install the required dependencies:
```bash
pip install -r serial_monitor_requirements.txt
```

2. Run the application:
```bash
python serial_monitor_gui.py
```

## Usage

### Connection Setup
1. **Select COM Port**: Choose from the dropdown or click refresh (🔄) to update the list
2. **Select Baud Rate**: Choose the appropriate baud rate for your device
3. **Connect**: Click the "Connect" button to establish connection

### Sending Commands
1. Type your command in the command input field
2. Press Enter or click "Send" to transmit the command
3. Commands are displayed in blue in the monitor

### Serial Monitor
- Received data appears in white text
- Error messages appear in red text
- Use "Clear Monitor" to clear the output
- The monitor auto-scrolls to show the latest data

### Disconnection
- Click "Disconnect" to close the serial connection
- The application will automatically disconnect when closed

## Interface Layout

```
┌─────────────────────────────────────────────────────────────┐
│ [COM Port ▼] [🔄] [Baud Rate ▼] [Connect] [Status]         │
├─────────────────────────────────────────────────────────────┤
│ Command: [___________________________] [Send]              │
├─────────────────────────────────────────────────────────────┤
│                Serial Monitor                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │                                                         │ │
│ │  >> command                                             │ │
│ │  response from device                                   │ │
│ │  >> another command                                     │ │
│ │  device response...                                     │ │
│ │                                                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                        [Clear Monitor]                     │
└─────────────────────────────────────────────────────────────┘
```

## Keyboard Shortcuts

- **Enter**: Send command (when command field is focused)
- **Ctrl+C**: Copy selected text from monitor
- **Ctrl+A**: Select all text in monitor

## Error Handling

- Invalid COM port selection shows error dialog
- Connection failures display detailed error messages
- Read errors are shown in the monitor in red text
- Automatic cleanup on application close

## Technical Details

- Built with CustomTkinter for modern UI
- Uses pyserial for serial communication
- Thread-safe design with queue-based message passing
- Automatic COM port detection and refresh
- Configurable baud rates and connection parameters
