<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="600px" viewBox="0 0 800 600" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <title>Soapy Football Hero</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="fieldGradient">
            <stop stop-color="#10b981" offset="0%"></stop>
            <stop stop-color="#059669" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="70%" id="bubbleGradient">
            <stop stop-color="#ffffff" stop-opacity="0.9" offset="0%"></stop>
            <stop stop-color="#ffffff" stop-opacity="0.5" offset="70%"></stop>
            <stop stop-color="#ffffff" stop-opacity="0.2" offset="100%"></stop>
        </radialGradient>
    </defs>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <!-- Football field -->
        <rect fill="url(#fieldGradient)" x="100" y="150" width="600" height="400" rx="20"></rect>
        
        <!-- Field lines -->
        <rect stroke="#ffffff" stroke-width="4" x="102" y="152" width="596" height="396" rx="18"></rect>
        <line x1="400" y1="150" x2="400" y2="550" stroke="#ffffff" stroke-width="4"></line>
        <circle cx="400" cy="350" r="80" stroke="#ffffff" stroke-width="4"></circle>
        
        <!-- Goal posts -->
        <rect fill="#ffffff" x="100" y="300" width="20" height="100"></rect>
        <rect fill="#ffffff" x="680" y="300" width="20" height="100"></rect>
        
        <!-- Football -->
        <circle cx="320" cy="380" r="30" fill="#ffffff" stroke="#000000" stroke-width="1"></circle>
        <path d="M320,350 L320,410 M290,380 L350,380" stroke="#000000" stroke-width="1"></path>
        <path d="M300,360 L340,400 M300,400 L340,360" stroke="#000000" stroke-width="1"></path>
        
        <!-- Soap bubbles -->
        <circle cx="250" cy="200" r="15" fill="url(#bubbleGradient)" opacity="0.8"></circle>
        <circle cx="280" cy="220" r="10" fill="url(#bubbleGradient)" opacity="0.7"></circle>
        <circle cx="230" cy="230" r="12" fill="url(#bubbleGradient)" opacity="0.6"></circle>
        <circle cx="500" cy="180" r="18" fill="url(#bubbleGradient)" opacity="0.8"></circle>
        <circle cx="530" cy="210" r="14" fill="url(#bubbleGradient)" opacity="0.7"></circle>
        <circle cx="480" cy="220" r="10" fill="url(#bubbleGradient)" opacity="0.6"></circle>
        <circle cx="600" cy="300" r="20" fill="url(#bubbleGradient)" opacity="0.8"></circle>
        <circle cx="580" cy="330" r="15" fill="url(#bubbleGradient)" opacity="0.7"></circle>
        <circle cx="620" cy="320" r="12" fill="url(#bubbleGradient)" opacity="0.6"></circle>
        <circle cx="200" cy="400" r="22" fill="url(#bubbleGradient)" opacity="0.8"></circle>
        <circle cx="180" cy="430" r="16" fill="url(#bubbleGradient)" opacity="0.7"></circle>
        <circle cx="220" cy="420" r="13" fill="url(#bubbleGradient)" opacity="0.6"></circle>
        
        <!-- Players (simplified) -->
        <circle cx="200" cy="350" r="20" fill="#0ea5e9"></circle>
        <circle cx="600" cy="350" r="20" fill="#f59e0b"></circle>
        <circle cx="300" cy="250" r="20" fill="#0ea5e9"></circle>
        <circle cx="500" cy="250" r="20" fill="#f59e0b"></circle>
        <circle cx="300" cy="450" r="20" fill="#0ea5e9"></circle>
        <circle cx="500" cy="450" r="20" fill="#f59e0b"></circle>
    </g>
</svg>
