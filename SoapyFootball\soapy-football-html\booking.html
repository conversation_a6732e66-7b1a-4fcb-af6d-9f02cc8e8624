<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book a Slot - Soapy Football</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            color: #333;
            line-height: 1.6;
            background-color: #f9fafb;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Utility classes */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .btn {
            display: inline-block;
            background-color: #0ea5e9;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            border: none;
            transition: background-color 0.3s;
        }

        .btn:hover {
            background-color: #0284c7;
        }

        .btn-secondary {
            background-color: #10b981;
        }

        .btn-secondary:hover {
            background-color: #059669;
        }

        /* Header styles */
        header {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #0ea5e9;
            text-decoration: none;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 20px;
        }

        nav a {
            text-decoration: none;
            color: #4b5563;
            font-weight: 500;
            transition: color 0.3s;
        }

        nav a:hover {
            color: #0ea5e9;
        }

        .nav-btn {
            background-color: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .nav-btn:hover {
            background-color: #0284c7;
            color: white;
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Booking section */
        .booking-section {
            padding: 60px 0;
        }

        .section-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 2rem;
            color: #1e293b;
        }

        .booking-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .booking-form {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #1e293b;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-family: 'Inter', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #0ea5e9;
        }

        .time-slots {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .time-slot {
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .time-slot:hover {
            border-color: #0ea5e9;
        }

        .time-slot.selected {
            background-color: #0ea5e9;
            color: white;
            border-color: #0ea5e9;
        }

        .time-slot.booked {
            background-color: #f3f4f6;
            color: #9ca3af;
            cursor: not-allowed;
            border-color: #e5e7eb;
        }

        .booking-summary {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .summary-title {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #1e293b;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .summary-item:last-child {
            border-bottom: none;
        }

        .summary-label {
            font-weight: 500;
            color: #4b5563;
        }

        .summary-value {
            font-weight: 600;
            color: #1e293b;
        }

        .total-amount {
            font-size: 1.2rem;
            font-weight: 700;
            color: #0ea5e9;
        }

        .payment-btn {
            width: 100%;
            padding: 12px;
            margin-top: 20px;
            font-size: 1.1rem;
        }

        /* Footer */
        footer {
            background-color: #1e293b;
            color: white;
            padding: 50px 0 20px;
            margin-top: auto;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-col h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .footer-col ul {
            list-style: none;
        }

        .footer-col ul li {
            margin-bottom: 10px;
        }

        .footer-col a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s;
        }

        .footer-col a:hover {
            color: white;
        }

        .footer-bottom {
            text-align: center;
            padding-top: 20px;
            border-top: 1px solid #374151;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }

            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                padding: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }

            nav ul.active {
                display: flex;
            }

            .booking-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container header-container">
            <a href="index.html" class="logo">Soapy Football</a>
            <button class="mobile-menu-btn">☰</button>
            <nav>
                <ul id="menu">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#">About</a></li>
                    <li><a href="#">Pricing</a></li>
                    <li><a href="#">Contact</a></li>
                    <li><a href="history.html">My Bookings</a></li>
                    <li><a href="login.html">Login</a></li>
                    <li><a href="booking.html" class="nav-btn">Book Now</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <section class="booking-section">
        <div class="container">
            <h2 class="section-title">Book Your Slot</h2>
            <div class="booking-container">
                <div class="booking-form">
                    <div class="form-group">
                        <label for="date">Select Date</label>
                        <input type="date" id="date" min="" required>
                    </div>

                    <div class="form-group">
                        <label>Select Time Slot</label>
                        <div class="time-slots">
                            <div class="time-slot" data-time="06:00 - 07:00">6:00 - 7:00 AM</div>
                            <div class="time-slot" data-time="07:00 - 08:00">7:00 - 8:00 AM</div>
                            <div class="time-slot booked" data-time="08:00 - 09:00">8:00 - 9:00 AM</div>
                            <div class="time-slot" data-time="09:00 - 10:00">9:00 - 10:00 AM</div>
                            <div class="time-slot" data-time="10:00 - 11:00">10:00 - 11:00 AM</div>
                            <div class="time-slot" data-time="11:00 - 12:00">11:00 - 12:00 PM</div>
                            <div class="time-slot booked" data-time="12:00 - 13:00">12:00 - 1:00 PM</div>
                            <div class="time-slot" data-time="13:00 - 14:00">1:00 - 2:00 PM</div>
                            <div class="time-slot" data-time="14:00 - 15:00">2:00 - 3:00 PM</div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="name">Full Name</label>
                        <input type="text" id="name" placeholder="Enter your full name" required>
                    </div>

                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" placeholder="Enter your email" required>
                    </div>

                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" placeholder="Enter your phone number" required>
                    </div>

                    <div class="form-group">
                        <label for="players">Number of Players</label>
                        <select id="players" required>
                            <option value="">Select number of players</option>
                            <option value="5">5 players</option>
                            <option value="6">6 players</option>
                            <option value="7">7 players</option>
                            <option value="8">8 players</option>
                            <option value="9">9 players</option>
                            <option value="10">10 players</option>
                        </select>
                    </div>
                </div>

                <div class="booking-summary">
                    <h3 class="summary-title">Booking Summary</h3>
                    <div class="summary-item">
                        <span class="summary-label">Date</span>
                        <span class="summary-value" id="summary-date">Select a date</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Time Slot</span>
                        <span class="summary-value" id="summary-time">Select a time slot</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Number of Players</span>
                        <span class="summary-value" id="summary-players">-</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Turf Booking Fee</span>
                        <span class="summary-value">₹200</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Amount</span>
                        <span class="summary-value total-amount">₹200</span>
                    </div>

                    <button class="btn payment-btn" id="pay-btn" disabled>Proceed to Payment</button>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3>Soapy Football</h3>
                    <p>The best turf booking platform for football enthusiasts.</p>
                </div>
                <div class="footer-col">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="#">About Us</a></li>
                        <li><a href="#">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-col">
                    <h3>Contact Us</h3>
                    <ul>
                        <li>Email: <EMAIL></li>
                        <li>Phone: +91 9876543210</li>
                        <li>Address: 123 Sports Complex, Mumbai</li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Soapy Football. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
        const menu = document.getElementById('menu');

        mobileMenuBtn.addEventListener('click', () => {
            menu.classList.toggle('active');
        });

        // Set min date to today
        const dateInput = document.getElementById('date');
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0];
        dateInput.min = formattedDate;

        // Time slot selection
        const timeSlots = document.querySelectorAll('.time-slot:not(.booked)');
        const summaryDate = document.getElementById('summary-date');
        const summaryTime = document.getElementById('summary-time');
        const summaryPlayers = document.getElementById('summary-players');
        const payBtn = document.getElementById('pay-btn');
        const playersSelect = document.getElementById('players');

        timeSlots.forEach(slot => {
            slot.addEventListener('click', () => {
                // Remove selected class from all slots
                timeSlots.forEach(s => s.classList.remove('selected'));

                // Add selected class to clicked slot
                slot.classList.add('selected');

                // Update summary
                summaryTime.textContent = slot.dataset.time;

                // Check if all required fields are filled
                checkFormCompletion();
            });
        });

        // Date selection
        dateInput.addEventListener('change', () => {
            const selectedDate = new Date(dateInput.value);
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            summaryDate.textContent = selectedDate.toLocaleDateString('en-US', options);

            // Check if all required fields are filled
            checkFormCompletion();
        });

        // Players selection
        playersSelect.addEventListener('change', () => {
            summaryPlayers.textContent = playersSelect.value + ' players';

            // Check if all required fields are filled
            checkFormCompletion();
        });

        // Check if all required fields are filled
        function checkFormCompletion() {
            const nameInput = document.getElementById('name');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');

            if (
                dateInput.value &&
                document.querySelector('.time-slot.selected') &&
                nameInput.value &&
                emailInput.value &&
                phoneInput.value &&
                playersSelect.value
            ) {
                payBtn.disabled = false;
            } else {
                payBtn.disabled = true;
            }
        }

        // Form input event listeners
        document.getElementById('name').addEventListener('input', checkFormCompletion);
        document.getElementById('email').addEventListener('input', checkFormCompletion);
        document.getElementById('phone').addEventListener('input', checkFormCompletion);

        // Payment button click
        payBtn.addEventListener('click', () => {
            // Get form values
            const date = document.getElementById('date').value;
            const selectedDate = new Date(date);
            const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
            const formattedDate = selectedDate.toLocaleDateString('en-US', options);

            const selectedTimeSlot = document.querySelector('.time-slot.selected');
            const timeSlot = selectedTimeSlot ? selectedTimeSlot.dataset.time : '';

            const players = document.getElementById('players').value;

            // Redirect to payment page with booking details
            window.location.href = `payment.html?date=${encodeURIComponent(formattedDate)}&time=${encodeURIComponent(timeSlot)}&players=${encodeURIComponent(players + ' players')}`;
        });
    </script>
</body>
</html>
