import customtkinter as ctk
import serial
import serial.tools.list_ports
import threading
import time
import os
import tkinter as tk
from tkinter import messagebox
from PIL import Image, ImageTk
import yaml

# Import custom widgets
from animated_widgets import AnimatedButton

# Set appearance mode and default color theme
ctk.set_appearance_mode("System")  # Modes: "System" (standard), "Dark", "Light"
ctk.set_default_color_theme("green")  # Themes: "blue" (standard), "green", "dark-blue"

# Classic Theme (Green) - Original color scheme
CLASSIC_MAIN_LIGHT = "#2ecc71"  # Green
CLASSIC_MAIN_DARK = "#27ae60"   # Darker Green
CLASSIC_ACCENT_LIGHT = "#1abc9c"  # Teal
CLASSIC_ACCENT_DARK = "#16a085"   # Darker Teal
CLASSIC_BACKGROUND_LIGHT = "#f5f5f5"  # Light Gray
CLASSIC_BACKGROUND_DARK = "#2c3e50"   # Dark Blue-Gray
CLASSIC_TEXT_LIGHT = "#2c3e50"  # Dark Blue-Gray
CLASSIC_TEXT_DARK = "#ecf0f1"   # Light Gray

# Modern Theme (Purple) - Based on the provided image
MODERN_MAIN_LIGHT = "#8B5CF6"     # Purple
MODERN_MAIN_DARK = "#7C3AED"      # Darker Purple
MODERN_ACCENT_LIGHT = "#A855F7"   # Light Purple
MODERN_ACCENT_DARK = "#9333EA"    # Vibrant Purple
MODERN_BACKGROUND_LIGHT = "#F8FAFC"  # Very Light Gray
MODERN_BACKGROUND_DARK = "#1E1B2E"   # Dark Purple-Gray
MODERN_TEXT_LIGHT = "#1F2937"     # Dark Gray
MODERN_TEXT_DARK = "#F1F5F9"      # Light Gray

# Current theme colors (will be set based on selected theme)
MAIN_COLOR_LIGHT = CLASSIC_MAIN_LIGHT
MAIN_COLOR_DARK = CLASSIC_MAIN_DARK
ACCENT_COLOR_LIGHT = CLASSIC_ACCENT_LIGHT
ACCENT_COLOR_DARK = CLASSIC_ACCENT_DARK
BACKGROUND_COLOR_LIGHT = CLASSIC_BACKGROUND_LIGHT
BACKGROUND_COLOR_DARK = CLASSIC_BACKGROUND_DARK
TEXT_COLOR_LIGHT = CLASSIC_TEXT_LIGHT
TEXT_COLOR_DARK = CLASSIC_TEXT_DARK

# Define animation constants
ANIMATION_DURATION = 150  # milliseconds
ANIMATION_STEPS = 10

# Get the directory of the current script for loading assets
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# Settings file path
SETTINGS_FILE = os.path.join(SCRIPT_DIR, "switcher_settings.yaml")

def apply_theme(theme_name):
    """Apply a color theme to the application"""
    global MAIN_COLOR_LIGHT, MAIN_COLOR_DARK, ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK
    global BACKGROUND_COLOR_LIGHT, BACKGROUND_COLOR_DARK, TEXT_COLOR_LIGHT, TEXT_COLOR_DARK

    if theme_name.lower() == "modern":
        MAIN_COLOR_LIGHT = MODERN_MAIN_LIGHT
        MAIN_COLOR_DARK = MODERN_MAIN_DARK
        ACCENT_COLOR_LIGHT = MODERN_ACCENT_LIGHT
        ACCENT_COLOR_DARK = MODERN_ACCENT_DARK
        BACKGROUND_COLOR_LIGHT = MODERN_BACKGROUND_LIGHT
        BACKGROUND_COLOR_DARK = MODERN_BACKGROUND_DARK
        TEXT_COLOR_LIGHT = MODERN_TEXT_LIGHT
        TEXT_COLOR_DARK = MODERN_TEXT_DARK
    else:  # Default to Classic theme
        MAIN_COLOR_LIGHT = CLASSIC_MAIN_LIGHT
        MAIN_COLOR_DARK = CLASSIC_MAIN_DARK
        ACCENT_COLOR_LIGHT = CLASSIC_ACCENT_LIGHT
        ACCENT_COLOR_DARK = CLASSIC_ACCENT_DARK
        BACKGROUND_COLOR_LIGHT = CLASSIC_BACKGROUND_LIGHT
        BACKGROUND_COLOR_DARK = CLASSIC_BACKGROUND_DARK
        TEXT_COLOR_LIGHT = CLASSIC_TEXT_LIGHT
        TEXT_COLOR_DARK = CLASSIC_TEXT_DARK

class SettingsPopup:
    """In-app settings popup for configuring switchers"""
    def __init__(self, parent):
        self.parent = parent

        # Create overlay frame that covers the entire app
        self.overlay = ctk.CTkFrame(
            parent,
            fg_color=("#333333", "#111111"),
            corner_radius=0
        )
        self.overlay.place(relx=0, rely=0, relwidth=1, relheight=1)

        # Create popup frame
        self.popup_frame = ctk.CTkFrame(
            self.overlay,
            fg_color=(BACKGROUND_COLOR_LIGHT, BACKGROUND_COLOR_DARK),
            corner_radius=15,
            border_width=1,
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )
        self.popup_frame.place(relx=0.5, rely=0.5, relwidth=0.8, relheight=0.8, anchor="center")

        # Configure popup frame
        self.popup_frame.grid_columnconfigure(0, weight=1)
        self.popup_frame.grid_rowconfigure(0, weight=0)  # Title
        self.popup_frame.grid_rowconfigure(1, weight=1)  # Tabs
        self.popup_frame.grid_rowconfigure(2, weight=0)  # Buttons

        # Create title
        self.title_frame = ctk.CTkFrame(
            self.popup_frame,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            corner_radius=10,
            height=50
        )
        self.title_frame.grid(row=0, column=0, padx=20, pady=(20, 10), sticky="ew")
        self.title_frame.grid_columnconfigure(0, weight=1)
        self.title_frame.grid_columnconfigure(1, weight=0)

        self.title_label = ctk.CTkLabel(
            self.title_frame,
            text="USB Switcher Settings",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("white", "white")
        )
        self.title_label.grid(row=0, column=0, padx=20, pady=10, sticky="w")

        # Close button
        self.close_button = ctk.CTkButton(
            self.title_frame,
            text="✕",
            command=self.close,
            width=30,
            height=30,
            corner_radius=15,
            fg_color="transparent",
            hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            text_color=("white", "white"),
            font=ctk.CTkFont(size=16)
        )
        self.close_button.grid(row=0, column=1, padx=10, pady=10)

        # Create tabs
        self.tabview = ctk.CTkTabview(
            self.popup_frame,
            fg_color=(BACKGROUND_COLOR_LIGHT, BACKGROUND_COLOR_DARK),
            segmented_button_fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            segmented_button_selected_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            segmented_button_unselected_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            segmented_button_selected_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            segmented_button_unselected_hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        self.tabview.grid(row=1, column=0, padx=20, pady=10, sticky="nsew")

        # Add tabs
        self.tabview.add("General")
        self.tabview.add("Switchers")
        self.tabview.add("COM Ports")

        # Configure tab grids
        self.tabview.tab("General").grid_columnconfigure(0, weight=1)
        self.tabview.tab("Switchers").grid_columnconfigure(0, weight=1)
        self.tabview.tab("COM Ports").grid_columnconfigure(0, weight=1)

        # Create General tab content
        self.create_general_tab()

        # Create Switchers tab content
        self.create_switchers_tab()

        # Create COM Ports tab content
        self.create_com_ports_tab()

        # Create buttons frame
        self.buttons_frame = ctk.CTkFrame(
            self.popup_frame,
            corner_radius=0,
            fg_color="transparent"
        )
        self.buttons_frame.grid(row=2, column=0, padx=20, pady=20, sticky="ew")
        self.buttons_frame.grid_columnconfigure(0, weight=1)
        self.buttons_frame.grid_columnconfigure(1, weight=0)
        self.buttons_frame.grid_columnconfigure(2, weight=0)

        # Create Save button
        self.save_button = ctk.CTkButton(
            self.buttons_frame,
            text="Save",
            command=self.save_settings,
            fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            hover_color=(ACCENT_COLOR_DARK, ACCENT_COLOR_LIGHT),
            text_color=("white", "white"),
            width=100,
            height=35
        )
        self.save_button.grid(row=0, column=2, padx=(10, 0), pady=10)

        # Create Cancel button
        self.cancel_button = ctk.CTkButton(
            self.buttons_frame,
            text="Cancel",
            command=self.close,
            fg_color="transparent",
            hover_color=(BACKGROUND_COLOR_DARK, BACKGROUND_COLOR_LIGHT),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
            border_width=1,
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            width=100,
            height=35
        )
        self.cancel_button.grid(row=0, column=1, padx=10, pady=10)

    def close(self):
        """Close the popup"""
        self.overlay.destroy()

    def save_settings(self):
        """Save the settings and update the main application"""
        # Update number of switchers
        try:
            new_num_switchers = int(self.num_switchers_var.get())
            if new_num_switchers != self.parent.num_switchers:
                self.parent.num_switchers = new_num_switchers
                self.parent.num_switchers_var.set(str(new_num_switchers))

                # Store COM port assignments in the dictionary
                for i, com_var in enumerate(self.com_vars):
                    self.parent.switcher_com_ports[i] = com_var.get()
                    if i < len(self.parent.switchers):
                        self.parent.switchers[i]["com_var"].set(com_var.get())

                # Store switcher names in the dictionary
                for i, name_var in enumerate(self.name_vars):
                    self.parent.switcher_names[i] = name_var.get()
                    if i < len(self.parent.switchers):
                        self.parent.switchers[i]["name_var"].set(name_var.get())

                # Now update the panels with the new settings
                self.parent.update_switcher_panels()
            else:
                # If number of switchers didn't change, still update names and COM ports
                # Store and update switcher names
                for i, name_var in enumerate(self.name_vars):
                    self.parent.switcher_names[i] = name_var.get()
                    if i < len(self.parent.switchers):
                        self.parent.switchers[i]["name_var"].set(name_var.get())

                # Store and update COM ports
                for i, com_var in enumerate(self.com_vars):
                    self.parent.switcher_com_ports[i] = com_var.get()
                    if i < len(self.parent.switchers):
                        self.parent.switchers[i]["com_var"].set(com_var.get())

                # Update the UI to reflect changes
                self.parent.update_switcher_names()
        except ValueError:
            pass

        # Save settings to YAML file
        self.parent.save_settings()

        # Close the popup
        self.close()

    def create_general_tab(self):
        """Create content for the General tab"""
        tab = self.tabview.tab("General")

        # Appearance mode frame
        appearance_frame = ctk.CTkFrame(tab, corner_radius=10)
        appearance_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        # Appearance mode label
        appearance_label = ctk.CTkLabel(
            appearance_frame,
            text="Appearance Mode:",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        appearance_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")

        # Appearance mode options
        self.appearance_var = ctk.StringVar(value=ctk.get_appearance_mode())
        appearance_options = ctk.CTkSegmentedButton(
            appearance_frame,
            values=["Light", "Dark", "System"],
            variable=self.appearance_var,
            command=self.change_appearance_mode,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            selected_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            unselected_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            selected_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            unselected_hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            text_color=("white", "white")
        )
        appearance_options.grid(row=0, column=1, padx=20, pady=20)

        # Color theme frame
        theme_frame = ctk.CTkFrame(tab, corner_radius=10)
        theme_frame.grid(row=1, column=0, padx=10, pady=10, sticky="ew")

        # Color theme label
        theme_label = ctk.CTkLabel(
            theme_frame,
            text="Color Theme:",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        theme_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")

        # Color theme options
        self.theme_var = ctk.StringVar(value=getattr(self.parent, 'current_theme', 'Classic'))
        theme_options = ctk.CTkSegmentedButton(
            theme_frame,
            values=["Classic", "Modern"],
            variable=self.theme_var,
            command=self.change_color_theme,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            selected_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            unselected_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            selected_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            unselected_hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            text_color=("white", "white")
        )
        theme_options.grid(row=0, column=1, padx=20, pady=20)

    def create_switchers_tab(self):
        """Create content for the Switchers tab"""
        tab = self.tabview.tab("Switchers")

        # Configure tab for scrolling
        tab.grid_rowconfigure(0, weight=0)  # Number of switchers frame
        tab.grid_rowconfigure(1, weight=1)  # Switcher names frame

        # Number of switchers frame
        num_switchers_frame = ctk.CTkFrame(tab, corner_radius=10)
        num_switchers_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        # Number of switchers label
        num_switchers_label = ctk.CTkLabel(
            num_switchers_frame,
            text="Number of Switchers:",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        num_switchers_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")

        # Number of switchers dropdown
        self.num_switchers_var = ctk.StringVar(value=str(self.parent.num_switchers))
        num_switchers_dropdown = ctk.CTkComboBox(
            num_switchers_frame,
            values=["1", "2", "3", "4"],
            variable=self.num_switchers_var,
            width=100,
            height=32,
            corner_radius=8,
            dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            text_color=("white", "white")
        )
        num_switchers_dropdown.grid(row=0, column=1, padx=20, pady=20)

        # Create scrollable frame for switcher names
        switcher_names_scroll = ctk.CTkScrollableFrame(
            tab,
            corner_radius=10,
            label_text="Switcher Names",
            label_font=ctk.CTkFont(size=14, weight="bold"),
            label_fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            label_text_color=("white", "white")
        )
        switcher_names_scroll.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        switcher_names_scroll.grid_columnconfigure(0, weight=1)
        switcher_names_scroll.grid_columnconfigure(1, weight=2)

        # Switcher name entries
        self.name_vars = []
        for i in range(4):  # Max 4 switchers
            name_label = ctk.CTkLabel(
                switcher_names_scroll,
                text=f"Switcher {i+1}:",
                text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
            )
            name_label.grid(row=i, column=0, padx=20, pady=10, sticky="w")

            # Use stored name if available, otherwise use default
            default_name = f"Switcher {i+1}"

            # First check if there's a stored name in the dictionary
            if i in self.parent.switcher_names:
                name_var = ctk.StringVar(value=self.parent.switcher_names[i])
            # Then check if there's a name in the current switchers list
            elif i < len(self.parent.switchers):
                name_var = ctk.StringVar(value=self.parent.switchers[i]["name_var"].get())
            # Otherwise use the default name
            else:
                name_var = ctk.StringVar(value=default_name)

            name_entry = ctk.CTkEntry(
                switcher_names_scroll,
                textvariable=name_var,
                width=200,
                height=32,
                corner_radius=8,
                border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                fg_color=("white", BACKGROUND_COLOR_DARK),
                text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
            )
            name_entry.grid(row=i, column=1, padx=20, pady=10, sticky="ew")
            self.name_vars.append(name_var)

    def create_com_ports_tab(self):
        """Create content for the COM Ports tab"""
        tab = self.tabview.tab("COM Ports")

        # Configure tab for scrolling
        tab.grid_rowconfigure(0, weight=0)  # Refresh frame
        tab.grid_rowconfigure(1, weight=1)  # COM assignments frame

        # Refresh button frame
        refresh_frame = ctk.CTkFrame(tab, corner_radius=10)
        refresh_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")

        # Available COM ports label
        com_ports_label = ctk.CTkLabel(
            refresh_frame,
            text="Available COM Ports:",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        com_ports_label.grid(row=0, column=0, padx=20, pady=20, sticky="w")

        # Refresh button
        refresh_button = AnimatedButton(
            refresh_frame,
            text="Refresh",
            command=self.refresh_com_ports,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            text_color=("white", "white"),
            width=100,
            height=32
        )
        refresh_button.grid(row=0, column=1, padx=20, pady=20)

        # Create scrollable frame for COM port assignments
        com_assignments_scroll = ctk.CTkScrollableFrame(
            tab,
            corner_radius=10,
            label_text="COM Port Assignments",
            label_font=ctk.CTkFont(size=14, weight="bold"),
            label_fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            label_text_color=("white", "white")
        )
        com_assignments_scroll.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        com_assignments_scroll.grid_columnconfigure(0, weight=1)
        com_assignments_scroll.grid_columnconfigure(1, weight=2)

        # Get available COM ports
        self.available_com_ports = [port.device for port in serial.tools.list_ports.comports()]

        # COM port dropdowns
        self.com_vars = []
        for i in range(4):  # Max 4 switchers
            com_label = ctk.CTkLabel(
                com_assignments_scroll,
                text=f"Switcher {i+1}:",
                text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
            )
            com_label.grid(row=i, column=0, padx=20, pady=10, sticky="w")

            com_var = ctk.StringVar()

            # First check if there's a stored COM port in the dictionary
            if i in self.parent.switcher_com_ports and self.parent.switcher_com_ports[i]:
                com_var.set(self.parent.switcher_com_ports[i])
            # Then check if there's a COM port in the current switchers list
            elif i < len(self.parent.switchers) and self.parent.switchers[i]["com_var"].get():
                com_var.set(self.parent.switchers[i]["com_var"].get())
            # Otherwise use the first available COM port
            elif self.available_com_ports and i < len(self.available_com_ports):
                com_var.set(self.available_com_ports[i])

            com_dropdown = ctk.CTkComboBox(
                com_assignments_scroll,
                values=self.available_com_ports,
                variable=com_var,
                width=200,
                height=32,
                corner_radius=8,
                dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
                fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
                button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
                text_color=("white", "white")
            )
            com_dropdown.grid(row=i, column=1, padx=20, pady=10, sticky="ew")
            self.com_vars.append(com_var)

    def refresh_com_ports(self):
        """Refresh the list of available COM ports"""
        self.available_com_ports = [port.device for port in serial.tools.list_ports.comports()]

        # Update COM port dropdowns in the COM Ports tab
        com_assignments_scroll = self.tabview.tab("COM Ports").winfo_children()[1]

        for i in range(4):
            # Find the dropdown in the scrollable frame
            for child in com_assignments_scroll.winfo_children():
                if isinstance(child, ctk.CTkComboBox) and child.grid_info()["row"] == i:
                    child.configure(values=self.available_com_ports)
                    break

    def change_appearance_mode(self, new_mode):
        """Change the appearance mode"""
        ctk.set_appearance_mode(new_mode)

    def change_color_theme(self, new_theme):
        """Change the color theme"""
        apply_theme(new_theme)
        self.parent.current_theme = new_theme
        # Update header and status bar colors
        self.parent.update_header_colors()
        self.parent.update_status_bar_colors()
        # Trigger a UI refresh by updating switcher panels
        self.parent.update_switcher_panels()



# AnimatedButton is now imported from animated_widgets.py

class USBSwitcherApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        # Configure window
        self.title("EPR Switcher Control")
        self.geometry("1100x700")
        self.minsize(900, 600)

        # Set background color
        self.configure(fg_color=(BACKGROUND_COLOR_LIGHT, BACKGROUND_COLOR_DARK))

        # Serial connections
        self.serial_connections = {}  # Dictionary to store serial connections for each switcher

        # Switcher data
        self.num_switchers = 1
        self.num_switchers_var = ctk.StringVar(value="1")  # For settings dialog
        self.switchers = []  # Will hold switcher frames
        self.active_channels = {}  # Will track active channels across switchers
        self.available_com_ports = []  # List of available COM ports
        self.switcher_names = {}  # Dictionary to store custom names for switchers
        self.switcher_com_ports = {}  # Dictionary to store COM port selections
        self.current_theme = "Classic"  # Current color theme

        # Configure grid layout
        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(0, weight=0)  # Header
        self.grid_rowconfigure(1, weight=1)  # Switcher panels
        self.grid_rowconfigure(2, weight=0)  # Status bar

        # Create header with title and settings button
        self.create_header()

        # Create switcher panels container
        self.switcher_container = ctk.CTkFrame(
            self,
            fg_color="transparent"
        )
        self.switcher_container.grid(row=1, column=0, padx=20, pady=20, sticky="nsew")

        # Create status bar
        self.create_status_bar()

        # Load settings from YAML file
        self.load_settings()

        # Update COM ports list
        self.update_com_ports()

        # Initialize switcher panels with loaded settings
        self.update_switcher_panels()

        # Auto-connect to switchers with saved COM ports
        self.auto_connect_switchers()

    def load_settings(self):
        """Load settings from YAML file"""
        try:
            if os.path.exists(SETTINGS_FILE):
                with open(SETTINGS_FILE, 'r') as file:
                    settings = yaml.safe_load(file) or {}

                # Load number of switchers
                self.num_switchers = settings.get('num_switchers', 1)
                self.num_switchers_var.set(str(self.num_switchers))

                # Load switcher names
                self.switcher_names = settings.get('switcher_names', {})

                # Load COM port assignments
                self.switcher_com_ports = settings.get('switcher_com_ports', {})

                # Load and apply appearance mode
                appearance_mode = settings.get('appearance_mode', 'System')
                ctk.set_appearance_mode(appearance_mode)

                # Load and apply color theme
                self.current_theme = settings.get('color_theme', 'Classic')
                apply_theme(self.current_theme)

                # Update header and status bar colors after they're created
                self.after(100, lambda: self.update_header_colors())
                self.after(100, lambda: self.update_status_bar_colors())

                # Update appearance menu to reflect loaded theme (after status bar is created)
                self.after(100, lambda: self.appearance_menu.set(appearance_mode))
                self.after(100, lambda: self.theme_menu.set(self.current_theme))

                self.status_label.configure(text="Settings loaded successfully")
            else:
                # Create default settings file
                self.create_default_settings()
                self.status_label.configure(text="Created default settings file")

        except Exception as e:
            messagebox.showerror("Settings Error", f"Failed to load settings: {str(e)}")
            self.create_default_settings()

    def create_default_settings(self):
        """Create default settings file"""
        default_settings = {
            'num_switchers': 1,
            'switcher_names': {},
            'switcher_com_ports': {},
            'appearance_mode': 'System',
            'color_theme': 'Classic'
        }
        self.save_settings_to_file(default_settings)

    def save_settings(self):
        """Save current settings to YAML file"""
        settings = {
            'num_switchers': self.num_switchers,
            'switcher_names': self.switcher_names,
            'switcher_com_ports': self.switcher_com_ports,
            'appearance_mode': ctk.get_appearance_mode(),
            'color_theme': self.current_theme
        }
        self.save_settings_to_file(settings)

    def save_settings_to_file(self, settings):
        """Save settings dictionary to YAML file"""
        try:
            with open(SETTINGS_FILE, 'w') as file:
                yaml.dump(settings, file, default_flow_style=False, indent=2)
        except Exception as e:
            messagebox.showerror("Settings Error", f"Failed to save settings: {str(e)}")

    def auto_connect_switchers(self):
        """Automatically connect switchers with saved COM ports"""
        for i, switcher in enumerate(self.switchers):
            com_port = switcher["com_var"].get()
            if com_port and com_port.strip():  # Only if COM port is specified and not empty
                # Check if COM port is available
                available_ports = [port.device for port in serial.tools.list_ports.comports()]
                if com_port in available_ports:
                    # Attempt to connect
                    try:
                        self.connect_to_switcher(i)
                    except Exception as e:
                        messagebox.showwarning(
                            "Auto-Connect Failed",
                            f"Failed to auto-connect {switcher['name_var'].get()} to {com_port}: {str(e)}"
                        )
                else:
                    # COM port not available - show warning
                    messagebox.showwarning(
                        "COM Port Unavailable",
                        f"COM port {com_port} for {switcher['name_var'].get()} is not available"
                    )

    def create_header(self):
        """Create the header with title and settings button"""
        self.header_frame = ctk.CTkFrame(
            self,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            corner_radius=0,
            height=60
        )
        self.header_frame.grid(row=0, column=0, sticky="ew")
        self.header_frame.grid_columnconfigure(0, weight=1)
        self.header_frame.grid_columnconfigure(1, weight=0)

        # App title
        self.title_label = ctk.CTkLabel(
            self.header_frame,
            text="EPR Switcher Control",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("white", "white")
        )
        self.title_label.grid(row=0, column=0, padx=20, pady=10, sticky="w")

        # Settings button
        try:
            # Try to load settings icon
            settings_icon_path = os.path.join(SCRIPT_DIR, "settings_icon.png")
            if os.path.exists(settings_icon_path):
                settings_icon = ctk.CTkImage(
                    light_image=Image.open(settings_icon_path),
                    dark_image=Image.open(settings_icon_path),
                    size=(24, 24)
                )
                self.settings_button = ctk.CTkButton(
                    self.header_frame,
                    text="",
                    image=settings_icon,
                    command=self.open_settings,
                    width=40,
                    height=40,
                    corner_radius=10,
                    fg_color="transparent",
                    hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                    text_color=("white", "white")
                )
            else:
                # Fallback to text button if icon not found
                self.settings_button = ctk.CTkButton(
                    self.header_frame,
                    text="⚙",
                    command=self.open_settings,
                    width=40,
                    height=40,
                    corner_radius=10,
                    fg_color="transparent",
                    hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                    text_color=("white", "white"),
                    font=ctk.CTkFont(size=20)
                )
        except:
            # Fallback to text button if loading icon fails
            self.settings_button = ctk.CTkButton(
                self.header_frame,
                text="⚙",
                command=self.open_settings,
                width=40,
                height=40,
                corner_radius=10,
                fg_color="transparent",
                hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                text_color=("white", "white"),
                font=ctk.CTkFont(size=20)
            )

        self.settings_button.grid(row=0, column=1, padx=20, pady=10, sticky="e")

    def update_header_colors(self):
        """Update header colors to match current theme"""
        self.header_frame.configure(fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK))

        # Update settings button hover color
        self.settings_button.configure(
            hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )

    def open_settings(self):
        """Open the settings dialog"""
        SettingsPopup(self)



    def create_status_bar(self):
        """Create the status bar"""
        self.status_frame = ctk.CTkFrame(
            self,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            height=40,
            corner_radius=0
        )
        self.status_frame.grid(row=2, column=0, sticky="ew")
        self.status_frame.grid_columnconfigure(0, weight=1)
        self.status_frame.grid_columnconfigure(1, weight=0)
        self.status_frame.grid_columnconfigure(2, weight=0)
        self.status_frame.grid_columnconfigure(3, weight=0)
        self.status_frame.grid_columnconfigure(4, weight=0)

        # Status label
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Ready",
            font=ctk.CTkFont(size=14),
            text_color=("white", "white")
        )
        self.status_label.grid(row=0, column=0, padx=20, pady=10, sticky="w")

        # Add color theme selector
        theme_label = ctk.CTkLabel(
            self.status_frame,
            text="Theme:",
            font=ctk.CTkFont(size=12),
            text_color=("white", "white")
        )
        theme_label.grid(row=0, column=1, padx=(0, 5), pady=10, sticky="e")

        self.theme_menu = ctk.CTkOptionMenu(
            self.status_frame,
            values=["Classic", "Modern"],
            command=self.change_color_theme,
            width=100,
            height=25,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            text_color=("white", "white")
        )
        self.theme_menu.grid(row=0, column=2, padx=10, pady=10, sticky="e")
        self.theme_menu.set(self.current_theme)

        # Add appearance mode selector
        appearance_label = ctk.CTkLabel(
            self.status_frame,
            text="Mode:",
            font=ctk.CTkFont(size=12),
            text_color=("white", "white")
        )
        appearance_label.grid(row=0, column=3, padx=(10, 5), pady=10, sticky="e")

        self.appearance_menu = ctk.CTkOptionMenu(
            self.status_frame,
            values=["Light", "Dark", "System"],
            command=self.change_appearance_mode,
            width=100,
            height=25,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            text_color=("white", "white")
        )
        self.appearance_menu.grid(row=0, column=4, padx=10, pady=10, sticky="e")
        self.appearance_menu.set(ctk.get_appearance_mode())

    def update_status_bar_colors(self):
        """Update status bar colors to match current theme"""
        self.status_frame.configure(fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK))

        # Update theme menu colors
        self.theme_menu.configure(
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT)
        )

        # Update appearance menu colors
        self.appearance_menu.configure(
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            button_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            button_hover_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            dropdown_hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT)
        )

    def create_switcher_panel(self, index):
        """Create a panel for a single switcher"""
        # Determine if we're in compact mode (3 or 4 switchers use same compact sizing)
        compact_mode = self.num_switchers >= 3

        # Create frame for this switcher
        switcher_frame = ctk.CTkFrame(
            self.switcher_container,
            fg_color=("white", BACKGROUND_COLOR_DARK),
            corner_radius=15,
            border_width=1,
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )

        # Configure grid for the switcher frame
        switcher_frame.grid_columnconfigure(0, weight=1)
        switcher_frame.grid_rowconfigure(0, weight=0)  # Title row
        switcher_frame.grid_rowconfigure(1, weight=1)  # Channels row
        switcher_frame.grid_rowconfigure(2, weight=0)  # Power button row

        # Switcher title frame
        title_frame = ctk.CTkFrame(
            switcher_frame,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            corner_radius=10,
            height=30 if compact_mode else 40
        )
        title_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        title_frame.grid_columnconfigure(0, weight=1)

        # Switcher title (using the name)
        # Use stored name if available, otherwise use default
        default_name = f"Switcher {index+1}"
        stored_name = self.switcher_names.get(index, default_name)
        name_var = ctk.StringVar(value=stored_name)
        title_label = ctk.CTkLabel(
            title_frame,
            textvariable=name_var,
            font=ctk.CTkFont(size=14 if compact_mode else 16, weight="bold"),
            text_color=("white", "white")
        )
        title_label.grid(row=0, column=0, padx=10, pady=2 if compact_mode else 5)

        # Create channel buttons frame
        channels_frame = ctk.CTkFrame(
            switcher_frame,
            fg_color="transparent"
        )
        channels_frame.grid(row=1, column=0, padx=10 if compact_mode else 20, pady=5 if compact_mode else 10, sticky="nsew")

        # Configure grid for channels
        for i in range(3):
            channels_frame.grid_columnconfigure(i, weight=1)
        channels_frame.grid_rowconfigure(0, weight=1)
        channels_frame.grid_rowconfigure(1, weight=1)

        # Create channel buttons with toggle switches (6 buttons in 2 rows of 3)
        channel_buttons = []
        channel_toggles = []
        for i in range(6):
            row = i // 3
            col = i % 3

            # Create a frame to hold both button and toggle
            channel_frame = ctk.CTkFrame(
                channels_frame,
                fg_color="transparent"
            )
            channel_frame.grid(row=row, column=col, padx=5 if compact_mode else 10, pady=5 if compact_mode else 10, sticky="nsew")
            channel_frame.grid_rowconfigure(0, weight=1)  # Button row
            channel_frame.grid_rowconfigure(1, weight=0)  # Toggle row
            channel_frame.grid_columnconfigure(0, weight=1)

            # Create the main channel button
            button = AnimatedButton(
                channel_frame,
                text=f"CH {i+1}",
                command=lambda idx=i, s_idx=index: self.toggle_channel(s_idx, idx),
                fg_color=("white", BACKGROUND_COLOR_DARK),
                hover_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
                border_width=2,
                border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                corner_radius=10 if compact_mode else 15,
                font=ctk.CTkFont(size=10 if compact_mode else 14, weight="bold"),
                height=28 if compact_mode else 40,
                animate_on_hover=True
            )
            button.grid(row=0, column=0, sticky="nsew", padx=2, pady=(2, 1))

            # Create toggle switch for flip mode with dynamic text
            toggle_switch = ctk.CTkSwitch(
                channel_frame,
                text="Normal",  # Initial text
                command=lambda idx=i, s_idx=index: self.toggle_flip_mode(s_idx, idx),
                width=50 if compact_mode else 60,
                height=16 if compact_mode else 20,
                switch_width=20 if compact_mode else 24,
                switch_height=10 if compact_mode else 12,
                font=ctk.CTkFont(size=8 if compact_mode else 10),
                text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
                fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                progress_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
            )
            toggle_switch.grid(row=1, column=0, sticky="ew", padx=2, pady=(1, 2))

            channel_buttons.append(button)
            channel_toggles.append(toggle_switch)

        # Bottom frame for controls
        controls_frame = ctk.CTkFrame(
            switcher_frame,
            fg_color="transparent"
        )
        controls_frame.grid(row=2, column=0, padx=5 if compact_mode else 10, pady=5 if compact_mode else 10, sticky="ew")
        controls_frame.grid_columnconfigure(0, weight=1)  # Left column for status elements
        controls_frame.grid_columnconfigure(1, weight=0)  # Right column for connect/power buttons
        controls_frame.grid_rowconfigure(0, weight=0)     # Row for status frame
        controls_frame.grid_rowconfigure(1, weight=0)     # Row for status button

        # Status frame (shows COM port and ID)
        status_frame = ctk.CTkFrame(
            controls_frame,
            fg_color=("white", BACKGROUND_COLOR_DARK),
            corner_radius=8 if compact_mode else 10,
            border_width=1,
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )
        status_frame.grid(row=0, column=0, padx=5 if compact_mode else 10, pady=5 if compact_mode else 10, sticky="ew")

        # COM port and ID display
        # Use stored COM port if available
        com_var = ctk.StringVar()
        if index in self.switcher_com_ports and self.switcher_com_ports[index]:
            com_var.set(self.switcher_com_ports[index])

        id_value = ctk.CTkLabel(
            status_frame,
            text="Not Connected",
            font=ctk.CTkFont(size=10 if compact_mode else 12),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        id_value.pack(pady=3 if compact_mode else 5)

        # Status button (separate button under ID box, left-aligned)
        status_button = AnimatedButton(
            controls_frame,
            text="Status",
            command=lambda s_idx=index: self.get_switcher_status(s_idx),
            width=60 if compact_mode else 80,
            height=20 if compact_mode else 25,
            corner_radius=4 if compact_mode else 6,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            text_color=("white", "white"),
            font=ctk.CTkFont(size=9 if compact_mode else 11),
            animate_on_hover=True
        )
        status_button.grid(row=1, column=0, padx=5 if compact_mode else 10, pady=(2, 5) if compact_mode else (3, 10), sticky="w")

        # Connect button with enhanced animations
        connect_button = AnimatedButton(
            controls_frame,
            text="Connect",
            command=lambda s_idx=index: self.connect_to_switcher(s_idx),
            width=80 if compact_mode else 100,
            height=25 if compact_mode else 32,
            corner_radius=6 if compact_mode else 8,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT),
            text_color=("white", "white"),
            animate_on_hover=True
        )
        connect_button.grid(row=0, column=1, padx=5 if compact_mode else 10, pady=5 if compact_mode else 10, sticky="e")

        # Power button with enhanced animations
        power_button = AnimatedButton(
            controls_frame,
            text="Power OFF",
            command=lambda s_idx=index: self.toggle_power(s_idx),
            width=80 if compact_mode else 100,
            height=25 if compact_mode else 32,
            corner_radius=6 if compact_mode else 8,
            fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            hover_color=(ACCENT_COLOR_DARK, ACCENT_COLOR_LIGHT),
            text_color=("white", "white"),
            animate_on_hover=True
        )
        power_button.grid(row=1, column=1, padx=5 if compact_mode else 10, pady=5 if compact_mode else 10, sticky="e")

        # Store switcher data
        switcher_data = {
            "frame": switcher_frame,
            "com_var": com_var,
            "id_value": id_value,
            "name_var": name_var,
            "channel_buttons": channel_buttons,
            "channel_toggles": channel_toggles,  # Toggle switches for flip mode
            "status_button": status_button,  # Status query button
            "connect_button": connect_button,
            "power_button": power_button,
            "power_state": True,  # True = ON, False = OFF
            "active_channel": None,  # Currently active channel (0-5) or None
            "connected": False,  # Connection status
            "serial_connection": None,  # Serial connection object
            "flip_modes": [False] * 6  # Flip mode state for each channel (0-5)
        }

        # Initialize toggle text for all channels
        for i in range(6):
            if switcher_data["flip_modes"][i]:
                switcher_data["channel_toggles"][i].configure(text="Flip")
            else:
                switcher_data["channel_toggles"][i].configure(text="Normal")

        return switcher_data

    def update_switcher_panels(self):
        """Update the switcher panels based on the selected number of switchers"""
        # Clear existing switcher frames
        for switcher in self.switchers:
            # Close any open serial connections
            if switcher["serial_connection"]:
                try:
                    switcher["serial_connection"].close()
                except:
                    pass
            switcher["frame"].grid_forget()

        # Clear any existing placeholder frames
        for widget in self.switcher_container.winfo_children():
            if isinstance(widget, ctk.CTkFrame) and widget.cget("fg_color") == "transparent":
                widget.destroy()

        # Reset switchers list
        self.switchers = []
        self.active_channels = {}

        # Get the number of switchers
        try:
            self.num_switchers = int(self.num_switchers_var.get())
        except ValueError:
            self.num_switchers = 1

        # Determine if we're in compact mode (only 4 switchers use compact layout)
        # 3 switchers should use the same sizing as 4 switchers to maintain consistency
        compact_mode = self.num_switchers >= 3
        padding = 8 if compact_mode else 15

        # Configure grid for the switcher container based on number of switchers
        if self.num_switchers == 1:
            # Single switcher - center it
            self.switcher_container.grid_columnconfigure(0, weight=1)
            self.switcher_container.grid_columnconfigure(1, weight=0)
            self.switcher_container.grid_rowconfigure(0, weight=1)
            self.switcher_container.grid_rowconfigure(1, weight=0)

            switcher_data = self.create_switcher_panel(0)
            switcher_data["frame"].grid(row=0, column=0, padx=padding, pady=padding, sticky="nsew")
            self.switchers.append(switcher_data)

        elif self.num_switchers == 2:
            # Two switchers - single row, both fill width equally
            self.switcher_container.grid_columnconfigure((0, 1), weight=1)
            self.switcher_container.grid_rowconfigure(0, weight=1)
            self.switcher_container.grid_rowconfigure(1, weight=0)

            for i in range(2):
                switcher_data = self.create_switcher_panel(i)
                switcher_data["frame"].grid(row=0, column=i, padx=padding, pady=padding, sticky="nsew")
                self.switchers.append(switcher_data)

        elif self.num_switchers == 3:
            # Three switchers - use 2x2 grid like 4 switchers but hide the 4th position
            self.switcher_container.grid_columnconfigure((0, 1), weight=1)
            self.switcher_container.grid_rowconfigure((0, 1), weight=1)

            # Create 3 switchers in 2x2 grid (positions 0, 1, 2 - leaving position 3 empty)
            for i in range(3):
                switcher_data = self.create_switcher_panel(i)
                row = i // 2
                col = i % 2
                switcher_data["frame"].grid(row=row, column=col, padx=padding, pady=padding, sticky="nsew")
                self.switchers.append(switcher_data)

            # Create invisible placeholder frame for 4th position to maintain grid structure
            placeholder_frame = ctk.CTkFrame(
                self.switcher_container,
                fg_color="transparent",
                corner_radius=0
            )
            placeholder_frame.grid(row=1, column=1, padx=padding, pady=padding, sticky="nsew")

        else:  # 4 switchers
            # Four switchers - 2x2 grid, fully filling GUI width
            self.switcher_container.grid_columnconfigure((0, 1), weight=1)
            self.switcher_container.grid_rowconfigure((0, 1), weight=1)

            # Create 2x2 grid
            for i in range(4):
                switcher_data = self.create_switcher_panel(i)
                row = i // 2
                col = i % 2
                switcher_data["frame"].grid(row=row, column=col, padx=padding, pady=padding, sticky="nsew")
                self.switchers.append(switcher_data)

        # Update COM port dropdowns
        self.update_com_ports()

    def update_com_ports(self):
        """Update the list of available COM ports"""
        ports = [port.device for port in serial.tools.list_ports.comports()]
        self.available_com_ports = ports

        # Set default COM port for switchers if not already set
        for switcher in self.switchers:
            if ports and not switcher["com_var"].get():
                switcher["com_var"].set(ports[0])

        if ports:
            self.status_label.configure(text=f"Found {len(ports)} COM ports")
        else:
            self.status_label.configure(text="No COM ports found")

    def connect_to_switcher(self, switcher_idx):
        """Connect to the selected COM port for a specific switcher"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        if switcher["connected"]:
            # Disconnect
            if switcher["serial_connection"]:
                try:
                    switcher["serial_connection"].close()
                except:
                    pass
                switcher["serial_connection"] = None

            switcher["connected"] = False
            switcher["connect_button"].configure(text="Connect")
            switcher["id_value"].configure(text="Not Connected")
            self.status_label.configure(text=f"Disconnected {switcher['name_var'].get()}")
            return

        # Get selected COM port
        com_port = switcher["com_var"].get()
        if not com_port:
            messagebox.showerror("Error", "Please select a COM port")
            return

        # Check if this COM port is already in use by another switcher
        for idx, other_switcher in enumerate(self.switchers):
            if idx != switcher_idx and other_switcher["connected"] and other_switcher["com_var"].get() == com_port:
                messagebox.showerror(
                    "COM Port Conflict",
                    f"COM port {com_port} is already in use by {other_switcher['name_var'].get()}"
                )
                return

        try:
            # Connect to the COM port
            serial_conn = serial.Serial(com_port, 9600, timeout=1)

            # Send command to get switcher ID
            serial_conn.write(b"uuid\r\n")
            time.sleep(0.5)  # Give the device time to respond

            # Read response (in a real device, this would return the actual ID)
            # For simulation, we'll generate a fake ID
            try:
                response = serial_conn.readline().decode('utf-8').strip()
            except:
                response = ""

            if not response:
                # Simulate a response for testing
                response = f"SW{switcher_idx+1}-{com_port.replace('COM', '')}"

            # Update UI
            switcher["id_value"].configure(text=response)
            switcher["connected"] = True
            switcher["connect_button"].configure(text="Disconnect")
            switcher["serial_connection"] = serial_conn
            self.status_label.configure(text=f"Connected to {switcher['name_var'].get()} on {com_port}")

            # Start a thread to monitor the connection
            threading.Thread(
                target=self.monitor_connection,
                args=(switcher_idx,),
                daemon=True
            ).start()

        except Exception as e:
            messagebox.showerror("Connection Error", str(e))
            if switcher["serial_connection"]:
                try:
                    switcher["serial_connection"].close()
                except:
                    pass
                switcher["serial_connection"] = None

    def monitor_connection(self, switcher_idx):
        """Monitor the serial connection in a separate thread"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        while switcher["connected"] and switcher["serial_connection"]:
            try:
                # Check if there's any data available
                if switcher["serial_connection"].in_waiting:
                    data = switcher["serial_connection"].readline().decode('utf-8').strip()
                    if data:
                        # Process any incoming data
                        print(f"Received from {switcher['name_var'].get()}: {data}")

                time.sleep(0.1)
            except:
                # Connection lost
                switcher["connected"] = False
                if switcher["serial_connection"]:
                    try:
                        switcher["serial_connection"].close()
                    except:
                        pass
                switcher["serial_connection"] = None

                # Update UI from the main thread
                self.after(0, lambda s_idx=switcher_idx: self.update_connection_status_disconnected(s_idx))
                break

    def update_connection_status_disconnected(self, switcher_idx):
        """Update UI when connection is lost"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]
        switcher["connect_button"].configure(text="Connect")
        switcher["id_value"].configure(text="Not Connected")
        self.status_label.configure(text=f"Connection lost for {switcher['name_var'].get()}")

    def update_switcher_names(self):
        """Update the switcher names in the UI without recreating the panels"""
        # The name_var is already updated in each switcher, so the UI should reflect the changes automatically
        # This method is here in case we need to do additional UI updates in the future
        self.status_label.configure(text="Switcher settings updated")

    def on_num_switchers_change(self, _):
        """Handle change in the number of switchers"""
        self.update_switcher_panels()

    def toggle_channel(self, switcher_idx, channel_idx):
        """Toggle a channel on a switcher"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        # Check if the switcher is connected
        if not switcher["connected"]:
            messagebox.showwarning("Not Connected", f"Please connect {switcher['name_var'].get()} first")
            return

        # Check if the switcher is powered on
        if not switcher["power_state"]:
            messagebox.showwarning("Switcher Off", f"Please turn on {switcher['name_var'].get()} first")
            return

        # Check if this channel is already active on another switcher
        for idx, other_switcher in enumerate(self.switchers):
            if idx != switcher_idx and other_switcher["active_channel"] == channel_idx and other_switcher["power_state"]:
                messagebox.showwarning(
                    "Channel Conflict",
                    f"Channel {channel_idx+1} is already active on {other_switcher['name_var'].get()}"
                )
                return

        # NEW BEHAVIOR: Check if this channel is already active on this switcher
        if switcher["active_channel"] == channel_idx:
            # User clicked the same active channel - turn it off
            self.deactivate_channel(switcher_idx, channel_idx)
            return

        # NEW BEHAVIOR: Check if another channel is active on this switcher
        if switcher["active_channel"] is not None:
            # Another channel is active - show warning and prevent switching
            active_channel_num = switcher["active_channel"] + 1
            clicked_channel_num = channel_idx + 1
            messagebox.showwarning(
                "Channel Active",
                f"Channel {active_channel_num} is currently active on {switcher['name_var'].get()}.\n"
                f"Please turn off Channel {active_channel_num} first before switching to Channel {clicked_channel_num}."
            )
            return

        # No channel is currently active - activate the new channel
        switcher["active_channel"] = channel_idx

        # Animate the button
        self.animate_channel_activation(switcher_idx, channel_idx)

        # Send command to the switcher
        self.send_channel_command(switcher_idx, channel_idx)

        # Update status
        flip_status = " (Flip Mode)" if switcher["flip_modes"][channel_idx] else ""
        self.status_label.configure(
            text=f"Activated Channel {channel_idx+1} on {switcher['name_var'].get()}{flip_status}"
        )

    def toggle_flip_mode(self, switcher_idx, channel_idx):
        """Toggle flip mode for a specific channel with safety checks"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]
        toggle_switch = switcher["channel_toggles"][channel_idx]

        # Get the new toggle state
        new_flip_state = toggle_switch.get()

        # Check if this channel is currently active
        is_channel_active = (switcher["active_channel"] == channel_idx)

        if is_channel_active:
            # Channel is active - ask for confirmation
            channel_name = f"Channel {channel_idx+1}"
            switcher_name = switcher['name_var'].get()
            mode_change = "Flip Mode" if new_flip_state else "Normal Mode"

            result = messagebox.askyesno(
                "Confirm Mode Change",
                f"Are you sure you want to change {channel_name} on {switcher_name} to {mode_change}?\n\n"
                f"This will temporarily disconnect and reconnect the channel to prevent sparking.",
                icon="question"
            )

            if not result:
                # User cancelled - revert the toggle switch
                toggle_switch.set(not new_flip_state)
                self.status_label.configure(text="Mode change cancelled")
                return

            # User confirmed - perform disconnect/reconnect sequence
            self.perform_safe_mode_change(switcher_idx, channel_idx, new_flip_state)
        else:
            # Channel is not active - safe to change mode directly
            switcher["flip_modes"][channel_idx] = new_flip_state
            self.update_toggle_text(switcher_idx, channel_idx)

            # Update status
            mode_name = "Flip Mode" if new_flip_state else "Normal Mode"
            self.status_label.configure(
                text=f"Set Channel {channel_idx+1} to {mode_name} on {switcher['name_var'].get()}"
            )

    def perform_safe_mode_change(self, switcher_idx, channel_idx, new_flip_state):
        """Safely change mode for active channel with disconnect/reconnect"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        # Step 1: Disconnect the channel (without confirmation since user already confirmed)
        self.status_label.configure(text="Disconnecting channel for safe mode change...")

        # Reset button appearance to inactive
        switcher["channel_buttons"][channel_idx].configure(
            fg_color=("white", BACKGROUND_COLOR_DARK),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )

        # Send disconnect command
        self.send_channel_off_command(switcher_idx, channel_idx)

        # Step 2: Update flip mode state
        switcher["flip_modes"][channel_idx] = new_flip_state
        self.update_toggle_text(switcher_idx, channel_idx)

        # Step 3: Reconnect with new mode
        self.status_label.configure(text="Reconnecting channel with new mode...")

        # Reactivate the channel with new flip mode
        switcher["active_channel"] = channel_idx

        # Update button appearance to active
        switcher["channel_buttons"][channel_idx].configure(
            fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            text_color=("white", "white"),
            border_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
        )

        # Send new command with updated flip mode
        self.send_channel_command(switcher_idx, channel_idx)

        # Final status update
        mode_name = "Flip Mode" if new_flip_state else "Normal Mode"
        self.status_label.configure(
            text=f"Channel {channel_idx+1} safely switched to {mode_name} on {switcher['name_var'].get()}"
        )

    def update_toggle_text(self, switcher_idx, channel_idx):
        """Update toggle switch text based on current state"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]
        toggle_switch = switcher["channel_toggles"][channel_idx]
        flip_state = switcher["flip_modes"][channel_idx]

        # Update text based on flip state
        if flip_state:
            toggle_switch.configure(text="Flip")
        else:
            toggle_switch.configure(text="Normal")

    def deactivate_channel(self, switcher_idx, channel_idx):
        """Deactivate a specific channel on a switcher with confirmation"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        # Show confirmation dialog to prevent accidental turn-off
        channel_name = f"Channel {channel_idx+1}"
        switcher_name = switcher['name_var'].get()

        result = messagebox.askyesno(
            "Confirm Channel Deactivation",
            f"Are you sure you want to turn off {channel_name} on {switcher_name}?\n\n"
            f"This will disconnect the active channel.",
            icon="question"
        )

        # If user cancels, don't deactivate
        if not result:
            self.status_label.configure(text="Channel deactivation cancelled")
            return

        # User confirmed - proceed with deactivation
        # Reset the button appearance to inactive state
        switcher["channel_buttons"][channel_idx].configure(
            fg_color=("white", BACKGROUND_COLOR_DARK),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
            border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
        )

        # Clear the active channel
        switcher["active_channel"] = None

        # Send command to turn off the channel
        self.send_channel_off_command(switcher_idx, channel_idx)

        # Update status
        self.status_label.configure(
            text=f"Deactivated Channel {channel_idx+1} on {switcher['name_var'].get()}"
        )

    def animate_channel_activation(self, switcher_idx, channel_idx):
        """Animate the channel button activation"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]
        button = switcher["channel_buttons"][channel_idx]

        # Final state
        button.configure(
            fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
            text_color=("white", "white"),
            border_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
        )

    def toggle_power(self, switcher_idx):
        """Toggle power for a switcher"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        # Check if the switcher is connected
        if not switcher["connected"]:
            messagebox.showwarning("Not Connected", f"Please connect {switcher['name_var'].get()} first")
            return

        # Toggle power state
        switcher["power_state"] = not switcher["power_state"]

        if switcher["power_state"]:
            # Power ON
            switcher["power_button"].configure(
                text="Power OFF",
                fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
                hover_color=(ACCENT_COLOR_DARK, ACCENT_COLOR_LIGHT)
            )

            # Restore active channel if there was one
            if switcher["active_channel"] is not None:
                channel_idx = switcher["active_channel"]
                switcher["channel_buttons"][channel_idx].configure(
                    fg_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK),
                    text_color=("white", "white"),
                    border_color=(ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
                )

            # Send power on command
            self.send_power_command(switcher_idx, True)

            self.status_label.configure(
                text=f"Powered ON {switcher['name_var'].get()}"
            )
        else:
            # Power OFF
            switcher["power_button"].configure(
                text="Power ON",
                fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
                hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT)
            )

            # Deactivate all channels
            if switcher["active_channel"] is not None:
                channel_idx = switcher["active_channel"]
                switcher["channel_buttons"][channel_idx].configure(
                    fg_color=("white", BACKGROUND_COLOR_DARK),
                    text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK),
                    border_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK)
                )

            # Send power off command
            self.send_power_command(switcher_idx, False)

            self.status_label.configure(
                text=f"Powered OFF {switcher['name_var'].get()}"
            )

    def send_channel_command(self, switcher_idx, channel_idx):
        """Send command to change channel with flip mode support"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        if not switcher["connected"] or not switcher["serial_connection"]:
            return

        try:
            # Check if flip mode is enabled for this channel
            flip_mode = switcher["flip_modes"][channel_idx]

            # Format: Connect <channel> <0/1>
            # Channel numbers are 1-based for the device (0-5 becomes 1-6)
            # 0 = Normal mode, 1 = Flip mode
            flip_flag = "1" if flip_mode else "0"
            command = f"Connect {channel_idx + 1} {flip_flag}\r\n"

            switcher["serial_connection"].write(command.encode('utf-8'))
        except Exception as e:
            messagebox.showerror("Communication Error", str(e))

    def send_power_command(self, switcher_idx, power_state):
        """Send command to change power state"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        if not switcher["connected"] or not switcher["serial_connection"]:
            return

        try:
            # Format: SET_POWER,<power_state>
            state = "1" if power_state else "0"
            command = f"SET_POWER,{state}\r\n"
            switcher["serial_connection"].write(command.encode('utf-8'))
        except Exception as e:
            messagebox.showerror("Communication Error", str(e))

    def send_channel_off_command(self, switcher_idx, channel_idx=None):
        """Send command to disconnect all channels"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        if not switcher["connected"] or not switcher["serial_connection"]:
            return

        try:
            # Format: disconnect
            command = "disconnect\r\n"
            switcher["serial_connection"].write(command.encode('utf-8'))
        except Exception as e:
            messagebox.showerror("Communication Error", str(e))

    def get_switcher_status(self, switcher_idx):
        """Get and display current status of all ports on a switcher"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]

        # Check if the switcher is connected
        if not switcher["connected"]:
            messagebox.showwarning("Not Connected", f"Please connect {switcher['name_var'].get()} first")
            return

        if not switcher["serial_connection"]:
            messagebox.showerror("Connection Error", "No active serial connection")
            return

        try:
            # Send status command
            command = "STATUS\r\n"
            switcher["serial_connection"].write(command.encode('utf-8'))

            # Wait for response
            time.sleep(0.2)  # Give device time to respond

            # Read response
            response = ""
            if switcher["serial_connection"].in_waiting:
                response = switcher["serial_connection"].readline().decode('utf-8').strip()

            # If no response, simulate one for testing
            if not response:
                # Simulate status response: "STATUS:CH1=ON,CH2=OFF,CH3=FLIP,CH4=OFF,CH5=ON,CH6=OFF"
                active_ch = switcher["active_channel"]
                status_parts = []
                for i in range(6):
                    if i == active_ch:
                        mode = "FLIP" if switcher["flip_modes"][i] else "ON"
                        status_parts.append(f"CH{i+1}={mode}")
                    else:
                        status_parts.append(f"CH{i+1}=OFF")
                response = f"STATUS:{','.join(status_parts)}"

            # Display status in a popup
            self.show_status_popup(switcher_idx, response)

            # Update main status bar
            self.status_label.configure(
                text=f"Status retrieved from {switcher['name_var'].get()}"
            )

        except Exception as e:
            messagebox.showerror("Communication Error", f"Failed to get status: {str(e)}")

    def show_status_popup(self, switcher_idx, status_response):
        """Display status information in a popup window"""
        if switcher_idx < 0 or switcher_idx >= len(self.switchers):
            return

        switcher = self.switchers[switcher_idx]
        switcher_name = switcher['name_var'].get()

        # Create popup window
        popup = ctk.CTkToplevel(self)
        popup.title(f"Status - {switcher_name}")
        popup.geometry("400x300")
        popup.resizable(False, False)

        # Center the popup
        popup.transient(self)
        popup.grab_set()

        # Title
        title_label = ctk.CTkLabel(
            popup,
            text=f"Port Status - {switcher_name}",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=(TEXT_COLOR_LIGHT, TEXT_COLOR_DARK)
        )
        title_label.pack(pady=10)

        # Status frame
        status_frame = ctk.CTkFrame(popup)
        status_frame.pack(pady=10, padx=20, fill="both", expand=True)

        # Parse and display status
        try:
            # Parse response: "STATUS:CH1=ON,CH2=OFF,CH3=FLIP,CH4=OFF,CH5=ON,CH6=OFF"
            if "STATUS:" in status_response:
                status_data = status_response.split("STATUS:")[1]
                channels = status_data.split(",")

                for i, channel_status in enumerate(channels):
                    if "=" in channel_status:
                        ch_name, ch_state = channel_status.split("=")

                        # Create status row
                        row_frame = ctk.CTkFrame(status_frame, fg_color="transparent")
                        row_frame.pack(fill="x", padx=10, pady=5)

                        # Channel label
                        ch_label = ctk.CTkLabel(
                            row_frame,
                            text=f"{ch_name}:",
                            font=ctk.CTkFont(size=12, weight="bold"),
                            width=60
                        )
                        ch_label.pack(side="left", padx=(0, 10))

                        # Status label with color coding
                        if ch_state == "ON":
                            status_color = (ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
                            status_text = "ACTIVE (Normal)"
                        elif ch_state == "FLIP":
                            status_color = (ACCENT_COLOR_LIGHT, ACCENT_COLOR_DARK)
                            status_text = "ACTIVE (Flip Mode)"
                        else:
                            status_color = ("gray", "gray")
                            status_text = "INACTIVE"

                        status_label = ctk.CTkLabel(
                            row_frame,
                            text=status_text,
                            font=ctk.CTkFont(size=12),
                            text_color=status_color
                        )
                        status_label.pack(side="left")
            else:
                # Raw response display
                raw_label = ctk.CTkLabel(
                    status_frame,
                    text=f"Raw Response:\n{status_response}",
                    font=ctk.CTkFont(size=11),
                    justify="left"
                )
                raw_label.pack(pady=20)

        except Exception as e:
            error_label = ctk.CTkLabel(
                status_frame,
                text=f"Error parsing status:\n{str(e)}\n\nRaw response:\n{status_response}",
                font=ctk.CTkFont(size=11),
                justify="left"
            )
            error_label.pack(pady=20)

        # Close button
        close_button = ctk.CTkButton(
            popup,
            text="Close",
            command=popup.destroy,
            width=100,
            height=30,
            fg_color=(MAIN_COLOR_LIGHT, MAIN_COLOR_DARK),
            hover_color=(MAIN_COLOR_DARK, MAIN_COLOR_LIGHT)
        )
        close_button.pack(pady=10)

    def change_appearance_mode(self, new_appearance_mode):
        """Change the appearance mode (light/dark)"""
        ctk.set_appearance_mode(new_appearance_mode)
        # Save settings when theme changes
        self.save_settings()

    def change_color_theme(self, new_theme):
        """Change the color theme"""
        apply_theme(new_theme)
        self.current_theme = new_theme
        # Update the theme menu to reflect the change
        self.theme_menu.set(new_theme)
        # Update header and status bar colors
        self.update_header_colors()
        self.update_status_bar_colors()
        # Trigger a UI refresh by updating switcher panels
        self.update_switcher_panels()
        # Save settings when theme changes
        self.save_settings()

if __name__ == "__main__":
    app = USBSwitcherApp()
    app.mainloop()
