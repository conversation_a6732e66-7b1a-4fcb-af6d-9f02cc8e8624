# Serial Monitor - Professional Installer

## Installation Instructions

### Method 1: Automatic Installation (Recommended)
1. **Right-click** on `install.bat`
2. Select **"Run as administrator"**
3. Follow the on-screen prompts
4. Choose whether to create desktop shortcut
5. Choose whether to launch the application

### Method 2: Manual Installation
1. Copy the `SerialMonitor` folder to `C:\Program Files\`
2. Run `create_desktop_shortcut.bat` if you want a desktop shortcut
3. Navigate to the installation folder and run `SerialMonitor.exe`

## What Gets Installed

- **Application**: `C:\Program Files\Serial Monitor\SerialMonitor.exe`
- **Start Menu**: Start Menu > Serial Monitor
- **Desktop Shortcut**: Optional during installation
- **Uninstaller**: Available in Start Menu and Add/Remove Programs

## Features

✓ Professional Windows installer
✓ Start Menu integration
✓ Desktop shortcut (optional)
✓ Proper uninstaller
✓ Registry entries for Add/Remove Programs
✓ Administrator privilege handling
✓ Custom application icon

## Uninstallation

### Method 1: Start Menu
- Go to Start Menu > Serial Monitor > Uninstall Serial Monitor

### Method 2: Control Panel
- Go to Control Panel > Programs > Programs and Features
- Find "Serial Monitor" and click Uninstall

### Method 3: Manual
- Run the uninstaller from: `C:\Program Files\Serial Monitor\uninstall.bat`

## System Requirements

- Windows 10/11 (64-bit)
- Administrator privileges for installation
- 50MB free disk space

## Troubleshooting

**Installation fails:**
- Ensure you're running as administrator
- Check available disk space
- Temporarily disable antivirus

**Application won't start:**
- Check if all files were copied correctly
- Verify Windows version compatibility
- Install Visual C++ Redistributable if needed

## Support

For support and updates, please refer to the application documentation.

---
Serial Monitor v1.0 - Professional Serial Communication Tool
