<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football - React Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0ea5e9',
                        secondary: '#10b981',
                        accent: '#f59e0b',
                        dark: '#1e293b',
                    },
                }
            }
        }
    </script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // React components
        const Header = () => {
            return (
                <header className="bg-white shadow-md">
                    <div className="container mx-auto px-4 py-4 flex justify-between items-center">
                        <div className="text-xl font-bold text-primary">Soapy Football</div>
                        <nav>
                            <ul className="flex space-x-4">
                                <li><a href="#" className="text-gray-700 hover:text-primary">Home</a></li>
                                <li><a href="#" className="text-gray-700 hover:text-primary">Book Now</a></li>
                                <li><a href="#" className="text-gray-700 hover:text-primary">My Bookings</a></li>
                                <li><a href="#" className="bg-primary text-white px-4 py-2 rounded-md">Sign In</a></li>
                            </ul>
                        </nav>
                    </div>
                </header>
            );
        };

        const Hero = () => {
            return (
                <section className="py-16 bg-gradient-to-b from-primary/10 to-white">
                    <div className="container mx-auto px-4">
                        <div className="max-w-4xl mx-auto text-center">
                            <h1 className="text-4xl font-bold mb-6">
                                <span className="text-primary">Soapy Football</span> Turf Booking
                            </h1>
                            <p className="text-xl text-gray-600 mb-8">
                                Experience the thrill of playing football on our premium soapy turf. 
                                Perfect for friendly matches, corporate events, and weekend fun with friends and family.
                                Book your slot today and enjoy a unique football experience!
                            </p>
                            <button className="bg-primary text-white px-8 py-3 rounded-md font-medium hover:bg-primary/90 shadow-lg">
                                Book Now
                            </button>
                        </div>
                    </div>
                </section>
            );
        };

        const FeatureCard = ({ icon, title, description }) => {
            return (
                <div className="bg-gray-50 p-6 rounded-lg shadow-md">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mb-4">
                        {icon}
                    </div>
                    <h3 className="text-xl font-semibold text-dark mb-2">{title}</h3>
                    <p className="text-gray-600">{description}</p>
                </div>
            );
        };

        const Features = () => {
            return (
                <section className="py-16 bg-white">
                    <div className="container mx-auto px-4">
                        <h2 className="text-3xl font-bold text-center mb-12">Why Choose Soapy Football?</h2>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <FeatureCard 
                                icon={
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                }
                                title="Unique Experience"
                                description="Enjoy football with a twist! Our soapy turf adds an exciting challenge to the game."
                            />
                            
                            <FeatureCard 
                                icon={
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                }
                                title="Convenient Booking"
                                description="Easy online booking system with flexible time slots from 7 AM to 10 PM."
                            />
                            
                            <FeatureCard 
                                icon={
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2z" />
                                    </svg>
                                }
                                title="Secure Payments"
                                description="Hassle-free booking with secure online payments through Razorpay."
                            />
                        </div>
                    </div>
                </section>
            );
        };

        const Footer = () => {
            return (
                <footer className="bg-dark text-white py-8">
                    <div className="container mx-auto px-4 text-center">
                        <p>&copy; {new Date().getFullYear()} Soapy Football. All rights reserved.</p>
                    </div>
                </footer>
            );
        };

        const App = () => {
            return (
                <div className="min-h-screen bg-gray-50 flex flex-col">
                    <Header />
                    <main className="flex-grow">
                        <Hero />
                        <Features />
                    </main>
                    <Footer />
                </div>
            );
        };

        // Render the App component
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
