@echo off
title Serial Monitor Uninstaller
color 0C
echo.
echo ===============================================
echo    Serial Monitor Uninstaller
echo ===============================================
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo.
    echo ERROR: This uninstaller requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

set "INSTALL_DIR=%ProgramFiles%\Serial Monitor"

echo Uninstalling Serial Monitor...
echo.

REM Remove Start Menu shortcuts
echo Removing Start Menu shortcuts...
if exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Serial Monitor" (
    rmdir /S /Q "%ProgramData%\Microsoft\Windows\Start Menu\Programs\Serial Monitor"
)

REM Remove desktop shortcut
echo Removing desktop shortcut...
if exist "%PUBLIC%\Desktop\Serial Monitor.lnk" (
    del /Q "%PUBLIC%\Desktop\Serial Monitor.lnk"
)

REM Remove registry entries
echo Removing registry entries...
reg delete "HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\SerialMonitor" /f >nul 2>&1

REM Remove installation directory
echo Removing installation files...
if exist "%INSTALL_DIR%" (
    rmdir /S /Q "%INSTALL_DIR%"
)

echo.
echo ===============================================
echo    Uninstallation completed successfully!
echo ===============================================
echo.
echo Serial Monitor has been completely removed from your system.
echo.
echo Press any key to exit.
pause >nul
