"""
Chroma 62006P Programmable DC Power Supply Driver
Single channel: 0-100V, 0-25A, 600W
Based on Chroma 62000P series manual
"""

from typing import Dict, Any, List, Optional
import time
from .base_instrument import BaseInstrument


class Chroma62006P(BaseInstrument):
    """Driver for Chroma 62006P Programmable DC Power Supply"""
    
    def __init__(self, name: str = "62006P", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        
        # Specifications
        self.max_voltage = 100.0  # V
        self.max_current = 25.0   # A
        self.max_power = 600.0    # W
        
        # Current settings
        self.settings = {
            'voltage': 0.0,
            'current': 0.0,
            'enabled': False,
            'ovp': 110.0,  # Over-voltage protection
            'ocp': 27.5,   # Over-current protection
            'opp': 660.0,  # Over-power protection
            'voltage_slew_rate': 'MAX',
            'current_slew_rate': 'MAX'
        }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get power supply capabilities"""
        return {
            'type': 'power_supply',
            'channels': 1,
            'max_voltage': self.max_voltage,
            'max_current': self.max_current,
            'max_power': self.max_power,
            'voltage_range': [0, self.max_voltage],
            'current_range': [0, self.max_current],
            'power_range': [0, self.max_power],
            'features': [
                'Programmable voltage/current slew rate',
                'Over-voltage/current/power protection',
                'Remote sensing',
                'Series/parallel operation',
                'APG (Analog Programming)',
                'Sequence programming'
            ]
        }
    
    def initialize(self) -> bool:
        """Initialize power supply to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(2)
            
            # Set to safe initial state
            self.set_output_enabled(False)
            self.set_voltage(0.0)
            self.set_current(0.1)  # Minimum current limit
            
            # Set protection limits
            self.set_ovp(110.0)
            self.set_ocp(27.5)
            self.set_opp(660.0)
            
            # Clear any errors
            self.write("*CLS")
            
            self.logger.info("Chroma 62006P initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Chroma 62006P: {e}")
            return False
    
    # Voltage Control
    def set_voltage(self, voltage: float) -> bool:
        """Set output voltage (V)"""
        if not 0 <= voltage <= self.max_voltage:
            raise ValueError(f"Voltage must be 0-{self.max_voltage}V")
        
        success = self.write(f"SOUR:VOLT {voltage}")
        if success:
            self.settings['voltage'] = voltage
        return success
    
    def get_voltage_setpoint(self) -> float:
        """Get voltage setpoint"""
        response = self.query("SOUR:VOLT?")
        return float(response)
    
    def measure_voltage(self) -> float:
        """Measure actual output voltage"""
        response = self.query("MEAS:VOLT?")
        return float(response)
    
    # Current Control
    def set_current(self, current: float) -> bool:
        """Set current limit (A)"""
        if not 0 <= current <= self.max_current:
            raise ValueError(f"Current must be 0-{self.max_current}A")
        
        success = self.write(f"SOUR:CURR {current}")
        if success:
            self.settings['current'] = current
        return success
    
    def get_current_setpoint(self) -> float:
        """Get current setpoint"""
        response = self.query("SOUR:CURR?")
        return float(response)
    
    def measure_current(self) -> float:
        """Measure actual output current"""
        response = self.query("MEAS:CURR?")
        return float(response)
    
    # Power Measurement
    def measure_power(self) -> float:
        """Measure actual output power"""
        response = self.query("MEAS:POW?")
        return float(response)
    
    # Output Control
    def set_output_enabled(self, enabled: bool) -> bool:
        """Enable or disable output"""
        state = "ON" if enabled else "OFF"
        success = self.write(f"OUTP {state}")
        if success:
            self.settings['enabled'] = enabled
        return success
    
    def get_output_enabled(self) -> bool:
        """Get output enabled state"""
        response = self.query("OUTP?")
        return response.strip() == "1"
    
    # Protection Settings
    def set_ovp(self, voltage: float) -> bool:
        """Set over-voltage protection (V)"""
        if not 0 <= voltage <= self.max_voltage * 1.1:
            raise ValueError(f"OVP must be 0-{self.max_voltage * 1.1}V")
        
        success = self.write(f"SOUR:VOLT:PROT {voltage}")
        if success:
            self.settings['ovp'] = voltage
        return success
    
    def get_ovp(self) -> float:
        """Get over-voltage protection setting"""
        response = self.query("SOUR:VOLT:PROT?")
        return float(response)
    
    def set_ocp(self, current: float) -> bool:
        """Set over-current protection (A)"""
        if not 0 <= current <= self.max_current * 1.1:
            raise ValueError(f"OCP must be 0-{self.max_current * 1.1}A")
        
        success = self.write(f"SOUR:CURR:PROT {current}")
        if success:
            self.settings['ocp'] = current
        return success
    
    def get_ocp(self) -> float:
        """Get over-current protection setting"""
        response = self.query("SOUR:CURR:PROT?")
        return float(response)
    
    def set_opp(self, power: float) -> bool:
        """Set over-power protection (W)"""
        if not 0 <= power <= self.max_power * 1.1:
            raise ValueError(f"OPP must be 0-{self.max_power * 1.1}W")
        
        success = self.write(f"SOUR:POW:PROT {power}")
        if success:
            self.settings['opp'] = power
        return success
    
    def get_opp(self) -> float:
        """Get over-power protection setting"""
        response = self.query("SOUR:POW:PROT?")
        return float(response)
    
    # Slew Rate Control
    def set_voltage_slew_rate(self, slew_rate) -> bool:
        """Set voltage slew rate (V/ms or 'MAX')"""
        if isinstance(slew_rate, str) and slew_rate.upper() == 'MAX':
            command = "SOUR:VOLT:SLEW MAX"
        else:
            command = f"SOUR:VOLT:SLEW {slew_rate}"
        
        success = self.write(command)
        if success:
            self.settings['voltage_slew_rate'] = slew_rate
        return success
    
    def get_voltage_slew_rate(self) -> float:
        """Get voltage slew rate"""
        response = self.query("SOUR:VOLT:SLEW?")
        return float(response)
    
    def set_current_slew_rate(self, slew_rate) -> bool:
        """Set current slew rate (A/ms or 'MAX')"""
        if isinstance(slew_rate, str) and slew_rate.upper() == 'MAX':
            command = "SOUR:CURR:SLEW MAX"
        else:
            command = f"SOUR:CURR:SLEW {slew_rate}"
        
        success = self.write(command)
        if success:
            self.settings['current_slew_rate'] = slew_rate
        return success
    
    def get_current_slew_rate(self) -> float:
        """Get current slew rate"""
        response = self.query("SOUR:CURR:SLEW?")
        return float(response)
    
    # Status and Mode
    def get_operating_mode(self) -> str:
        """Get operating mode (CV or CC)"""
        # Check status register to determine mode
        try:
            status = self.query("STAT:OPER:COND?")
            status_int = int(status)
            
            # Bit interpretation based on Chroma manual
            if status_int & 0x01:  # CV mode
                return "CV"
            elif status_int & 0x02:  # CC mode
                return "CC"
            else:
                return "OFF"
        except:
            return "UNKNOWN"
    
    def get_all_measurements(self) -> Dict[str, float]:
        """Get all measurements"""
        try:
            measurements = {
                'voltage': self.measure_voltage(),
                'current': self.measure_current(),
                'power': self.measure_power()
            }
            return measurements
        except Exception as e:
            self.logger.error(f"Failed to get measurements: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive status"""
        base_status = super().get_status()
        
        if self.connected:
            try:
                ps_status = {
                    'enabled': self.get_output_enabled(),
                    'mode': self.get_operating_mode(),
                    'setpoints': {
                        'voltage': self.get_voltage_setpoint(),
                        'current': self.get_current_setpoint()
                    },
                    'measurements': self.get_all_measurements(),
                    'protection': {
                        'ovp': self.get_ovp(),
                        'ocp': self.get_ocp(),
                        'opp': self.get_opp()
                    },
                    'slew_rates': {
                        'voltage': self.get_voltage_slew_rate(),
                        'current': self.get_current_slew_rate()
                    }
                }
                base_status.update(ps_status)
            except Exception as e:
                self.logger.error(f"Failed to get status: {e}")
        
        return base_status
    
    # Error Checking
    def check_errors(self) -> List[str]:
        """Check for instrument errors"""
        errors = []
        try:
            while True:
                error = self.query("SYST:ERR?")
                if error.startswith("0,"):
                    break
                errors.append(error)
        except Exception as e:
            self.logger.error(f"Failed to check errors: {e}")
        
        return errors
    
    def get_protection_status(self) -> Dict[str, bool]:
        """Get protection status"""
        try:
            # Query protection status register
            status = self.query("STAT:QUES:COND?")
            status_int = int(status)
            
            return {
                'ovp_tripped': bool(status_int & 0x01),
                'ocp_tripped': bool(status_int & 0x02),
                'opp_tripped': bool(status_int & 0x04),
                'otp_tripped': bool(status_int & 0x08),  # Over-temperature
                'fan_fault': bool(status_int & 0x10)
            }
        except Exception as e:
            self.logger.error(f"Failed to get protection status: {e}")
            return {}
    
    # Convenience Methods
    def set_output(self, voltage: float, current: float, enabled: bool = True) -> bool:
        """Set voltage, current, and enable state"""
        success = True
        success &= self.set_voltage(voltage)
        success &= self.set_current(current)
        success &= self.set_output_enabled(enabled)
        return success
    
    def emergency_stop(self) -> bool:
        """Emergency stop - turn off output immediately"""
        return self.set_output_enabled(False)
