"""
Tektronix MSO58B Mixed Signal Oscilloscope Driver
8-channel oscilloscope with 1 GHz bandwidth and 6.25 GS/s sample rate
"""

from typing import Dict, Any, List, Optional, Tuple
import time
from .base_instrument import BaseInstrument


class TektronixMSO58B(BaseInstrument):
    """Driver for Tektronix MSO58B 8-channel oscilloscope"""
    
    def __init__(self, name: str = "MSO58B", visa_address: str = "", visa_manager=None, **kwargs):
        super().__init__(name, visa_address, visa_manager, **kwargs)
        self.num_channels = 8
        self.bandwidth = "1 GHz"
        self.max_sample_rate = "6.25 GS/s"
        self.memory_depth = "62.5 Mpts"
        
        # Channel settings
        self.channel_settings = {}
        for i in range(1, self.num_channels + 1):
            self.channel_settings[i] = {
                'enabled': False,
                'scale': 1.0,  # V/div
                'position': 0.0,  # V
                'coupling': 'DC',
                'bandwidth': '1GHZ',
                'impedance': '1MEG'
            }
    
    def get_capabilities(self) -> Dict[str, Any]:
        """Get oscilloscope capabilities"""
        return {
            'type': 'oscilloscope',
            'channels': self.num_channels,
            'bandwidth': self.bandwidth,
            'max_sample_rate': self.max_sample_rate,
            'memory_depth': self.memory_depth,
            'coupling_options': ['AC', 'DC', 'DCREJ'],
            'bandwidth_options': ['20MHZ', '250MHZ', '1GHZ'],
            'impedance_options': ['FIFTY', '1MEG'],
            'trigger_types': ['EDGE', 'PULSE', 'RUNT', 'TIMEOUT', 'TRANSITION']
        }
    
    def initialize(self) -> bool:
        """Initialize oscilloscope to known state"""
        try:
            # Reset to factory defaults
            self.reset()
            time.sleep(2)
            
            # Set up basic configuration
            self.write("HEADER OFF")  # Turn off headers in responses
            self.write("VERBOSE OFF")  # Turn off verbose responses
            
            # Initialize all channels to default state
            for ch in range(1, self.num_channels + 1):
                self.set_channel_enabled(ch, False)
                self.set_channel_scale(ch, 1.0)
                self.set_channel_position(ch, 0.0)
                self.set_channel_coupling(ch, 'DC')
            
            self.logger.info("MSO58B initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize MSO58B: {e}")
            return False
    
    # Channel Control Methods
    def set_channel_enabled(self, channel: int, enabled: bool) -> bool:
        """Enable or disable a channel"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        state = "ON" if enabled else "OFF"
        success = self.write(f"CH{channel}:STATE {state}")
        if success:
            self.channel_settings[channel]['enabled'] = enabled
        return success
    
    def get_channel_enabled(self, channel: int) -> bool:
        """Get channel enabled state"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"CH{channel}:STATE?")
        return response == "1"
    
    def set_channel_scale(self, channel: int, scale: float) -> bool:
        """Set channel vertical scale (V/div)"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        success = self.write(f"CH{channel}:SCALE {scale}")
        if success:
            self.channel_settings[channel]['scale'] = scale
        return success
    
    def get_channel_scale(self, channel: int) -> float:
        """Get channel vertical scale"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"CH{channel}:SCALE?")
        return float(response)
    
    def set_channel_position(self, channel: int, position: float) -> bool:
        """Set channel vertical position (V)"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        success = self.write(f"CH{channel}:POSITION {position}")
        if success:
            self.channel_settings[channel]['position'] = position
        return success
    
    def get_channel_position(self, channel: int) -> float:
        """Get channel vertical position"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"CH{channel}:POSITION?")
        return float(response)
    
    def set_channel_coupling(self, channel: int, coupling: str) -> bool:
        """Set channel coupling (AC, DC, DCREJ)"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        coupling = coupling.upper()
        if coupling not in ['AC', 'DC', 'DCREJ']:
            raise ValueError("Coupling must be AC, DC, or DCREJ")
        
        success = self.write(f"CH{channel}:COUPLING {coupling}")
        if success:
            self.channel_settings[channel]['coupling'] = coupling
        return success
    
    def get_channel_coupling(self, channel: int) -> str:
        """Get channel coupling"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")

        return self.query(f"CH{channel}:COUPLING?")

    def set_channel_label(self, channel: int, label: str) -> bool:
        """Set channel label"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")

        # Try different command formats for channel labels
        commands_to_try = [
            f'CH{channel}:LABel:NAMe "{label}"',
            f'CH{channel}:LABel "{label}"',
            f'DISPlay:CH{channel}:LABel "{label}"',
            f'DISPlay:LABel:CH{channel} "{label}"'
        ]

        for cmd in commands_to_try:
            try:
                success = self.write(cmd)
                if success:
                    self.logger.info(f"Channel {channel} label set using: {cmd}")
                    self.channel_settings[channel]['label'] = label
                    return True
            except Exception as e:
                self.logger.debug(f"Command {cmd} failed: {e}")
                continue

        self.logger.error(f"All label commands failed for CH{channel}")
        return False

    def get_channel_label(self, channel: int) -> str:
        """Get channel label"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")

        # Try different query formats
        queries_to_try = [
            f"CH{channel}:LABel:NAMe?",
            f"CH{channel}:LABel?",
            f"DISPlay:CH{channel}:LABel?",
            f"DISPlay:LABel:CH{channel}?"
        ]

        for query in queries_to_try:
            try:
                response = self.query(query)
                return response.strip('"')
            except Exception as e:
                self.logger.debug(f"Query {query} failed: {e}")
                continue

        return f"CH{channel}"  # Default label
    
    def set_channel_bandwidth(self, channel: int, bandwidth: str) -> bool:
        """Set channel bandwidth limit"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        bandwidth = bandwidth.upper()
        if bandwidth not in ['20MHZ', '250MHZ', '1GHZ']:
            raise ValueError("Bandwidth must be 20MHZ, 250MHZ, or 1GHZ")
        
        success = self.write(f"CH{channel}:BANDWIDTH {bandwidth}")
        if success:
            self.channel_settings[channel]['bandwidth'] = bandwidth
        return success
    
    # Timebase Control
    def set_timebase_scale(self, scale: float) -> bool:
        """Set horizontal timebase scale (s/div)"""
        return self.write(f"HORIZONTAL:SCALE {scale}")
    
    def get_timebase_scale(self) -> float:
        """Get horizontal timebase scale"""
        response = self.query("HORIZONTAL:SCALE?")
        return float(response)
    
    def set_timebase_position(self, position: float) -> bool:
        """Set horizontal timebase position (s)"""
        return self.write(f"HORIZONTAL:POSITION {position}")
    
    def get_timebase_position(self) -> float:
        """Get horizontal timebase position"""
        response = self.query("HORIZONTAL:POSITION?")
        return float(response)
    
    # Trigger Control
    def set_trigger_source(self, source: str) -> bool:
        """Set trigger source (CH1-CH8, EXT, LINE)"""
        source = source.upper()
        return self.write(f"TRIGGER:A:EDGE:SOURCE {source}")
    
    def get_trigger_source(self) -> str:
        """Get trigger source"""
        return self.query("TRIGGER:A:EDGE:SOURCE?")
    
    def set_trigger_level(self, level: float, source: str = None) -> bool:
        """Set trigger level (V) for specific source"""
        if source is None:
            # Get current trigger source
            source = self.get_trigger_source()

        # Format the command based on source type
        if source.startswith('CH'):
            return self.write(f"TRIGGER:A:LEVEL:{source} {level}")
        else:
            return self.write(f"TRIGGER:A:LEVEL {level}")

    def get_trigger_level(self, source: str = None) -> float:
        """Get trigger level for specific source"""
        if source is None:
            # Get current trigger source
            source = self.get_trigger_source()

        if source.startswith('CH'):
            response = self.query(f"TRIGGER:A:LEVEL:{source}?")
        else:
            response = self.query("TRIGGER:A:LEVEL?")
        return float(response)
    
    def set_trigger_slope(self, slope: str) -> bool:
        """Set trigger slope (RISE, FALL, EITHER)"""
        slope = slope.upper()
        if slope not in ['RISE', 'FALL', 'EITHER']:
            raise ValueError("Slope must be RISE, FALL, or EITHER")
        return self.write(f"TRIGGER:A:EDGE:SLOPE {slope}")
    
    def get_trigger_slope(self) -> str:
        """Get trigger slope"""
        return self.query("TRIGGER:A:EDGE:SLOPE?")

    def set_trigger_mode(self, mode: str) -> bool:
        """Set trigger mode (AUTO, NORMAL)"""
        mode = mode.upper()
        if mode not in ['AUTO', 'NORMAL']:
            raise ValueError("Mode must be AUTO or NORMAL")
        return self.write(f"TRIGGER:A:MODE {mode}")

    def get_trigger_mode(self) -> str:
        """Get trigger mode"""
        return self.query("TRIGGER:A:MODE?")

    def set_display_style(self, style: str) -> bool:
        """Set display style (OVERLAY, STACKED)"""
        style = style.upper()
        if style not in ['OVERLAY', 'STACKED']:
            raise ValueError("Style must be OVERLAY or STACKED")

        # For MSO58B, the correct commands are:
        commands_to_try = [
            # Primary command for MSO58B
            f"DISPLAY:WAVEVIEW1:VIEW:STYLE {style}",
            # Alternative formats to try
            f"DISPLAY:WAVEVIEW:VIEW:STYLE {style}",
            f"DISPLAY:VIEW:STYLE {style}",
            f"WAVEVIEW1:VIEW:STYLE {style}"
        ]

        for cmd in commands_to_try:
            try:
                success = self.write(cmd)
                if success:
                    # Verify the command worked by querying the setting
                    try:
                        current_style = self.get_display_style()
                        if current_style.upper() == style:
                            self.logger.info(f"Display style set to {style} using: {cmd}")
                            return True
                    except:
                        # If query fails, assume write was successful
                        self.logger.info(f"Display style set to {style} using: {cmd}")
                        return True
            except Exception as e:
                self.logger.debug(f"Command {cmd} failed: {e}")
                continue

        self.logger.error(f"All display style commands failed")
        return False

    def get_display_style(self) -> str:
        """Get display style"""
        queries_to_try = [
            "DISPLAY:WAVEVIEW1:VIEW:STYLE?",
            "DISPLAY:WAVEVIEW:VIEW:STYLE?",
            "DISPLAY:VIEW:STYLE?",
            "WAVEVIEW1:VIEW:STYLE?"
        ]

        for query in queries_to_try:
            try:
                response = self.query(query)
                if response:
                    return response.strip().upper()
            except Exception as e:
                self.logger.debug(f"Query {query} failed: {e}")
                continue

        return "OVERLAY"  # Default

    # Acquisition Control
    def start_acquisition(self) -> bool:
        """Start acquisition"""
        return self.write("ACQUIRE:STATE RUN")
    
    def stop_acquisition(self) -> bool:
        """Stop acquisition"""
        return self.write("ACQUIRE:STATE STOP")
    
    def single_acquisition(self) -> bool:
        """Single acquisition"""
        return self.write("ACQUIRE:STOPAFTER SEQUENCE")

    def continuous_acquisition(self) -> bool:
        """Continuous acquisition"""
        return self.write("ACQUIRE:STOPAFTER RUNSTOP")

    def get_acquisition_state(self) -> str:
        """Get acquisition state"""
        return self.query("ACQUIRE:STATE?")
    
    # Measurement Methods
    def measure_voltage_pk2pk(self, channel: int) -> float:
        """Measure peak-to-peak voltage"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"MEASUREMENT:IMMED:SOURCE CH{channel}; TYPE PK2PK; VALUE?")
        return float(response)
    
    def measure_voltage_rms(self, channel: int) -> float:
        """Measure RMS voltage"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"MEASUREMENT:IMMED:SOURCE CH{channel}; TYPE RMS; VALUE?")
        return float(response)
    
    def measure_frequency(self, channel: int) -> float:
        """Measure frequency"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        response = self.query(f"MEASUREMENT:IMMED:SOURCE CH{channel}; TYPE FREQUENCY; VALUE?")
        return float(response)
    
    def get_all_measurements(self, channel: int) -> Dict[str, float]:
        """Get all basic measurements for a channel"""
        if not 1 <= channel <= self.num_channels:
            raise ValueError(f"Channel must be 1-{self.num_channels}")
        
        measurements = {}
        try:
            measurements['pk2pk'] = self.measure_voltage_pk2pk(channel)
            measurements['rms'] = self.measure_voltage_rms(channel)
            measurements['frequency'] = self.measure_frequency(channel)
        except Exception as e:
            self.logger.warning(f"Some measurements failed for CH{channel}: {e}")
        
        return measurements
    
    def auto_setup(self) -> bool:
        """Perform auto setup"""
        return self.write("AUTOSET EXECUTE")
    
    def save_screenshot(self, filename: str, format: str = "PNG") -> bool:
        """Save screenshot to file"""
        try:
            # Set image format
            format = format.upper()
            if format not in ['PNG', 'BMP', 'JPEG', 'TIFF']:
                format = 'PNG'

            # Convert to forward slashes for SCPI compatibility
            filename = filename.replace('\\', '/')

            # Try different screenshot command formats for MSO58B
            commands_to_try = [
                # Method 1: Modern MSO format (most likely to work)
                [f'SAVE:IMAGE:FILEFORMAT {format}', f'SAVE:IMAGE "{filename}"'],
                # Method 2: Alternative SAVE format
                [f'SAVE:IMAGE:FILEFORMAT {format}', f'SAVE:IMAGE:FILENAME "{filename}"', 'SAVE:IMAGE'],
                # Method 3: HARDCopy format
                [f'HARDCopy:FORMat {format}', f'HARDCopy:FILEName "{filename}"', 'HARDCopy START'],
                # Method 4: Simple EXPORT format
                [f'EXPORT:IMAGE "{filename}"'],
                # Method 5: Direct hardcopy to default location
                [f'HARDCopy:FORMat {format}', 'HARDCopy START']
            ]

            for i, cmd_sequence in enumerate(commands_to_try, 1):
                try:
                    self.logger.debug(f"Trying screenshot method {i}: {cmd_sequence}")
                    success = True

                    for cmd in cmd_sequence:
                        if not self.write(cmd):
                            success = False
                            break
                        # Small delay between commands
                        time.sleep(0.1)

                    if success:
                        # Wait for operation to complete
                        time.sleep(1.0)

                        # Check if operation completed successfully
                        try:
                            self.query("*OPC?")  # Wait for operation complete
                        except:
                            pass  # Ignore if OPC query fails

                        self.logger.info(f"Screenshot saved using method {i}: {cmd_sequence}")
                        return True

                except Exception as e:
                    self.logger.debug(f"Command sequence {i} failed: {e}")
                    continue

            self.logger.error("All screenshot commands failed")
            return False

        except Exception as e:
            self.logger.error(f"Failed to save screenshot: {e}")
            return False

    def capture_screen_data(self) -> bytes:
        """Capture screen data as bytes"""
        try:
            # Try different methods to get screen data
            methods_to_try = [
                # Method 1: Modern format
                ["SAVE:IMAGE:FILEFORMAT PNG", "HARDCOPY:PORT GPIB", "HARDCOPY START"],
                # Method 2: Alternative
                ["HARDCopy:FORMat PNG", "HARDCopy:PORT GPIB", "HARDCopy START"],
                # Method 3: Direct query
                ["DISPLAY:DATA?"]
            ]

            for method in methods_to_try:
                try:
                    for cmd in method[:-1]:
                        self.write(cmd)

                    # Get the data with the last command
                    if method[-1].endswith('?'):
                        data = self.query_binary(method[-1])
                    else:
                        self.write(method[-1])
                        data = self.query_binary("*OPC?")  # Wait for completion

                    if data:
                        return data

                except Exception as e:
                    self.logger.debug(f"Screen capture method {method} failed: {e}")
                    continue

            return b""

        except Exception as e:
            self.logger.error(f"Failed to capture screen data: {e}")
            return b""
