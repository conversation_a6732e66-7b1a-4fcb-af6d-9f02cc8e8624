<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Soapy Football - Booking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0ea5e9',
                        secondary: '#10b981',
                        accent: '#f59e0b',
                        dark: '#1e293b',
                    },
                }
            }
        }
    </script>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/react-datepicker@4.8.0/dist/react-datepicker.css" />
    <script src="https://unpkg.com/react-datepicker@4.8.0/dist/react-datepicker.min.js"></script>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // React components
        const Header = () => {
            return (
                <header className="bg-white shadow-md">
                    <div className="container mx-auto px-4 py-4 flex justify-between items-center">
                        <div className="text-xl font-bold text-primary">Soapy Football</div>
                        <nav>
                            <ul className="flex space-x-4">
                                <li><a href="react-demo.html" className="text-gray-700 hover:text-primary">Home</a></li>
                                <li><a href="booking-demo.html" className="text-gray-700 hover:text-primary font-medium">Book Now</a></li>
                                <li><a href="#" className="text-gray-700 hover:text-primary">My Bookings</a></li>
                                <li><a href="#" className="bg-primary text-white px-4 py-2 rounded-md">Sign In</a></li>
                            </ul>
                        </nav>
                    </div>
                </header>
            );
        };

        const Footer = () => {
            return (
                <footer className="bg-dark text-white py-8">
                    <div className="container mx-auto px-4 text-center">
                        <p>&copy; {new Date().getFullYear()} Soapy Football. All rights reserved.</p>
                    </div>
                </footer>
            );
        };

        const BookingPage = () => {
            const [selectedDate, setSelectedDate] = React.useState(new Date());
            const [selectedSlot, setSelectedSlot] = React.useState(null);
            const [name, setName] = React.useState('');
            const [phone, setPhone] = React.useState('');
            const [isLoading, setIsLoading] = React.useState(false);
            
            // Mock data for available slots
            const slots = [
                { id: 'slot-7', time: '07:00 - 08:00', status: 'available' },
                { id: 'slot-8', time: '08:00 - 09:00', status: 'available' },
                { id: 'slot-9', time: '09:00 - 10:00', status: 'booked' },
                { id: 'slot-10', time: '10:00 - 11:00', status: 'available' },
                { id: 'slot-11', time: '11:00 - 12:00', status: 'available' },
                { id: 'slot-12', time: '12:00 - 13:00', status: 'booked' },
                { id: 'slot-13', time: '13:00 - 14:00', status: 'available' },
                { id: 'slot-14', time: '14:00 - 15:00', status: 'available' },
                { id: 'slot-15', time: '15:00 - 16:00', status: 'available' },
                { id: 'slot-16', time: '16:00 - 17:00', status: 'booked' },
                { id: 'slot-17', time: '17:00 - 18:00', status: 'available' },
                { id: 'slot-18', time: '18:00 - 19:00', status: 'available' },
                { id: 'slot-19', time: '19:00 - 20:00', status: 'booked' },
                { id: 'slot-20', time: '20:00 - 21:00', status: 'available' },
                { id: 'slot-21', time: '21:00 - 22:00', status: 'available' },
            ];
            
            const handleDateChange = (date) => {
                setSelectedDate(date);
                setSelectedSlot(null);
            };
            
            const handleSlotSelect = (slot) => {
                if (slot.status === 'available') {
                    setSelectedSlot(slot);
                }
            };
            
            const handleBooking = () => {
                if (!selectedSlot || !name || !phone) {
                    alert('Please fill in all fields');
                    return;
                }
                
                if (phone.length < 10) {
                    alert('Please enter a valid phone number');
                    return;
                }
                
                setIsLoading(true);
                
                // Simulate API call
                setTimeout(() => {
                    setIsLoading(false);
                    alert(`Booking confirmed for ${selectedDate.toDateString()} at ${selectedSlot.time}`);
                    window.location.href = 'payment-demo.html';
                }, 1500);
            };
            
            // Get max date (today + 2 days)
            const maxDate = new Date();
            maxDate.setDate(maxDate.getDate() + 2);
            
            return (
                <div className="min-h-screen bg-gray-50 py-12">
                    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                        <div className="max-w-3xl mx-auto">
                            <div className="flex justify-between items-center mb-8">
                                <h1 className="text-3xl font-bold text-dark">Book Your Slot</h1>
                                <div className="space-x-4">
                                    <a 
                                        href="react-demo.html"
                                        className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md"
                                    >
                                        Home
                                    </a>
                                    <a 
                                        href="#"
                                        className="px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md"
                                    >
                                        History
                                    </a>
                                </div>
                            </div>
                            
                            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                                {/* Date Selection */}
                                <div className="p-6 border-b">
                                    <h2 className="text-xl font-semibold mb-4">Select Date</h2>
                                    <input 
                                        type="date" 
                                        value={selectedDate.toISOString().split('T')[0]} 
                                        onChange={(e) => handleDateChange(new Date(e.target.value))}
                                        min={new Date().toISOString().split('T')[0]}
                                        max={maxDate.toISOString().split('T')[0]}
                                        className="w-full p-2 border rounded"
                                    />
                                </div>
                                
                                {/* Time Slots */}
                                <div className="p-6 border-b">
                                    <h2 className="text-xl font-semibold mb-4">Available Slots</h2>
                                    
                                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                                        {slots.map((slot) => (
                                            <button
                                                key={slot.id}
                                                onClick={() => handleSlotSelect(slot)}
                                                className={`p-3 rounded-lg text-center transition-colors ${
                                                    selectedSlot?.id === slot.id
                                                        ? 'bg-primary text-white'
                                                        : slot.status === 'available'
                                                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                                        : 'bg-red-100 text-red-800 cursor-not-allowed'
                                                }`}
                                                disabled={slot.status !== 'available'}
                                            >
                                                {slot.time}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                                
                                {/* Booking Form */}
                                {selectedSlot && (
                                    <div className="p-6">
                                        <h2 className="text-xl font-semibold mb-4">Your Information</h2>
                                        
                                        <div className="space-y-4">
                                            <div>
                                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                                                    Full Name
                                                </label>
                                                <input
                                                    type="text"
                                                    id="name"
                                                    value={name}
                                                    onChange={(e) => setName(e.target.value)}
                                                    className="w-full p-2 border rounded"
                                                    placeholder="Enter your full name"
                                                    required
                                                />
                                            </div>
                                            
                                            <div>
                                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                                    Phone Number
                                                </label>
                                                <input
                                                    type="tel"
                                                    id="phone"
                                                    value={phone}
                                                    onChange={(e) => setPhone(e.target.value)}
                                                    className="w-full p-2 border rounded"
                                                    placeholder="Enter your phone number"
                                                    required
                                                />
                                            </div>
                                            
                                            <div className="pt-4">
                                                <button
                                                    onClick={handleBooking}
                                                    disabled={isLoading || !name || !phone}
                                                    className={`w-full px-4 py-2 rounded-md font-medium text-white ${
                                                        isLoading || !name || !phone
                                                            ? 'bg-primary/70 cursor-not-allowed'
                                                            : 'bg-primary hover:bg-primary/90'
                                                    }`}
                                                >
                                                    {isLoading ? 'Processing...' : 'Book Now - ₹200'}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        const App = () => {
            return (
                <div className="min-h-screen bg-gray-50 flex flex-col">
                    <Header />
                    <main className="flex-grow">
                        <BookingPage />
                    </main>
                    <Footer />
                </div>
            );
        };

        // Render the App component
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
