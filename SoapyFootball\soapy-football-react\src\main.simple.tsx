import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'

const App = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-16">
        <h1 className="text-4xl font-bold text-center mb-8">
          <span className="text-primary">Soapy Football</span> Turf Booking
        </h1>
        <p className="text-xl text-gray-600 text-center mb-8">
          Experience the thrill of playing football on our premium soapy turf.
          Perfect for friendly matches, corporate events, and weekend fun with friends and family.
        </p>
        <div className="flex justify-center">
          <button className="bg-primary text-white px-6 py-3 rounded-md font-medium hover:bg-primary/90">
            Book Now
          </button>
        </div>
      </div>
    </div>
  )
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
