# Instrument Configuration File
# This file contains VISA addresses and specifications for all lab instruments

instruments:
  oscilloscope:
    tektronix_mso58b:
      name: "Tektronix MSO58B"
      visa_address: "TCPIP0::***********::inst0::INSTR"  # Your actual MSO address
      type: "tektronix_mso58b"
      channels: 8
      max_sample_rate: "6.25 GS/s"
      bandwidth: "1 GHz"
      memory_depth: "62.5 Mpts"
      enabled: true
      timeout: 10000
      
  power_supplies:
    agilent_e3631a:
      name: "Agilent E3631A"
      visa_address: "GPIB0::5::INSTR"  # Update with actual address
      type: "power_supply"
      channels: 3
      channel_specs:
        1:
          voltage_range: [0, 6.0]
          current_range: [0, 5.0]
          power_max: 30.0
        2:
          voltage_range: [-25.0, 25.0]
          current_range: [0, 1.0]
          power_max: 25.0
        3:
          voltage_range: [-25.0, 25.0]
          current_range: [0, 1.0]
          power_max: 25.0
      enabled: true
      timeout: 5000
      
    chroma_62006p:
      name: "Chroma 62006P"
      visa_address: "GPIB0::6::INSTR"  # Update with actual address
      type: "power_supply"
      channels: 1
      channel_specs:
        1:
          voltage_range: [0, 100.0]
          current_range: [0, 25.0]
          power_max: 600.0
      enabled: true
      timeout: 5000
      
    rigol_dp821a:
      name: "Rigol DP821A"
      visa_address: "USB0::0x1AB1::0x0E11::DP8A123456789::INSTR"  # Update with actual address
      type: "power_supply"
      channels: 2
      channel_specs:
        1:
          voltage_range: [0, 60.0]
          current_range: [0, 1.0]
          power_max: 60.0
        2:
          voltage_range: [0, 8.0]
          current_range: [0, 10.0]
          power_max: 80.0
      enabled: true
      timeout: 5000
      
  loads:
    hp_6063b:
      name: "HP 6063B"
      visa_address: "GPIB0::7::INSTR"  # Update with actual address
      type: "electronic_load"
      channels: 1
      channel_specs:
        1:
          voltage_range: [0, 240.0]
          current_range: [0, 240.0]
          power_max: 240.0
      modes: ["CC", "CV", "CR", "CP"]
      enabled: true
      timeout: 5000
      
  multimeters:
    agilent_34401a:
      name: "Agilent 34401A"
      visa_address: "GPIB0::8::INSTR"  # Update with actual address
      type: "multimeter"
      functions:
        - "DC_VOLTAGE"
        - "AC_VOLTAGE"
        - "DC_CURRENT"
        - "AC_CURRENT"
        - "RESISTANCE_2W"
        - "RESISTANCE_4W"
        - "FREQUENCY"
        - "PERIOD"
        - "CONTINUITY"
        - "DIODE"
      ranges:
        DC_VOLTAGE: [0.1, 1, 10, 100, 1000]
        AC_VOLTAGE: [0.1, 1, 10, 100, 750]
        DC_CURRENT: [0.01, 0.1, 1, 3]
        AC_CURRENT: [1, 3]
        RESISTANCE_2W: [100, 1000, 10000, 100000, 1000000, 10000000, 100000000]
        RESISTANCE_4W: [100, 1000, 10000, 100000, 1000000, 10000000, 100000000]
      enabled: true
      timeout: 5000

# Connection settings
connection:
  visa_library: "@py"  # Use pyvisa-py as default, change to "@ni" for NI-VISA
  scan_for_instruments: true
  auto_identify: true
  
# Safety limits (global overrides)
safety_limits:
  max_voltage: 100.0
  max_current: 50.0
  max_power: 1000.0
  enable_limits: true
  
# GUI settings
gui:
  auto_refresh: true
  refresh_rate: 1000  # milliseconds
  show_plots: true
  log_measurements: true
