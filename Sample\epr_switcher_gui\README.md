# USB Switcher Control GUI

A modern, responsive GUI application for controlling EPR switcher devices with multiple channels. Built with Python and CustomTkinter.

## Features

- **Multi-Switcher Support**: Control up to 4 EPR switchers simultaneously
- **6-Channel Control**: Each switcher supports 6 independent channels with Normal and flip mode toggle
- **Explicit Channel Control**: Channels must be explicitly turned off before switching to prevent accidental changes
- **Serial Communication**: Connect via COM ports with automatic device detection
- **Modern UI**: Dual color themes (Classic Green & Modern Purple) with animations and hover effects
- **Responsive Layout**: Adaptive grid layout optimized for different switcher counts
- **Settings Management**: In-app configuration for switcher names, COM ports, and appearance
- **Connection Monitoring**: Real-time connection status and device identification
- **Power Control**: Individual power management for each switcher
- **Status Monitoring**: Real-time port status queries with detailed popup displays
- **Safety Features**: Confirmation dialogs and warnings to prevent accidental operations

## Requirements

### Python Version
- **Python 3.8 or higher** (recommended: Python 3.9+)

### Dependencies
All dependencies are listed in `requirements.txt`:
- `customtkinter>=5.2.0` - Modern GUI framework
- `pyserial>=3.5` - Serial communication
- `Pillow>=10.0.0` - Image processing for GUI elements
- `PyYAML>=6.0` - Configuration file handling

## Installation

1. **Clone or download** this repository
2. **Install Python** 3.8+ from [python.org](https://python.org)
3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Quick Start

1. **Run the application**:
   ```bash
   python usb_switcher_gui.py
   ```

2. **Configure your switchers**:
   - Click the ⚙ settings icon in the top-right corner
   - Set the number of switchers (1-4)
   - Assign COM ports to each switcher
   - Customize switcher names

3. **Connect to devices**:
   - Click "Connect" for each switcher
   - Verify connection status shows device ID

4. **Control channels**:
   - Click "Power ON" to enable the switcher (Turned ON by default)
   - Set flip mode using the toggle switch below each channel
   - Click any channel button to activate it (with or without flip based on toggle)
   - Click the same channel again to deactivate it
   - To switch channels: deactivate current → activate new

5. **Monitor status**:
   - Click "Status" button to view current port states
   - See detailed popup with all channel statuses
   - Check active channels and their modes (Normal/Flip)

## Workflow

### Channel Control Logic
The application implements a **safe switching mechanism**:

1. **Activation**: Click any channel when none are active
2. **Deactivation**: Click the same active channel to turn it off (with confirmation)
3. **Switching**: Must deactivate current channel before activating a new one
4. **Warning System**: Prevents accidental switching with clear error messages
5. **Confirmation Dialog**: Prevents accidental channel disconnection
6. **Toggle Switches**: Each channel has a flip mode toggle for enhanced control

### Toggle Switch Functionality
Each channel button includes a toggle switch that controls **flip mode**:

- **Dynamic Text**: Toggle shows "Normal" or "Flip" based on current state
- **Inactive Channels**: Can toggle freely between Normal/Flip modes
- **Active Channels**: Requires confirmation before mode change
- **Safety Feature**: Active channels disconnect/reconnect to prevent sparking
- **Independent Control**: Each channel's flip mode is set independently
- **Visual Feedback**: Status bar shows flip mode state and operations

### Example Usage Flow
```
1. Power ON switcher
2. Toggle Channel 1 switch → Changes from "Normal" to "Flip"
3. Click "CH 1" → Channel 1 activates (green) with flip mode
4. Try to toggle Channel 1 switch → Confirmation dialog appears
5. Click "Yes" → Channel safely disconnects/reconnects with new mode
6. Click "CH 2" → Warning: "Turn off Channel 1 first"
7. Click "CH 1" → Confirmation dialog for deactivation
8. Click "Yes" → Channel 1 deactivates
9. Toggle Channel 2 switch → Shows "Normal" (no confirmation needed)
10. Click "CH 2" → Channel 2 activates (green) in normal mode
```

## Status Monitoring

The application provides comprehensive status monitoring for all switcher ports:

### Status Button Functionality
- **Location**: Underneath device ID box, stacked vertically and left-aligned
- **Access**: Click "Status" button to query current port states
- **Real-time**: Sends `STATUS\r\n` command to get live data from device

### Status Display Popup
- **Detailed View**: Shows all 6 channels in a dedicated popup window
- **Color Coding**:
  - **Green**: Active channels (Normal or Flip mode)
  - **Gray**: Inactive channels
- **Mode Information**: Distinguishes between Normal and Flip modes
- **Clear Layout**: Easy-to-read channel-by-channel breakdown

### Status Information Shown
- **CH1-CH6**: Individual channel states
- **ACTIVE (Normal)**: Channel is on in normal mode
- **ACTIVE (Flip Mode)**: Channel is on in flip mode
- **INACTIVE**: Channel is off
- **Raw Response**: Fallback display for non-standard responses

## Theme System

The application features a dual-theme system with both color themes and appearance modes:

### Color Themes
- **Classic Theme**: Original green color scheme with teal accents
  - Main colors: Sea Green (#2ecc71) and Forest Green (#27ae60)
  - Accent colors: Teal (#1abc9c) and Darker Teal (#16a085)
  - Professional and familiar appearance

- **Modern Theme**: Purple color scheme inspired by contemporary design
  - Main colors: Purple (#8B5CF6) and Darker Purple (#7C3AED)
  - Accent colors: Light Purple (#A855F7) and Vibrant Purple (#9333EA)
  - Contemporary and stylish appearance

### Appearance Modes
- **Light Mode**: Light backgrounds with dark text
- **Dark Mode**: Dark backgrounds with light text
- **System Mode**: Automatically follows system preference

### Theme Controls
- **Status Bar**: Quick access theme and mode selectors
- **Settings Panel**: Comprehensive theme configuration
- **Persistent Settings**: Theme preferences saved automatically
- **Real-time Updates**: Instant theme switching without restart

## Safety Features

The application includes multiple safety mechanisms to prevent accidental operations:

### Confirmation Dialogs
- **Channel Deactivation**: Confirmation required when turning off an active channel
- **Mode Changes**: Confirmation required when changing flip mode of active channels
- **Clear messaging**: Shows which channel and switcher will be affected
- **Cancel option**: Users can abort the operation if clicked accidentally
- **Toggle Reversion**: If cancelled, toggle switch reverts to previous state

### Warning Systems
- **Channel switching prevention**: Cannot switch directly between channels
- **Connection requirements**: Must be connected and powered before channel operations
- **COM port conflicts**: Prevents multiple switchers from using the same port
- **Cross-switcher conflicts**: Warns if trying to use a channel active on another switcher

### Spark Prevention
- **Safe Mode Changes**: Active channels disconnect/reconnect when changing flip mode
- **Automatic Sequence**: System handles disconnect → mode change → reconnect automatically
- **Status Feedback**: Real-time updates during the safety sequence
- **No Manual Intervention**: User just confirms, system handles the safe switching

### User-Friendly Messages
- **Descriptive titles**: Clear dialog titles like "Confirm Mode Change"
- **Detailed explanations**: Messages explain what will happen
- **Status updates**: Real-time feedback on all operations

## GUI Layout

### Header
- Application title: "EPR Switcher Control"
- Settings button (⚙) for configuration

### Main Area - Adaptive Layout
The layout automatically adapts based on the number of switchers:

- **1 Switcher**: Centered single panel
- **2 Switchers**: Single row, both panels fill width equally
- **3 Switchers**: 2×2 grid with 3 panels (4th position reserved but hidden)
- **4 Switchers**: 2×2 grid layout, fully filling GUI width

Each switcher panel includes:
- Custom name/title
- 6 channel buttons (2 rows × 3 columns)
- 6 flip mode toggle switches (one below each channel button)
- Connection status display with device ID
- Status button (underneath device ID box, stacked and left-aligned)
- Connect/Disconnect button
- Power ON/OFF button

### Status Bar
- Connection status messages
- Color theme selector (Classic/Modern)
- Appearance mode selector (Light/Dark/System)

## Settings Configuration

Access via the ⚙ settings button:

### General Tab
- **Appearance Mode**: Light, Dark, or System theme
- **Color Theme**: Classic (Green) or Modern (Purple)

### Switchers Tab
- **Number of Switchers**: 1-4 devices
- **Custom Names**: Personalize each switcher

### COM Ports Tab
- **Port Assignment**: Map each switcher to a COM port
- **Refresh**: Update available COM ports list
- **Auto-Detection**: Automatic COM port discovery

## Settings Persistence

The application automatically saves and loads your preferences:

### Automatic Features
- **Settings File**: `switcher_settings.yaml` created in the application directory
- **Auto-Save**: Settings saved whenever you make changes
- **Auto-Load**: Previous settings restored on startup
- **Auto-Connect**: Attempts to reconnect to saved COM ports on startup

### What Gets Saved
- Number of switchers (1-4)
- Custom switcher names
- COM port assignments for each switcher
- Selected appearance mode (Light/Dark/System)
- Selected color theme (Classic/Modern)

### What Doesn't Get Saved
- Active channel states (channels always start inactive)
- Connection status (connections must be re-established)
- Power states (switchers start in default power state)

### Auto-Connection Behavior
On startup, the application will:
1. **Auto-connect** to switchers with saved COM ports
2. **Show warning** if saved COM port is unavailable
3. **Silent skip** if no COM port is assigned (empty setting)
4. **Graceful handling** of connection failures

### Settings File Location
- **File**: `switcher_settings.yaml` (same directory as the application)
- **Format**: Human-readable YAML format
- **Auto-creation**: Created automatically if missing
- **Manual editing**: Possible but not recommended

## Serial Communication

### Commands Sent
- `uuid\r\n` - Request device identification
- `Connect <channel> <mode>\r\n` - Activate channel with specified mode
  - `<channel>`: Channel number (1-6)
  - `<mode>`: 0 = Normal mode, 1 = Flip mode
  - Example: `Connect 3 1\r\n` (activate channel 3 in flip mode)
- `disconnect\r\n` - Disconnect all active channels
- `SET_POWER,<state>\r\n` - Power control (1=ON, 0=OFF)
- `STATUS\r\n` - Request current status of all ports

### Expected Status Response
- **Format**: `STATUS:CH1=ON,CH2=OFF,CH3=FLIP,CH4=OFF,CH5=ON,CH6=OFF`
- **States**: `ON` (normal mode), `FLIP` (flip mode), `OFF` (inactive)
- **Fallback**: Application simulates response if device doesn't respond

### Connection Settings
- **Baud Rate**: 9600
- **Timeout**: 1 second
- **Line Ending**: `\r\n`

## File Structure

```
├── usb_switcher_gui.py              # Main application
├── animated_widgets.py              # Custom animated UI components
├── requirements.txt                 # Python dependencies
├── README.md                       # This documentation
├── switcher_settings.yaml          # Settings file (auto-created)
├── switcher_settings_example.yaml  # Example settings file
└── settings_icon.png               # Optional settings icon (fallback: ⚙ text)
```

## Troubleshooting

### Common Issues

**"No COM ports found"**
- Check device connections
- Verify drivers are installed
- Click "Refresh" in COM Ports settings

**"Connection Error"**
- Ensure correct COM port selection
- Check if port is already in use
- Verify device is powered and responsive

**"Channel Conflict"**
- Another switcher is using the same channel
- Check all connected switchers

**Import errors**
- Run: `pip install -r requirements.txt`
- Verify Python version ≥ 3.8

**Settings not saving/loading**
- Check file permissions in application directory
- Verify `switcher_settings.yaml` is not read-only
- Delete corrupted settings file to reset to defaults

**Auto-connect warnings**
- Normal behavior for unavailable COM ports
- Check device connections and drivers
- Update COM port assignments in settings

### Performance Tips
- Close unused switcher connections
- Use "System" theme for better performance
- Limit to necessary number of switchers

## Development

### Key Components
- `USBSwitcherApp`: Main application class
- `SettingsPopup`: Configuration dialog
- `AnimatedButton`: Enhanced UI buttons with animations
- Channel control logic with safety mechanisms

### Customization
- Modify color constants for different themes
- Adjust animation timing in `ANIMATION_DURATION`
- Extend serial commands for additional features

## License

This project is provided as-is for educational and development purposes.

## Support

For issues or questions:
1. Check the troubleshooting section
2. Verify all requirements are met
3. Test with a simple serial device first
