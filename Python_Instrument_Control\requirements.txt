# Core GUI Framework
customtkinter>=5.2.0
tkinter-tooltip>=2.0.0

# Instrument Communication
pyserial>=3.5
pyvisa>=1.13.0
pyvisa-py>=0.7.0

# Data Processing & Analysis
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scipy>=1.10.0

# Configuration & Logging
pyyaml>=6.0
configparser>=5.3.0

# Testing
pytest>=7.4.0
pytest-cov>=4.1.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Development Tools
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: Advanced Instrument Drivers
# pymeasure>=0.13.0
# lantz>=0.5.0
# qcodes>=0.40.0

# Optional: Database Support
# sqlalchemy>=2.0.0
# sqlite3 (built-in)

# Optional: Web Interface
# flask>=2.3.0
# fastapi>=0.100.0

# Build Tools
pyinstaller>=5.13.0
