"""
Create a professional installer package for Serial Monitor
"""
import os
import shutil
import zipfile
import json
from datetime import datetime

def create_installer_package():
    """Create a comprehensive installer package"""
    
    print("Creating Professional Serial Monitor Installer Package...")
    print("=" * 60)
    
    # Create installer directory
    installer_dir = "SerialMonitor_Installer"
    if os.path.exists(installer_dir):
        shutil.rmtree(installer_dir)
    
    os.makedirs(installer_dir)
    
    # Create application directory
    app_dir = os.path.join(installer_dir, "SerialMonitor")
    os.makedirs(app_dir)
    
    # Copy application files
    files_to_copy = [
        ("dist/SerialMonitor.exe", "SerialMonitor.exe"),
        ("serial_monitor_icon.ico", "serial_monitor_icon.ico"),
        ("INSTALLATION_GUIDE.md", "README.md"),
        ("serial_monitor_icon.png", "icon_preview.png")
    ]
    
    print("Copying application files...")
    for src, dst in files_to_copy:
        if os.path.exists(src):
            shutil.copy2(src, os.path.join(app_dir, dst))
            print(f"  [+] {dst}")
        else:
            print(f"  [-] {src} not found")
    
    # Create installer script
    create_installer_script(installer_dir, app_dir)
    
    # Create uninstaller script
    create_uninstaller_script(installer_dir)
    
    # Create desktop shortcut script
    create_shortcut_scripts(installer_dir)
    
    # Create installer info
    create_installer_info(installer_dir)
    
    # Create main installer executable
    create_main_installer(installer_dir)
    
    print(f"\n[+] Professional installer package created: {os.path.abspath(installer_dir)}")
    print("\nInstaller Features:")
    print("  [+] Automatic installation to Program Files")
    print("  [+] Start Menu shortcuts")
    print("  [+] Desktop shortcut creation")
    print("  [+] Proper uninstaller")
    print("  [+] Registry entries")
    print("  [+] Professional appearance")
    
    return installer_dir

def create_installer_script(installer_dir, app_dir):
    """Create the main installer script"""
    
    script_content = '''@echo off
title Serial Monitor Installer
color 0A
echo.
echo ===============================================
echo    Serial Monitor Professional Installer
echo ===============================================
echo.
echo Installing Serial Monitor...
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo.
    echo ERROR: This installer requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Create installation directory
set "INSTALL_DIR=%ProgramFiles%\\Serial Monitor"
echo Creating installation directory: %INSTALL_DIR%
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying application files...
xcopy /Y /Q "SerialMonitor\\*.*" "%INSTALL_DIR%\\"
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy files
    pause
    exit /b 1
)

REM Create Start Menu shortcuts
echo Creating Start Menu shortcuts...
set "START_MENU=%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs"
if not exist "%START_MENU%\\Serial Monitor" mkdir "%START_MENU%\\Serial Monitor"

REM Create main shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\Serial Monitor\\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\serial_monitor_icon.ico'; $Shortcut.Save()"

REM Create uninstaller shortcut
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\\Serial Monitor\\Uninstall Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\uninstall.bat'; $Shortcut.Save()"

REM Copy uninstaller
copy /Y "uninstall.bat" "%INSTALL_DIR%\\uninstall.bat"

REM Add to registry (for Add/Remove Programs)
echo Adding registry entries...
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /v "DisplayName" /t REG_SZ /d "Serial Monitor" /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\\uninstall.bat" /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\\serial_monitor_icon.ico" /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /v "Publisher" /t REG_SZ /d "Serial Monitor Team" /f
reg add "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /v "DisplayVersion" /t REG_SZ /d "1.0" /f

echo.
echo ===============================================
echo    Installation completed successfully!
echo ===============================================
echo.
echo Serial Monitor has been installed to:
echo %INSTALL_DIR%
echo.
echo You can find it in:
echo - Start Menu ^> Serial Monitor
echo - Or run: "%INSTALL_DIR%\\SerialMonitor.exe"
echo.

REM Ask to create desktop shortcut
set /p desktop="Create desktop shortcut? (Y/N): "
if /i "%desktop%"=="Y" (
    echo Creating desktop shortcut...
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\\Desktop\\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\serial_monitor_icon.ico'; $Shortcut.Save()"
    echo Desktop shortcut created!
)

REM Ask to launch application
echo.
set /p launch="Launch Serial Monitor now? (Y/N): "
if /i "%launch%"=="Y" (
    start "" "%INSTALL_DIR%\\SerialMonitor.exe"
)

echo.
echo Installation complete! Press any key to exit.
pause >nul
'''
    
    with open(os.path.join(installer_dir, "install.bat"), "w", encoding='utf-8') as f:
        f.write(script_content)

def create_uninstaller_script(installer_dir):
    """Create the uninstaller script"""
    
    uninstall_content = '''@echo off
title Serial Monitor Uninstaller
color 0C
echo.
echo ===============================================
echo    Serial Monitor Uninstaller
echo ===============================================
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running with administrator privileges...
) else (
    echo.
    echo ERROR: This uninstaller requires administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

set "INSTALL_DIR=%ProgramFiles%\\Serial Monitor"

echo Uninstalling Serial Monitor...
echo.

REM Remove Start Menu shortcuts
echo Removing Start Menu shortcuts...
if exist "%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs\\Serial Monitor" (
    rmdir /S /Q "%ProgramData%\\Microsoft\\Windows\\Start Menu\\Programs\\Serial Monitor"
)

REM Remove desktop shortcut
echo Removing desktop shortcut...
if exist "%PUBLIC%\\Desktop\\Serial Monitor.lnk" (
    del /Q "%PUBLIC%\\Desktop\\Serial Monitor.lnk"
)

REM Remove registry entries
echo Removing registry entries...
reg delete "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\SerialMonitor" /f >nul 2>&1

REM Remove installation directory
echo Removing installation files...
if exist "%INSTALL_DIR%" (
    rmdir /S /Q "%INSTALL_DIR%"
)

echo.
echo ===============================================
echo    Uninstallation completed successfully!
echo ===============================================
echo.
echo Serial Monitor has been completely removed from your system.
echo.
echo Press any key to exit.
pause >nul
'''
    
    with open(os.path.join(installer_dir, "uninstall.bat"), "w", encoding='utf-8') as f:
        f.write(uninstall_content)

def create_shortcut_scripts(installer_dir):
    """Create shortcut creation scripts"""
    
    # Desktop shortcut creator
    desktop_script = '''@echo off
echo Creating desktop shortcut for Serial Monitor...
set "INSTALL_DIR=%ProgramFiles%\\Serial Monitor"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%PUBLIC%\\Desktop\\Serial Monitor.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\\SerialMonitor.exe'; $Shortcut.IconLocation = '%INSTALL_DIR%\\serial_monitor_icon.ico'; $Shortcut.Save()"
echo Desktop shortcut created!
pause
'''
    
    with open(os.path.join(installer_dir, "create_desktop_shortcut.bat"), "w", encoding='utf-8') as f:
        f.write(desktop_script)

def create_installer_info(installer_dir):
    """Create installer information files"""
    
    # README for installer
    readme_content = """# Serial Monitor - Professional Installer

## Installation Instructions

### Method 1: Automatic Installation (Recommended)
1. **Right-click** on `install.bat`
2. Select **"Run as administrator"**
3. Follow the on-screen prompts
4. Choose whether to create desktop shortcut
5. Choose whether to launch the application

### Method 2: Manual Installation
1. Copy the `SerialMonitor` folder to `C:\\Program Files\\`
2. Run `create_desktop_shortcut.bat` if you want a desktop shortcut
3. Navigate to the installation folder and run `SerialMonitor.exe`

## What Gets Installed

- **Application**: `C:\\Program Files\\Serial Monitor\\SerialMonitor.exe`
- **Start Menu**: Start Menu > Serial Monitor
- **Desktop Shortcut**: Optional during installation
- **Uninstaller**: Available in Start Menu and Add/Remove Programs

## Features

✓ Professional Windows installer
✓ Start Menu integration
✓ Desktop shortcut (optional)
✓ Proper uninstaller
✓ Registry entries for Add/Remove Programs
✓ Administrator privilege handling
✓ Custom application icon

## Uninstallation

### Method 1: Start Menu
- Go to Start Menu > Serial Monitor > Uninstall Serial Monitor

### Method 2: Control Panel
- Go to Control Panel > Programs > Programs and Features
- Find "Serial Monitor" and click Uninstall

### Method 3: Manual
- Run the uninstaller from: `C:\\Program Files\\Serial Monitor\\uninstall.bat`

## System Requirements

- Windows 10/11 (64-bit)
- Administrator privileges for installation
- 50MB free disk space

## Troubleshooting

**Installation fails:**
- Ensure you're running as administrator
- Check available disk space
- Temporarily disable antivirus

**Application won't start:**
- Check if all files were copied correctly
- Verify Windows version compatibility
- Install Visual C++ Redistributable if needed

## Support

For support and updates, please refer to the application documentation.

---
Serial Monitor v1.0 - Professional Serial Communication Tool
"""
    
    with open(os.path.join(installer_dir, "README_INSTALLER.md"), "w", encoding='utf-8') as f:
        f.write(readme_content)

def create_main_installer(installer_dir):
    """Create the main installer executable script"""
    
    main_installer = '''@echo off
title Serial Monitor Setup
color 0B
cls
echo.
echo     ███████╗███████╗██████╗ ██╗ █████╗ ██╗         
echo     ██╔════╝██╔════╝██╔══██╗██║██╔══██╗██║         
echo     ███████╗█████╗  ██████╔╝██║███████║██║         
echo     ╚════██║██╔══╝  ██╔══██╗██║██╔══██║██║         
echo     ███████║███████╗██║  ██║██║██║  ██║███████╗    
echo     ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝╚═╝  ╚═╝╚══════╝    
echo.
echo     ███╗   ███╗ ██████╗ ███╗   ██╗██╗████████╗ ██████╗ ██████╗ 
echo     ████╗ ████║██╔═══██╗████╗  ██║██║╚══██╔══╝██╔═══██╗██╔══██╗
echo     ██╔████╔██║██║   ██║██╔██╗ ██║██║   ██║   ██║   ██║██████╔╝
echo     ██║╚██╔╝██║██║   ██║██║╚██╗██║██║   ██║   ██║   ██║██╔══██╗
echo     ██║ ╚═╝ ██║╚██████╔╝██║ ╚████║██║   ██║   ╚██████╔╝██║  ██║
echo     ╚═╝     ╚═╝ ╚═════╝ ╚═╝  ╚═══╝╚═╝   ╚═╝    ╚═════╝ ╚═╝  ╚═╝
echo.
echo ===============================================================================
echo                        Professional Serial Communication Tool
echo                                    Version 1.0
echo ===============================================================================
echo.
echo Welcome to the Serial Monitor Setup Wizard!
echo.
echo This will install Serial Monitor on your computer.
echo.
echo Features:
echo   • Beautiful purple-themed interface
echo   • COM port auto-detection
echo   • Multiple baud rate support
echo   • Real-time serial communication
echo   • Professional vector icons
echo   • Arduino IDE-style serial monitor
echo.
echo ===============================================================================
echo.
echo IMPORTANT: This installer requires administrator privileges.
echo.
pause

REM Check if running as admin
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✓ Administrator privileges confirmed
    echo.
    echo Starting installation...
    timeout /t 2 >nul
    call install.bat
) else (
    echo.
    echo ⚠ ERROR: Administrator privileges required!
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    echo Press any key to exit...
    pause >nul
)
'''
    
    with open(os.path.join(installer_dir, "Setup.bat"), "w", encoding='utf-8') as f:
        f.write(main_installer)

def main():
    """Main function"""
    try:
        installer_dir = create_installer_package()
        
        print("\n" + "=" * 60)
        print("PROFESSIONAL INSTALLER PACKAGE CREATED SUCCESSFULLY!")
        print("=" * 60)
        print(f"\nLocation: {os.path.abspath(installer_dir)}")
        print("\nTo install Serial Monitor:")
        print("1. Navigate to the installer folder")
        print("2. Right-click 'Setup.bat'")
        print("3. Select 'Run as administrator'")
        print("4. Follow the installation wizard")
        print("\nThe installer will:")
        print("[+] Install to Program Files")
        print("[+] Create Start Menu shortcuts")
        print("[+] Add to Add/Remove Programs")
        print("[+] Create desktop shortcut (optional)")
        print("[+] Provide proper uninstaller")
        
        return True
        
    except Exception as e:
        print(f"Error creating installer: {e}")
        return False

if __name__ == "__main__":
    main()
