"""
Installation Test Script
Lab Instrument Control System

This script tests the installation and verifies that all components are working correctly.
"""

import sys
import os
from pathlib import Path

def test_python_version():
    """Test Python version"""
    print("Testing Python version...")
    
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✓ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False


def test_dependencies():
    """Test required dependencies"""
    print("\nTesting dependencies...")
    
    dependencies = [
        ('pyvisa', 'PyVISA'),
        ('customtkinter', 'CustomTkinter'),
        ('yaml', 'PyYAML'),
        ('numpy', 'NumPy'),
        ('pandas', 'Pandas'),
        ('matplotlib', 'Matplotlib')
    ]
    
    all_ok = True
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✓ {name} - OK")
        except ImportError:
            print(f"✗ {name} - Missing")
            all_ok = False
    
    return all_ok


def test_visa_backends():
    """Test VISA backends"""
    print("\nTesting VISA backends...")
    
    try:
        import pyvisa
        
        # Test NI-VISA backend
        try:
            rm_ni = pyvisa.ResourceManager()
            print("✓ NI-VISA backend - Available")
            rm_ni.close()
            ni_available = True
        except Exception as e:
            print(f"✗ NI-VISA backend - Not available ({e})")
            ni_available = False
        
        # Test pyvisa-py backend
        try:
            rm_py = pyvisa.ResourceManager('@py')
            print("✓ pyvisa-py backend - Available")
            rm_py.close()
            py_available = True
        except Exception as e:
            print(f"✗ pyvisa-py backend - Not available ({e})")
            py_available = False
        
        return ni_available or py_available
        
    except ImportError:
        print("✗ PyVISA not installed")
        return False


def test_project_structure():
    """Test project structure"""
    print("\nTesting project structure...")
    
    required_paths = [
        'src',
        'src/instruments',
        'src/gui',
        'src/utils',
        'config',
        'docs',
        'examples',
        'requirements.txt'
    ]
    
    all_ok = True
    
    for path in required_paths:
        if os.path.exists(path):
            print(f"✓ {path} - OK")
        else:
            print(f"✗ {path} - Missing")
            all_ok = False
    
    return all_ok


def test_instrument_imports():
    """Test instrument driver imports"""
    print("\nTesting instrument driver imports...")
    
    # Add src to path
    sys.path.insert(0, 'src')
    
    instruments = [
        ('instruments.base_instrument', 'BaseInstrument'),
        ('instruments.tektronix_mso58b', 'TektronixMSO58B'),
        ('instruments.agilent_e3631a', 'AgilentE3631A'),
        ('instruments.agilent_34401a', 'Agilent34401A'),
        ('instruments.chroma_62006p', 'Chroma62006P'),
        ('instruments.rigol_dp821a', 'RigolDP821A'),
        ('instruments.hp_6063b', 'HP6063B')
    ]
    
    all_ok = True
    
    for module, class_name in instruments:
        try:
            mod = __import__(module, fromlist=[class_name])
            cls = getattr(mod, class_name)
            print(f"✓ {class_name} - OK")
        except Exception as e:
            print(f"✗ {class_name} - Error: {e}")
            all_ok = False
    
    return all_ok


def test_gui_import():
    """Test GUI imports"""
    print("\nTesting GUI imports...")
    
    # Add src to path
    sys.path.insert(0, 'src')
    
    try:
        from gui.main_window import InstrumentControlGUI
        print("✓ Main GUI - OK")
        return True
    except Exception as e:
        print(f"✗ Main GUI - Error: {e}")
        return False


def test_config_files():
    """Test configuration files"""
    print("\nTesting configuration files...")
    
    config_files = [
        'config/config.yaml',
        'config/instruments.yaml'
    ]
    
    all_ok = True
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                import yaml
                with open(config_file, 'r') as f:
                    yaml.safe_load(f)
                print(f"✓ {config_file} - OK")
            except Exception as e:
                print(f"✗ {config_file} - Invalid YAML: {e}")
                all_ok = False
        else:
            print(f"✗ {config_file} - Missing")
            all_ok = False
    
    return all_ok


def test_visa_manager():
    """Test VISA manager"""
    print("\nTesting VISA manager...")
    
    # Add src to path
    sys.path.insert(0, 'src')
    
    try:
        from utils.visa_manager import VISAManager
        
        # Create VISA manager instance
        vm = VISAManager()
        
        # Test resource scanning
        resources = vm.scan_resources()
        print(f"✓ VISA Manager - OK (found {len(resources)} resources)")
        
        vm.disconnect_all()
        return True
        
    except Exception as e:
        print(f"✗ VISA Manager - Error: {e}")
        return False


def main():
    """Main test function"""
    
    print("="*60)
    print("Lab Instrument Control System - Installation Test")
    print("="*60)
    
    tests = [
        ("Python Version", test_python_version),
        ("Dependencies", test_dependencies),
        ("VISA Backends", test_visa_backends),
        ("Project Structure", test_project_structure),
        ("Instrument Drivers", test_instrument_imports),
        ("GUI Components", test_gui_import),
        ("Configuration Files", test_config_files),
        ("VISA Manager", test_visa_manager)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} - Exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Installation is ready.")
        print("\nYou can now run the application with:")
        print("  python src/main_app.py")
        print("  or")
        print("  Quick_Start.bat")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        print("\nTo install missing dependencies:")
        print("  pip install -r requirements.txt")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        sys.exit(1)
