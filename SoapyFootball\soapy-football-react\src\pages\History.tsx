import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '../utils/supabaseClient';
import { useAuth } from '../context/AuthContext';
import { Booking } from '../types';

const History = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      navigate('/auth');
      return;
    }

    const fetchBookings = async () => {
      try {
        const { data, error } = await supabase
          .from('bookings')
          .select('*')
          .eq('user_id', user.id)
          .order('date', { ascending: false });

        if (error) throw error;

        setBookings(data || []);
      } catch (error) {
        console.error('Error fetching bookings:', error);
        setError('Failed to load booking history. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchBookings();
  }, [user, navigate]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">Paid</span>;
      case 'pending':
        return <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">Pending</span>;
      case 'failed':
        return <span className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs font-medium">Failed</span>;
      default:
        return <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-xs font-medium">{status}</span>;
    }
  };

  const isUpcoming = (date: string) => {
    const bookingDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return bookingDate >= today;
  };

  // Group bookings into upcoming and past
  const upcomingBookings = bookings.filter(booking => isUpcoming(booking.date));
  const pastBookings = bookings.filter(booking => !isUpcoming(booking.date));

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-8">
            <h1 className="text-3xl font-bold text-dark">Booking History</h1>
            <div className="space-x-4">
              <button 
                onClick={() => navigate('/')}
                className="btn bg-gray-200 hover:bg-gray-300 text-gray-800"
              >
                Home
              </button>
              <button 
                onClick={() => navigate('/booking')}
                className="btn bg-gray-200 hover:bg-gray-300 text-gray-800"
              >
                Book Again
              </button>
            </div>
          </div>
          
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              {error}
            </div>
          )}
          
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Upcoming Bookings */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Upcoming Bookings</h2>
                
                {upcomingBookings.length === 0 ? (
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-gray-500">You don't have any upcoming bookings.</p>
                    <button 
                      onClick={() => navigate('/booking')}
                      className="mt-4 btn btn-primary"
                    >
                      Book Now
                    </button>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <ul className="divide-y divide-gray-200">
                      {upcomingBookings.map((booking) => (
                        <li key={booking.id} className="p-6">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                            <div>
                              <div className="flex items-center mb-2">
                                <span className="font-semibold text-lg">{booking.date}</span>
                                <span className="mx-2 text-gray-400">•</span>
                                <span>{booking.slot}</span>
                              </div>
                              <p className="text-gray-600 mb-2">
                                <span className="font-medium">{booking.name}</span> • {booking.phone}
                              </p>
                              <div className="flex items-center">
                                <span className="mr-2">Status:</span>
                                {getStatusBadge(booking.payment_status)}
                              </div>
                            </div>
                            
                            <div className="mt-4 sm:mt-0">
                              {booking.payment_status === 'pending' && (
                                <button
                                  onClick={() => navigate(`/payment/${booking.id}`)}
                                  className="btn btn-primary"
                                >
                                  Complete Payment
                                </button>
                              )}
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
              
              {/* Past Bookings */}
              <div>
                <h2 className="text-xl font-semibold mb-4">Past Bookings</h2>
                
                {pastBookings.length === 0 ? (
                  <div className="bg-white rounded-lg shadow-md p-6 text-center">
                    <p className="text-gray-500">You don't have any past bookings.</p>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg shadow-md overflow-hidden">
                    <ul className="divide-y divide-gray-200">
                      {pastBookings.map((booking) => (
                        <li key={booking.id} className="p-6">
                          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                            <div>
                              <div className="flex items-center mb-2">
                                <span className="font-semibold text-lg">{booking.date}</span>
                                <span className="mx-2 text-gray-400">•</span>
                                <span>{booking.slot}</span>
                              </div>
                              <p className="text-gray-600 mb-2">
                                <span className="font-medium">{booking.name}</span> • {booking.phone}
                              </p>
                              <div className="flex items-center">
                                <span className="mr-2">Status:</span>
                                {getStatusBadge(booking.payment_status)}
                              </div>
                            </div>
                            
                            <div className="mt-4 sm:mt-0">
                              <span className="text-lg font-semibold text-primary">₹{booking.amount}</span>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default History;
