# Serial Monitor - Distribution Package

## Package Contents

This package contains multiple installation options for Serial Monitor:

### 1. Professional Installer (Recommended)
**Location**: `SerialMonitor_Installer/`

**Installation**:
1. Extract this ZIP file
2. Navigate to `SerialMonitor_Installer` folder
3. Right-click `Setup.bat`
4. Select "Run as administrator"
5. Follow the installation wizard

**Features**:
- Installs to Program Files
- Creates Start Menu shortcuts
- Adds to Add/Remove Programs
- Optional desktop shortcut
- Proper uninstaller

### 2. Standalone Executable
**Location**: `Standalone/SerialMonitor.exe`

**Usage**:
- No installation required
- Just run the exe file
- Portable - can run from USB drive
- Perfect for temporary use

### 3. Documentation
**Location**: `Documentation/`

Contains:
- Installation guide
- Application icon
- User documentation

## System Requirements

- Windows 10/11 (64-bit)
- Administrator privileges (for installer)
- 50MB free disk space
- COM port access permissions

## Application Features

- **Beautiful Purple Theme**: Modern dark interface
- **COM Port Auto-Detection**: Finds available ports automatically
- **Multiple Baud Rates**: 9600 to 921600 baud support
- **Real-time Communication**: Send/receive serial data
- **Arduino IDE Style**: Familiar serial monitor interface
- **Professional Vector Icons**: Custom serial port icons
- **Clean Interface**: White status text for visibility

## Quick Start

1. **For permanent installation**: Use the Professional Installer
2. **For quick testing**: Use the Standalone executable
3. **Connect your device**: Select COM port and baud rate
4. **Start communicating**: Send commands and monitor responses

## Support

- Check the documentation folder for detailed guides
- Ensure device drivers are installed
- Run as administrator if COM port access fails

---
Serial Monitor v1.0 - Professional Serial Communication Tool
Created: 2025-06-02
