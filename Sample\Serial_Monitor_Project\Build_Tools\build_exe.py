"""
Build script for creating standalone Serial Monitor executable
"""
import os
import subprocess
import sys

def install_pyinstaller():
    """Install PyInstaller if not already installed"""
    try:
        import PyInstaller
        print("PyInstaller is already installed")
    except ImportError:
        print("Installing PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

def build_executable():
    """Build the standalone executable"""
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Create a single executable file
        "--windowed",                   # Hide console window
        "--name=SerialMonitor",         # Name of the executable
        "--icon=serial_monitor_icon.ico",  # Application icon
        "--add-data=purple_theme.json;.",  # Include theme file
        "--add-data=serial_monitor_icon.ico;.",  # Include icon file
        "--distpath=dist",              # Output directory
        "--workpath=build",             # Build directory
        "--specpath=.",                 # Spec file location
        "serial_monitor_gui.py"         # Main Python file
    ]
    
    print("Building executable...")
    print("Command:", " ".join(cmd))
    
    try:
        subprocess.check_call(cmd)
        print("\n" + "="*50)
        print("BUILD SUCCESSFUL!")
        print("="*50)
        print(f"Executable created: {os.path.abspath('dist/SerialMonitor.exe')}")
        print("\nYou can now distribute this exe file to any Windows PC!")
        print("The exe file includes all dependencies and can run without Python installed.")
        
    except subprocess.CalledProcessError as e:
        print(f"Build failed with error: {e}")
        return False
    
    return True

def main():
    print("Serial Monitor - Executable Builder")
    print("="*40)
    
    # Check if required files exist
    required_files = [
        "serial_monitor_gui.py",
        "purple_theme.json", 
        "serial_monitor_icon.ico"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    if missing_files:
        print(f"Error: Missing required files: {missing_files}")
        return
    
    # Install PyInstaller
    install_pyinstaller()
    
    # Build executable
    if build_executable():
        print("\nBuild completed successfully!")
        
        # Show file size
        exe_path = "dist/SerialMonitor.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"Executable size: {size_mb:.1f} MB")
    else:
        print("Build failed!")

if __name__ == "__main__":
    main()
