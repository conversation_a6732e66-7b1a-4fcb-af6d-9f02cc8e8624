#include <BleCombo.h>
#include <WiFi.h>
#include <WebServer.h>
#include <EEPROM.h>

#define EEPROM_SIZE    128
#define USER_ADDR      0
#define PASS_ADDR      64

#define LOCK_PIN       12
#define UNLOCK_PIN     14

#define HOL<PERSON>_MS        2000
#define DEBOUNCE_MS    250
#define DBLCLICK_MS    500

//BleCombo Keyboard;
WebServer server(80);

bool configMode = false;
unsigned long lockStart, lastLock, lastUnlock;
bool clickOnce = false;

void setup() {
  Serial.begin(115200);
  pinMode(LOCK_PIN, INPUT_PULLUP);
  pinMode(UNLOCK_PIN, INPUT_PULLUP);
  EEPROM.begin(EEPROM_SIZE);

  if (digitalRead(LOCK_PIN) == LOW) {
    delay(50);
    lockStart = millis();
    while (digitalRead(LOCK_PIN) == LOW)
      if (millis() - lockStart > HOLD_MS) { configMode = true; break; }
  }

  if (configMode) startAP();
  else {
    //Keyboard.setDeviceName("Dockei v1.0");
    Keyboard.begin();
  }
}

void loop() {
  if (configMode) {
    server.handleClient();
    return;
  }
  if (!Keyboard.isConnected()) return;

  // LOCK button
  if (digitalRead(LOCK_PIN) == LOW && millis() - lastLock > DEBOUNCE_MS) {
    lastLock = millis();
    Serial.println("Lock button pressed");
    Keyboard.press(KEY_LEFT_GUI); Keyboard.press('l'); delay(100); Keyboard.releaseAll();
  }

  // UNLOCK button
  static unsigned long unlockHoldStart = 0;

  if (digitalRead(UNLOCK_PIN) == LOW) {
    if (unlockHoldStart == 0) {
      unlockHoldStart = millis();
    }

    // If held for 2 seconds, input only password
    if (millis() - unlockHoldStart >= 2000) {
      Serial.println("Hold unlock for 2s: Password only");
      Keyboard.print(readEEPROM(PASS_ADDR, 64));
      Keyboard.write(KEY_RETURN);
      while (digitalRead(UNLOCK_PIN) == LOW); // Wait for release
      unlockHoldStart = 0;
      clickOnce = false;  // Cancel click sequence
      return;
    }
  } else {
    // Tap logic
    if (unlockHoldStart != 0) {
      if (!clickOnce && millis() - unlockHoldStart < DBLCLICK_MS) {
        clickOnce = true;
        lastUnlock = millis();
      } else if (clickOnce && millis() - lastUnlock < DBLCLICK_MS) {
        clickOnce = false;
        Serial.println("Double-click unlock");
        unlockFull();
      }
      unlockHoldStart = 0;
    }
  }

  if (clickOnce && millis() - lastUnlock >= DBLCLICK_MS) {
    clickOnce = false;
    Serial.println("Single-click unlock");
    unlockSimple();
  }

  delay(10);
}

// ========== Unlock Routines ==========

void sendCtrlAltDel() {
  Keyboard.press(KEY_LEFT_CTRL);
  Keyboard.press(KEY_LEFT_ALT);
  Keyboard.press(KEY_DELETE);
  delay(100);
  Keyboard.releaseAll();
}

void unlockSimple() {
  sendCtrlAltDel();
  delay(1000);
  Keyboard.print(readEEPROM(PASS_ADDR, 64));
  Keyboard.write(KEY_RETURN);
}

void unlockFull() {
  sendCtrlAltDel();
  delay(1000);
  Keyboard.print(readEEPROM(USER_ADDR, 64));
  Keyboard.write(KEY_TAB);
  delay(200);
  Keyboard.print(readEEPROM(PASS_ADDR, 64));
  Keyboard.write(KEY_RETURN);
}

// ========== EEPROM ==========

String readEEPROM(int start, int len) {
  String val = "";
  for (int i = 0; i < len; i++) {
    char c = EEPROM.read(start + i);
    if (c == 0) break;
    val += c;
  }
  return val;
}

void writeEEPROM(int addr, String data) {
  for (int i = 0; i < 64; i++) {
    EEPROM.write(addr + i, i < data.length() ? data[i] : 0);
  }
  EEPROM.commit();
}

// ========== SoftAP Webserver ==========

void startAP() {
  WiFi.softAP("Dockei-Config", "12345678");
  server.on("/", HTTP_GET, []() { server.send(200, "text/html", getWebPage()); });
  server.on("/save", HTTP_POST, handleSave);
  server.on("/reboot", HTTP_POST, []() {
    server.send(200, "text/plain", "Rebooting...");
    delay(1000);
    ESP.restart();
  });
  server.begin();
  Serial.println("SoftAP started: http://192.168.4.1");
}

void handleSave() {
  String user = server.arg("username");
  String pass = server.arg("password");
  writeEEPROM(USER_ADDR, user);
  writeEEPROM(PASS_ADDR, pass);
  server.send(200, "text/plain", "Saved");
}

String getWebPage() {
  return R"rawliteral(
<!DOCTYPE html>
<html>
<head>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Dockei Config</title>
  <style>
    * { box-sizing: border-box; }
    body {
      margin: 0; font-family: 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #8e2de2, #ff6ec4);
      display: flex; justify-content: center; align-items: center;
      height: 100vh;
    }
    .card {
      background: white; padding: 2em; border-radius: 2em;
      box-shadow: 0 10px 25px rgba(0,0,0,0.2);
      width: 90%; max-width: 400px;
    }
    h2 {
      text-align: center; color: #8e2de2;
    }
    input, button {
      display: block;
      width: 100%;
      padding: 0.75em;
      margin-top: 1em;
      border: none;
      border-radius: 1em;
    }
    input {
      background: #f0f0f0;
    }
    button {
      background: #8e2de2;
      color: white;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.3s ease;
    }
    button:hover {
      background: #a149ec;
    }
    #status {
      margin-top: 1em;
      font-size: 0.9em;
      color: green;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="card">
    <h2>Dockei Configuration</h2>
    <input type="text" id="username" placeholder="Enter Username" required>
    <input type="password" id="password" placeholder="Enter Password" required>
    <button onclick="save()">Save</button>
    <button onclick="reboot()">Reboot to Bluetooth</button>
    <div id="status"></div>
  </div>
  <script>
    function save() {
      const u = document.getElementById('username').value;
      const p = document.getElementById('password').value;
      fetch("/save", {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: "username=" + encodeURIComponent(u) + "&password=" + encodeURIComponent(p)
      }).then(response => response.text()).then(text => {
        document.getElementById("status").innerText = "Credentials saved!";
      });
    }
    function reboot() {
      fetch("/reboot", { method: "POST" });
    }
  </script>
</body>
</html>
)rawliteral";
}
